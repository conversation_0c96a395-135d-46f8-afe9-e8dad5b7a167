import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Plus, 
  Eye, 
  Flag, 
  CheckCircle, 
  XCircle, 
  FileText,
  Building,
  Calendar,
  DollarSign,
  Truck,
  AlertTriangle
} from "lucide-react";
import {
  useGetRFQResponsesQuery,
} from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";
import AddRFQResponse from "../modals/AddRFQResponse";
import RFQResponseComparison from "./RFQResponseComparison";

interface RFQResponsesViewProps {
  rfqId: number;
}

const RFQResponsesView = ({ rfqId }: RFQResponsesViewProps) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [viewMode, setViewMode] = useState<"list" | "comparison">("list");

  const { data: responsesData, isLoading } = useGetRFQResponsesQuery({ rfq: rfqId });

  const responses = responsesData?.data?.results || [];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Received": { variant: "secondary" as const, className: "bg-green-100 text-green-800" },
      "Pending": { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800" },
      "Disqualified": { variant: "secondary" as const, className: "bg-red-100 text-red-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Pending"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  const getCompletenessBadge = (isComplete: boolean, hasDiscrepancies: boolean) => {
    if (hasDiscrepancies) {
      return (
        <Badge variant="secondary" className="bg-red-100 text-red-800 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          Has Issues
        </Badge>
      );
    }
    
    if (isComplete) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Complete
        </Badge>
      );
    }
    
    return (
      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 flex items-center gap-1">
        <XCircle className="h-3 w-3" />
        Incomplete
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading responses...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">RFQ Responses</h2>
          <p className="text-gray-600">
            {responses.length} response(s) received
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={viewMode} onValueChange={(value: "list" | "comparison") => setViewMode(value)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="list">List View</SelectItem>
              <SelectItem value="comparison">Comparison View</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Response
          </Button>
        </div>
      </div>

      {viewMode === "list" ? (
        <div className="space-y-4">
          {responses.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Responses Yet</h3>
                <p className="text-gray-600 mb-4">
                  No suppliers have responded to this RFQ yet.
                </p>
                <Button onClick={() => setShowAddModal(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Response
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {responses.map((response: any) => (
                <Card key={response.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Building className="h-5 w-5" />
                          {response.supplier_name}
                        </CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          {getStatusBadge(response.status)}
                          {getCompletenessBadge(response.is_complete, response.has_discrepancies)}
                        </div>
                      </div>
                      <div className="text-right text-sm text-gray-500">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(response.submitted_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Key Information */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Total Value:</span>
                        <div className="font-medium flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          {response.currency} {response.total_value?.toLocaleString() || "N/A"}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Delivery Time:</span>
                        <div className="font-medium flex items-center gap-1">
                          <Truck className="h-3 w-3" />
                          {response.delivery_time_days ? `${response.delivery_time_days} days` : "N/A"}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Payment Terms:</span>
                        <div className="font-medium">{response.payment_terms || "N/A"}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Items:</span>
                        <div className="font-medium">{response.items?.length || 0} item(s)</div>
                      </div>
                    </div>

                    {/* Document */}
                    {response.document_url && (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{response.document_name}</span>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={response.document_url} target="_blank" rel="noopener noreferrer">
                            <Eye className="h-3 w-3" />
                          </a>
                        </Button>
                      </div>
                    )}

                    {/* Discrepancy Notes */}
                    {response.has_discrepancies && response.discrepancy_notes && (
                      <div className="p-2 bg-red-50 border border-red-200 rounded">
                        <div className="flex items-center gap-1 text-red-800 text-sm font-medium mb-1">
                          <AlertTriangle className="h-3 w-3" />
                          Issues Identified
                        </div>
                        <p className="text-red-700 text-sm">{response.discrepancy_notes}</p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex justify-end items-center pt-2 border-t">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      ) : (
        <RFQResponseComparison responses={responses} />
      )}

      {/* Add Response Modal */}
      <AddRFQResponse
        open={showAddModal}
        onClose={() => setShowAddModal(false)}
        rfqId={rfqId}
      />
    </div>
  );
};

export default RFQResponsesView;
