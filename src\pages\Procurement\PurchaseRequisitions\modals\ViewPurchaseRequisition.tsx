import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { PurchaseRequisition } from "@/types/procurement";

interface ViewPurchaseRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  requisition: PurchaseRequisition | null;
}

const ViewPurchaseRequisition: React.FC<ViewPurchaseRequisitionProps> = ({
  isOpen,
  onClose,
  requisition,
}) => {
  if (!requisition) return null;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { 
        variant: "secondary" as const, 
        color: "bg-gray-100 text-gray-800", 
        icon: "📝",
        description: "In preparation"
      },
      Submitted: { 
        variant: "default" as const, 
        color: "bg-blue-100 text-blue-800", 
        icon: "📤",
        description: "Awaiting approval"
      },
      Approved: { 
        variant: "default" as const, 
        color: "bg-green-100 text-green-800", 
        icon: "✅",
        description: "Ready for RFQ"
      },
      Rejected: { 
        variant: "destructive" as const, 
        color: "bg-red-100 text-red-800", 
        icon: "❌",
        description: "Needs revision"
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
        <span>{config.icon}</span>
        {status}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Purchase Requisition Details</span>
            {getStatusBadge(requisition.status || "Draft")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Purchase Requisition Code</label>
                <p className="text-lg font-semibold text-gray-900">
                  {requisition.code || `PR-${String(requisition.id).padStart(6, '0')}`}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Created By</label>
                <p className="text-gray-900">
                  {requisition.created_by || "Not specified"}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Source Store Requisition</label>
                <p className="text-gray-900">
                  {requisition.store_requisition ? (
                    <span className="text-blue-600 font-medium">
                      {requisition.store_requisition}
                    </span>
                  ) : (
                    "Direct Creation"
                  )}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">Status</label>
                <div className="flex flex-col gap-1">
                  {getStatusBadge(requisition.status || "Draft")}
                  <span className="text-xs text-gray-500">
                    {requisition.status === "Draft" && "In preparation"}
                    {requisition.status === "Submitted" && "Awaiting approval"}
                    {requisition.status === "Approved" && "Ready for RFQ"}
                    {requisition.status === "Rejected" && "Needs revision"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Items Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Purchase Items ({requisition.purchase_items?.length || 0})
            </h3>
            
            {requisition.purchase_items && requisition.purchase_items.length > 0 ? (
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit of Measure
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {requisition.purchase_items.map((item, index) => (
                      <tr key={item.id || index}>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex flex-col">
                            <span className="text-sm font-medium text-gray-900">
                              {item.product || "Not specified"}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            {parseFloat(item.quantity || "0").toFixed(2)}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            Unit ID: {item.unit_of_measure}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No items found
              </div>
            )}
          </div>

          {/* Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Items:</span>
                <span className="ml-2 font-medium">{requisition.purchase_items?.length || 0}</span>
              </div>
              <div>
                <span className="text-gray-600">Total Quantity:</span>
                <span className="ml-2 font-medium">
                  {requisition.purchase_items?.reduce((sum, item) => 
                    sum + parseFloat(item.quantity || "0"), 0
                  ).toFixed(2) || "0.00"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewPurchaseRequisition;
