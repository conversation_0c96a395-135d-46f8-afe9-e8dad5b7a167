import { Link } from "react-router-dom";
import Error404Img from '@/assets/404 Error Page not Found with people connecting a plug-cuate.png'

const Error404Page = () => (
  <div className="w-full min-h-screen flex items-center justify-center bg-white px-4">
    <div className="w-full max-w-md flex flex-col items-center gap-8 py-12">
      
      {/* Heading */}
      <div className="text-center w-full">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Oops! You're Off the Map – Error 404
        </h2>
        <p className="text-gray-500 text-sm mb-6">
          The page you're looking for doesn't exist or has been moved.
        </p>
      </div>
      {/* Image */}
      <div className="flex justify-center">
        <img src={Error404Img} alt="Error 404" className="h-80 w-full max-w-[420px] object-contain mx-auto" />
      </div>
      {/* Button */}
      <Link
        to="/"
        className="bg-black text-white px-6 py-2 rounded-md font-semibold hover:bg-gray-900 transition-colors text-sm"
      >
        Back to home page
      </Link>
    </div>
  </div>
);

export default Error404Page;
