import React, { useState } from "react";
import { Coffee, Utensils, Clock, Star } from "lucide-react";

// Define the Meal interface
export interface Meal {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  revenueCategory?: "Food" | "Beverage" | "Dessert" | "Other";
  TaxClass?: number;
  prepTime?: string;
  isAvailable?: boolean;
  orderingChannels?: ("Online" | "InStore" | "Delivery")[];
  isCombo?: boolean;
  autoAdjustInventory?: boolean;
  inventoryCount?: number;
  printer?: "Kitchen" | "Bar" | "Dessert" | "Other" | null; // Added printer assignment
}

// Modal component
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  meal: Meal;
  onToggleAvailability?: (meal: Meal, isAvailable: boolean) => void; // Callback for availability toggle
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, meal, onToggleAvailability }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4">{meal.title}</h2>
        <img src={meal.imageUrl} alt={meal.title} className="w-full h-48 object-cover rounded-lg mb-4" />
        <p className="text-gray-600 mb-2">{meal.description}</p>
        <p className="text-gray-800 font-semibold">Price: {meal.price}</p>
        <p className="text-gray-600">Category: {meal.category}</p>
        <p className="text-gray-600">Revenue Category: {meal.revenueCategory}</p>
        <p className="text-gray-600">Preparation Time: {meal.prepTime}</p>
        <p className="text-gray-600">Printer: {meal.printer || "Not assigned"}</p>
        <p className="text-gray-600">Ordering Channels: {meal.orderingChannels?.join(", ") || "All"}</p>
        <p className="text-gray-600">Combo Item: {meal.isCombo ? "Yes" : "No"}</p>
        <p className="text-gray-600">Auto-Adjust Inventory: {meal.autoAdjustInventory ? "Yes" : "No"}</p>
        {meal.inventoryCount !== undefined && (
          <p className="text-gray-600">Inventory: {meal.inventoryCount}</p>
        )}
        <div className="flex items-center space-x-2 mt-2">
          <span className="text-gray-600">Availability:</span>
          <button
            onClick={() => onToggleAvailability && onToggleAvailability(meal, !meal.isAvailable)}
            className={`
              px-2 py-1 text-xs font-medium rounded-full
              ${meal.isAvailable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
              hover:bg-opacity-80 transition-colors
            `}
          >
            {meal.isAvailable ? "Available" : "Unavailable"}
          </button>
        </div>
        <button
          onClick={onClose}
          className="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
        >
          Close
        </button>
      </div>
    </div>
  );
};

interface MealCardProps {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  revenueCategory?: "Food" | "Beverage" | "Dessert" | "Other";
  rating?: number;
  prepTime?: string;
  isAvailable?: boolean;
  orderingChannels?: ("Online" | "InStore" | "Delivery")[];
  isCombo?: boolean;
  autoAdjustInventory?: boolean;
  inventoryCount?: number;
  printer?: "Kitchen" | "Bar" | "Dessert" | "Other" | null; // Added printer assignment
  onAddToCart?: (e?: React.MouseEvent<HTMLButtonElement>, meal?: Meal) => void;
  onViewDetails?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  onToggleAvailability?: (meal: Meal, isAvailable: boolean) => void; // Callback for availability toggle
  onClick?: () => void;
  className?: string;
}

export function MealCard({
  title,
  description,
  imageUrl,
  price = "$8.50",
  category = "Mains",
  revenueCategory = "Food",
  rating = 4.8,
  prepTime = "5 mins",
  isAvailable = true,
  orderingChannels = ["Online", "InStore", "Delivery"],
  isCombo = false,
  autoAdjustInventory = false,
  inventoryCount,
  printer = null, // Default to no printer
  onAddToCart,
  onViewDetails,
  onToggleAvailability,
  onClick,
  className,
}: MealCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const meal: Meal = {
    title,
    description,
    imageUrl,
    price,
    category,
    revenueCategory,
    prepTime,
    isAvailable,
    orderingChannels,
    isCombo,
    autoAdjustInventory,
    inventoryCount,
    printer,
  };

  const handleViewDetails = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsModalOpen(true);
    onViewDetails && onViewDetails(e);
  };

  const handleAddToCart = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (onAddToCart && isAvailable && (!inventoryCount || inventoryCount > 0)) {
      onAddToCart(e, meal);
      if (autoAdjustInventory && inventoryCount !== undefined) {
        console.log(`Inventory for ${title} decremented by 1`);
      }
    }
  };

  const handleToggleAvailability = (updatedMeal: Meal, newAvailability: boolean) => {
    onToggleAvailability && onToggleAvailability(updatedMeal, newAvailability);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div
        className={`
          group relative overflow-hidden
          bg-gradient-to-br from-white to-gray-50
          border-0 shadow-sm hover:shadow-xl
          transition-all duration-300 ease-out
          cursor-pointer
          backdrop-blur-sm
          hover:scale-[1.02] hover:-translate-y-1
          rounded-xl
          ${className || ""}
          ${!isAvailable ? "opacity-50" : ""}
        `}
        onClick={onClick}
        tabIndex={0}
        role="button"
        aria-label={`Open ${title} details`}
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <div className="relative flex items-center p-3 space-x-3">
          {/* Compact image */}
          <div className="relative flex-shrink-0 w-16 h-16 rounded-xl overflow-hidden">
            <img
              src={imageUrl}
              alt={title}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-black/10 group-hover:bg-black/0 transition-colors duration-300" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm truncate group-hover:text-orange-600 transition-colors duration-200">
              {title}
            </h3>
            <p className="text-xs text-gray-500 mt-0.5 line-clamp-2 leading-relaxed">
              {description}
            </p>
            <div className="flex items-center mt-1 space-x-2">
              <span className="text-xs text-gray-600">{revenueCategory}</span>
              {isCombo && (
                <span className="text-xs text-orange-500 font-medium">Combo</span>
              )}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleAvailability(meal, !isAvailable);
                }}
                className={`
                  px-2 py-0.5 text-xs font-medium rounded-full
                  ${isAvailable ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                  hover:bg-opacity-80 transition-colors
                `}
                aria-label={`Toggle availability for ${title}`}
              >
                {isAvailable ? "Available" : "Unavailable"}
              </button>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex-shrink-0 flex space-x-1">
            <button
              onClick={handleAddToCart}
              disabled={!onAddToCart || !isAvailable || (inventoryCount !== undefined && inventoryCount <= 0)}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-gradient-to-r from-orange-500 to-red-500
                text-white rounded-lg
                hover:from-orange-600 hover:to-red-600
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
                hover:shadow-lg hover:shadow-orange-500/25
                transform hover:scale-105
              "
              aria-label={`Add ${title} to cart`}
            >
              Add
            </button>

            <button
              onClick={handleViewDetails}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-white border border-gray-200
                text-gray-700 rounded-lg
                hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50
                hover:border-orange-300
                transition-all duration-200
                hover:shadow-md
                transform hover:scale-105
              "
              aria-label={`View details for ${title}`}
            >
              View
            </button>
          </div>
        </div>

        {/* Bottom accent line */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Render Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={closeModal}
        meal={meal}
        onToggleAvailability={handleToggleAvailability}
      />
    </>
  );
}