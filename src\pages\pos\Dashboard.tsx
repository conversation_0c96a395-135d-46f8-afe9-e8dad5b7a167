import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Screen } from '@/app-components/layout/screen';
import {
  Building2,
  Store,
  Monitor,
  Calculator,
  Printer,
  Plus,
  BarChart3,
  Settings
} from 'lucide-react';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetRevenueCentersQuery } from '@/redux/slices/revenueCenters';
import { useGetWorkstationsQuery } from '@/redux/slices/workstations';
import { useGetTaxClassesQuery } from '@/redux/slices/taxClasses';
import { useGetPrintersQuery } from '@/redux/slices/printers';

const POSDashboard: React.FC = () => {
  // API hooks to fetch real data
  const { data: branches = [] } = useGetBranchesQuery({});
  const { data: revenueCenters = [] } = useGetRevenueCentersQuery({});
  const { data: workstations = [] } = useGetWorkstationsQuery({});
  const { data: taxClasses = [] } = useGetTaxClassesQuery({});
  const { data: printers = [] } = useGetPrintersQuery({});

  // Calculate statistics from real data
  const branchStats = {
    total: Array.isArray(branches) ? branches.length : 0,
    active: Array.isArray(branches) ? branches.filter(b => b.is_active !== false).length : 0
  };

  const revenueCenterStats = {
    total: Array.isArray(revenueCenters) ? revenueCenters.length : 0,
    active: Array.isArray(revenueCenters) ? revenueCenters.filter(rc => rc.is_active !== false).length : 0
  };

  const workstationStats = {
    total: Array.isArray(workstations) ? workstations.length : 0,
    active: Array.isArray(workstations) ? workstations.filter(ws => ws.is_active !== false).length : 0
  };

  const taxClassStats = {
    total: Array.isArray(taxClasses) ? taxClasses.length : 0,
    active: Array.isArray(taxClasses) ? taxClasses.length : 0 // Assuming all tax classes are active
  };

  const printerStats = {
    total: Array.isArray(printers) ? printers.length : 0,
    active: Array.isArray(printers) ? printers.filter(p => p.is_active !== false).length : 0
  };

  const configurationCards = [
    {
      title: 'Branch Management',
      description: 'Manage hotel or restaurant chain branches with localization and tax settings',
      icon: Building2,
      href: '/pos/branches',
      color: 'bg-blue-500',
      stats: branchStats
    },
    {
      title: 'Revenue Centers',
      description: 'Configure business units within branches that generate income',
      icon: Store,
      href: '/pos/revenue-centers',
      color: 'bg-green-500',
      stats: revenueCenterStats
    },
    {
      title: 'Workstations',
      description: 'Set up POS devices, terminals, and kitchen displays',
      icon: Monitor,
      href: '/pos/workstations',
      color: 'bg-purple-500',
      stats: workstationStats
    },
    {
      title: 'Tax Configuration',
      description: 'Create tax classes and rates for products and services',
      icon: Calculator,
      href: '/pos/tax-configuration',
      color: 'bg-orange-500',
      stats: taxClassStats
    },
    {
      title: 'Printer Management',
      description: 'Configure thermal and receipt printers for workstations',
      icon: Printer,
      href: '/pos/printers',
      color: 'bg-red-500',
      stats: printerStats
    }
  ];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">POS Configuration</h1>
          <p className="text-muted-foreground">
            Manage your point-of-sale system configuration and settings
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Reports
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Configuration Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {configurationCards.map((card) => {
          const IconComponent = card.icon;
          return (
            <Card key={card.title} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${card.color} text-white`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <Link to={card.href}>
                    <Button variant="ghost" size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                <CardTitle className="text-lg">{card.title}</CardTitle>
                <CardDescription className="text-sm">
                  {card.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">
                    Total: {card.stats.total}
                  </span>
                  <span className="text-green-600 font-medium">
                    Active: {card.stats.active}
                  </span>
                </div>
                <Link to={card.href}>
                  <Button className="w-full mt-4" variant="outline">
                    Manage {card.title}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common configuration tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Link to="/pos/branches/new">
              <Button variant="outline" className="w-full justify-start">
                <Building2 className="h-4 w-4 mr-2" />
                Add New Branch
              </Button>
            </Link>
            <Link to="/pos/revenue-centers/new">
              <Button variant="outline" className="w-full justify-start">
                <Store className="h-4 w-4 mr-2" />
                Add Revenue Center
              </Button>
            </Link>
            <Link to="/pos/workstations/new">
              <Button variant="outline" className="w-full justify-start">
                <Monitor className="h-4 w-4 mr-2" />
                Setup Workstation
              </Button>
            </Link>
            <Link to="/pos/printers/new">
              <Button variant="outline" className="w-full justify-start">
                <Printer className="h-4 w-4 mr-2" />
                Add Printer
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="h-2 w-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-green-600">All systems operational</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Workstations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {workstationStats.active}
            </div>
            <p className="text-xs text-muted-foreground">
              out of {workstationStats.total} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Printer Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {printerStats.active}
            </div>
            <p className="text-xs text-muted-foreground">printers online</p>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default POSDashboard;
