import React from 'react';
import { X, Edit, Package, DollarSign, Calendar, Truck, Tag, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { Product } from './product.type';


interface ProductDetailViewProps {
  product: Product;
  onClose: () => void;
  onEdit: () => void;
}

export const ProductDetailView: React.FC<ProductDetailViewProps> = ({
  product,
  onClose,
  onEdit
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStockStatus = () => {
    if (!product.stockLevel || !product.minStockLevel) return 'unknown';
    if (product.stockLevel <= product.minStockLevel) return 'low';
    if (product.stockLevel <= product.minStockLevel * 1.5) return 'medium';
    return 'good';
  };

  const stockStatus = getStockStatus();
  const stockColors = {
    good: 'text-green-600 bg-green-50',
    medium: 'text-yellow-600 bg-yellow-50',
    low: 'text-red-600 bg-red-50',
    unknown: 'text-gray-600 bg-gray-50'
  };

  const isExpiringSoon = () => {
    if (!product.expiryDate) return false;
    const expiryDate = new Date(product.expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };

  const isExpired = () => {
    if (!product.expiryDate) return false;
    const expiryDate = new Date(product.expiryDate);
    const today = new Date();
    return expiryDate < today;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-3">
              <Package className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{product.name}</h2>
                <p className="text-sm text-gray-500">Product Code: {product.code}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={onEdit}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Edit className="w-4 h-4" />
                Edit Product
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Status Banner */}
          <div className="mb-6">
            <div className="flex items-center gap-4 p-4 rounded-lg bg-gray-50">
              <div className="flex items-center gap-2">
                {product.active ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                <span className={`font-medium ${product.active ? 'text-green-700' : 'text-red-700'}`}>
                  {product.active ? 'Active Product' : 'Inactive Product'}
                </span>
              </div>
              
              {product.perishable && (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-orange-500" />
                  <span className="font-medium text-orange-700">Perishable Item</span>
                </div>
              )}

              {isExpired() && (
                <div className="flex items-center gap-2">
                  <XCircle className="w-5 h-5 text-red-500" />
                  <span className="font-medium text-red-700">Expired</span>
                </div>
              )}

              {isExpiringSoon() && !isExpired() && (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                  <span className="font-medium text-yellow-700">Expiring Soon</span>
                </div>
              )}
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Tag className="w-5 h-5 text-blue-600" />
                Basic Information
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Product Name</label>
                  <p className="text-gray-900 font-medium">{product.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Product Code (SKU)</label>
                  <p className="text-gray-900 font-mono">{product.code}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Category</label>
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    {product.category}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Unit of Measure</label>
                  <p className="text-gray-900">{product.unitOfMeasure}</p>
                </div>
              </div>
            </div>

            {/* Pricing & Tax Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-green-600" />
                Pricing & Tax
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Cost per Unit</label>
                  <p className="text-2xl font-bold text-gray-900">${product.cost.toFixed(2)}</p>
                  <p className="text-sm text-gray-500">Exclusive of tax</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Tax Code</label>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    product.taxCode === 'VAT' ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {product.taxCode}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Tax Inclusive Price</label>
                  <p className="text-lg font-semibold text-gray-700">
                    ${product.taxCode === 'VAT' ? (product.cost * 1.2).toFixed(2) : product.cost.toFixed(2)}
                  </p>
                  {product.taxCode === 'VAT' && (
                    <p className="text-sm text-gray-500">Includes 20% VAT</p>
                  )}
                </div>
              </div>
            </div>

            {/* Perishable Information */}
            {product.perishable && (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Calendar className="w-5 h-5 text-orange-600" />
                  Perishable Information
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Perishable Status</label>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                      Perishable Item
                    </span>
                  </div>
                  {product.shelfLifeDays && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Shelf Life</label>
                      <p className="text-gray-900">{product.shelfLifeDays} days</p>
                    </div>
                  )}
                  {product.expiryDate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Expiry Date</label>
                      <p className={`font-medium ${
                        isExpired() ? 'text-red-600' : 
                        isExpiringSoon() ? 'text-yellow-600' : 'text-gray-900'
                      }`}>
                        {new Date(product.expiryDate).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                      {isExpired() && (
                        <p className="text-sm text-red-500">This product has expired</p>
                      )}
                      {isExpiringSoon() && !isExpired() && (
                        <p className="text-sm text-yellow-600">Expires within 7 days</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Supplier Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Truck className="w-5 h-5 text-purple-600" />
                Supplier Information
              </h3>
              <div className="space-y-4">
                {product.defaultSupplier ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Default Supplier</label>
                      <p className="text-gray-900 font-medium">{product.defaultSupplier}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Supplier Status</label>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Active Supplier
                      </span>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">No default supplier assigned</p>
                    <p className="text-sm text-gray-400">Supplier can be assigned during editing</p>
                  </div>
                )}
              </div>
            </div>

            {/* Stock Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="w-5 h-5 text-indigo-600" />
                Stock Information
              </h3>
              <div className="space-y-4">
                {product.stockLevel !== undefined && product.minStockLevel !== undefined ? (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Current Stock Level</label>
                      <div className="flex items-center gap-3">
                        <p className="text-2xl font-bold text-gray-900">{product.stockLevel}</p>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${stockColors[stockStatus]}`}>
                          {stockStatus === 'good' && 'Good Stock'}
                          {stockStatus === 'medium' && 'Medium Stock'}
                          {stockStatus === 'low' && 'Low Stock'}
                          {stockStatus === 'unknown' && 'Unknown'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Minimum Stock Level</label>
                      <p className="text-gray-900">{product.minStockLevel}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">Stock Status</label>
                      {stockStatus === 'low' && (
                        <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                          <AlertTriangle className="w-5 h-5 text-red-500" />
                          <span className="text-red-700 font-medium">Stock level is below minimum threshold</span>
                        </div>
                      )}
                      {stockStatus === 'medium' && (
                        <div className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg">
                          <AlertTriangle className="w-5 h-5 text-yellow-500" />
                          <span className="text-yellow-700 font-medium">Stock level is getting low</span>
                        </div>
                      )}
                      {stockStatus === 'good' && (
                        <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                          <CheckCircle className="w-5 h-5 text-green-500" />
                          <span className="text-green-700 font-medium">Stock level is adequate</span>
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Stock information not available</p>
                    <p className="text-sm text-gray-400">Connect to inventory module for real-time stock data</p>
                  </div>
                )}
              </div>
            </div>

            {/* Linked Recipes (Future Integration) */}
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Package className="w-5 h-5 text-teal-600" />
                Linked Recipes
              </h3>
              <div className="text-center py-4">
                <p className="text-gray-500">No linked recipes found</p>
                <p className="text-sm text-gray-400">This product is not currently used in any recipes</p>
                <button className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                  View Recipe Integration →
                </button>
              </div>
            </div>
          </div>

          {/* Metadata */}
          <div className="mt-6 bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Record Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Created:</span>
                <span className="ml-2 text-gray-900">{formatDate(product.createdAt)}</span>
              </div>
              <div>
                <span className="text-gray-500">Last Updated:</span>
                <span className="ml-2 text-gray-900">{formatDate(product.updatedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};