import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import {
  useGetPurchaseRequisitionsQuery,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,
  useDeletePurchaseRequisitionMutation,
  useConvertPurchaseRequisitionToRFQMutation,
  useMarkPurchaseRequisitionAsApprovedMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { PurchaseRequisition } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { searchDebouncer } from "@/utils/debouncers";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Edit, Send, CheckCircle, XCircle, Plus, Trash2 } from "lucide-react";
import AddPurchaseRequisition from "./modals/AddPurchaseRequisition";
import RejectPurchaseRequisition from "./modals/RejectPurchaseRequisition";
import ViewPurchaseRequisition from "./modals/ViewPurchaseRequisition";
import { toast } from "@/components/custom/Toast/MyToast";

const PurchaseRequisitionsIndex = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedRequisition, setSelectedRequisition] = useState<any>(null);
  const [searchInput, setSearchInput] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [statusFilter, setStatusFilter] = useState("");

  // Fetch data
  const {
    data: purchaseRequisitions,
    isLoading,
    error,
    isFetching,
  } = useGetPurchaseRequisitionsQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
    status: statusFilter,
  });

  // Mutation hooks
  const [submitPurchaseRequisition, { isLoading: isSubmitting }] = useSubmitPurchaseRequisitionMutation();
  const [approvePurchaseRequisition, { isLoading: isApproving }] = useApprovePurchaseRequisitionMutation();
  const [rejectPurchaseRequisition, { isLoading: isRejecting }] = useRejectPurchaseRequisitionMutation();
  const [deletePurchaseRequisition, { isLoading: isDeleting }] = useDeletePurchaseRequisitionMutation();
  const [convertPurchaseRequisitionToRFQ, { isLoading: isConverting }] = useConvertPurchaseRequisitionToRFQMutation();
  const [markPurchaseRequisitionAsApproved] = useMarkPurchaseRequisitionAsApprovedMutation();

  // Handler functions
  const handleSubmit = async (id: number) => {
    try {
      await submitPurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async (id: number) => {
    try {
      await approvePurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = (requisition: any) => {
    setSelectedRequisition(requisition);
    setIsRejectModalOpen(true);
  };

  const handleView = (requisition: any) => {
    setSelectedRequisition(requisition);
    setIsViewModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this purchase requisition?")) return;

    try {
      await deletePurchaseRequisition(id).unwrap();
      toast.success("Purchase requisition deleted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete requisition");
    }
  };

  const handleConvertToRFQ = async (requisition: PurchaseRequisition) => {
    try {
      // Step 1: Create RFQ from Purchase Requisition
      const result = await convertPurchaseRequisitionToRFQ({
        id: requisition.id,
        code: requisition.code,
        store_requisition: requisition.store_requisition,
        created_by: requisition.created_by
      }).unwrap();

      // Step 2: Mark Purchase Requisition as approved
      await markPurchaseRequisitionAsApproved(requisition.id).unwrap();

      toast.success(`Successfully converted to RFQ: ${result.rfq_number || 'RFQ-' + String(result.id).padStart(5, '0')}`);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to RFQ");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: {
        variant: "secondary" as const,
        color: "bg-gray-100 text-gray-800",
        icon: "📝",
        description: "In preparation"
      },
      Open: {
        variant: "default" as const,
        color: "bg-blue-100 text-blue-800",
        icon: "📂",
        description: "Ready for processing"
      },
      Submitted: {
        variant: "default" as const,
        color: "bg-blue-100 text-blue-800",
        icon: "📤",
        description: "Awaiting approval"
      },
      Approved: {
        variant: "default" as const,
        color: "bg-green-100 text-green-800",
        icon: "✅",
        description: "Ready for RFQ"
      },
      Rejected: {
        variant: "destructive" as const,
        color: "bg-red-100 text-red-800",
        icon: "❌",
        description: "Needs revision"
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <div className="flex flex-col">
        <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
          <span>{config.icon}</span>
          {status}
        </Badge>
        <span className="text-xs text-gray-500 mt-1">
          {config.description}
        </span>
      </div>
    );
  };

  const columns: ColumnDef<PurchaseRequisition>[] = [
    {
      accessorKey: "code",
      header: "Purchase Requisition Code",
      cell: (info) => {
        const code = info.getValue() as string;
        const id = info.row.original.id;
        return (
          <div className="flex flex-col">
            <button
              onClick={() => handleView(info.row.original)}
              className="font-semibold text-blue-600 hover:text-blue-800 hover:underline text-left"
              title="Click to view details"
            >
              {code || `PR-${String(id).padStart(6, '0')}`}
            </button>
            <span className="text-xs text-gray-500">
              ID: {id}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "store_requisition",
      header: "Source Store Requisition",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <div className="flex flex-col">
            {value ? (
              <>
                <Link
                  to={`/procurement/store-requisitions`}
                  className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {value}
                </Link>
                <span className="text-xs text-gray-500">
                  Converted from Store Req.
                </span>
              </>
            ) : (
              <>
                <span className="font-medium text-gray-900">Direct Creation</span>
                <span className="text-xs text-gray-500">
                  Created manually
                </span>
              </>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_by",
      header: "Created By",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {value || "Not Assigned"}
            </span>
            {value && (
              <span className="text-xs text-gray-500">
                Employee: {value}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "purchase_items",
      header: "Items",
      cell: (info) => {
        const items = info.getValue() as any[] || [];
        const itemCount = items.length;
        const totalQuantity = items.reduce((sum: number, item: any) =>
          sum + parseFloat(item.quantity || 0), 0
        );

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {itemCount} {itemCount === 1 ? 'item' : 'items'}
            </span>
            <span className="text-xs text-gray-500">
              Total qty: {totalQuantity.toFixed(2)}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => getStatusBadge(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      accessorKey: "id",
      header: "Created",
      cell: (info) => {
        // Since created_at is not available in the API response, we'll show a placeholder
        // You can update this when the API includes created_at field
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              Recently
            </span>
            <span className="text-xs text-gray-500">
              ID: {info.getValue() as number}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const requisition = row.original;
        const status = requisition.status;
        const canEdit = status === "Draft";
        const canDelete = status === "Draft";
        const canSubmit = status === "Draft";
        const canConvertToRFQ = status === "Submitted";
        const canApprove = status === "Submitted";
        const canReject = status === "Submitted";

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(requisition)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>

              {canEdit && (
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}

              {canSubmit && (
                <DropdownMenuItem onClick={() => handleSubmit(requisition.id!)}>
                  <Send className="mr-2 h-4 w-4" />
                  Submit
                </DropdownMenuItem>
              )}

              {canConvertToRFQ && (
                <DropdownMenuItem onClick={() => handleConvertToRFQ(requisition)}>
                  <Send className="mr-2 h-4 w-4" />
                  Convert to RFQ
                </DropdownMenuItem>
              )}

              {canDelete && (
                <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(requisition.id!)}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}

              {canReject && (
                <DropdownMenuItem className="text-red-600" onClick={() => handleReject(requisition)}>
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </DropdownMenuItem>
              )}

              {status === "Approved" && (
                <DropdownMenuItem disabled>
                  <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                  Ready for RFQ
                </DropdownMenuItem>
              )}

              {status === "Rejected" && (
                <DropdownMenuItem>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit & Resubmit
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Purchase Requisitions</h1>
            <p className="text-gray-600 mt-1">Manage purchase requests and approvals</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="default" onClick={() => setIsAddModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
              ➕ Create Purchase Requisition
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        {purchaseRequisitions && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            {(() => {
              const results = purchaseRequisitions?.results || [];
              const stats = results.reduce((acc: any, req: any) => {
                acc.total++;
                acc[req.status] = (acc[req.status] || 0) + 1;
                acc.totalItems += req.purchase_items?.length || 0;
                return acc;
              }, { total: 0, totalItems: 0, Draft: 0, Open: 0, Submitted: 0, Approved: 0, Rejected: 0 }) || {};

              return (
                <>
                  <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Purchase Requisitions</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                      </div>
                      <div className="text-2xl">📋</div>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Open/Active</p>
                        <p className="text-2xl font-bold text-blue-600">{(stats.Open || 0) + (stats.Submitted || 0)}</p>
                      </div>
                      <div className="text-2xl">📂</div>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Approved</p>
                        <p className="text-2xl font-bold text-green-600">{stats.Approved || 0}</p>
                      </div>
                      <div className="text-2xl">✅</div>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Draft</p>
                        <p className="text-2xl font-bold text-gray-600">{stats.Draft || 0}</p>
                      </div>
                      <div className="text-2xl">📝</div>
                    </div>
                  </div>
                  <div className="bg-white p-4 rounded-lg shadow-sm border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Items</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
                      </div>
                      <div className="text-2xl">📦</div>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap gap-4 items-center">
          {/* Status Filter */}
          <div className="flex gap-2">
            <Button
              variant={statusFilter === "" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("")}
            >
              All Status
            </Button>
            <Button
              variant={statusFilter === "Draft" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Draft")}
            >
              Draft
            </Button>
            <Button
              variant={statusFilter === "Submitted" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Submitted")}
            >
              Submitted
            </Button>
            <Button
              variant={statusFilter === "Approved" ? "default" : "outline"}
              size="sm"
              onClick={() => setStatusFilter("Approved")}
            >
              Approved
            </Button>
          </div>
        </div>

        {/* Loading Indicator */}
        {(isFetching || isSubmitting || isApproving || isRejecting || isDeleting) && (
          <div className="flex items-center justify-center py-4 bg-blue-50 rounded-lg mb-4">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            <span className="text-blue-800 text-sm">
              {isFetching && "Loading data..."}
              {isSubmitting && "Submitting requisition..."}
              {isApproving && "Approving requisition..."}
              {isRejecting && "Rejecting requisition..."}
              {isDeleting && "Deleting requisition..."}
            </span>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading purchase requisitions...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <XCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">
                Failed to load purchase requisitions. Please try again.
              </span>
            </div>
          </div>
        )}

        {/* Data Table */}
        <DataTable<PurchaseRequisition>
          data={purchaseRequisitions?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search purchase requisitions..."
            />
          }
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={purchaseRequisitions?.total_data || 0}
        />

        {/* Global Loading Indicator */}
        {(isFetching || isSubmitting || isApproving || isRejecting || isDeleting || isConverting) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700 font-medium">
                {isFetching && "Loading data..."}
                {isSubmitting && "Submitting requisition..."}
                {isApproving && "Approving requisition..."}
                {isRejecting && "Rejecting requisition..."}
                {isDeleting && "Deleting requisition..."}
                {isConverting && "Converting to RFQ..."}
              </span>
            </div>
          </div>
        )}

        {isAddModalOpen && (
          <AddPurchaseRequisition
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
          />
        )}

        {isRejectModalOpen && selectedRequisition && (
          <RejectPurchaseRequisition
            isOpen={isRejectModalOpen}
            onClose={() => {
              setIsRejectModalOpen(false);
              setSelectedRequisition(null);
            }}
            requisitionId={selectedRequisition.id}
            requisitionNumber={`PR-${String(selectedRequisition.id).padStart(4, '0')}`}
          />
        )}

        {isViewModalOpen && selectedRequisition && (
          <ViewPurchaseRequisition
            isOpen={isViewModalOpen}
            onClose={() => {
              setIsViewModalOpen(false);
              setSelectedRequisition(null);
            }}
            requisition={selectedRequisition}
          />
        )}
      </div>
    </Screen>
  );
};

export default PurchaseRequisitionsIndex;
