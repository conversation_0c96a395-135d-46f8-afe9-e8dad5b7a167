import { apiSlice } from "../apiSlice";

export const menuGroupApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenuSubGroups: builder.query({
      query: (params) => ({
        url: "/menu/menu-subgroups",
        method: "GET",
        params: params,
      }),
      providesTags: ["MenuGroups"],
    }),

    retrieveMenuSubGroup: builder.query({
      query: (id) => ({
        url: `/menu/menu-subgroups/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "MenuGroups", id }],
    }),

    addMenuSubGroup: builder.mutation({
      query: (payload) => ({
        url: "/menu/menu-subgroups",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["MenuGroups"],
    }),

    patchMenuSubGroup: builder.mutation({
      query: (payload) => ({
        url: `/menu/menu-subgroups/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "MenuGroups", id },
        "MenuGroups",
      ],
    }),

    deleteMenuSubGroup: builder.mutation({
      query: (id) => ({
        url: `/menu/menu-subgroups/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["MenuGroups"],
    }),
  }),
});

export const {
    useGetMenuSubGroupsQuery,
    useRetrieveMenuSubGroupQuery,
    useAddMenuSubGroupMutation,
    usePatchMenuSubGroupMutation,
    useDeleteMenuSubGroupMutation,
    useLazyGetMenuSubGroupsQuery,
    useLazyRetrieveMenuSubGroupQuery,
    

 
} = menuGroupApiSlice;
