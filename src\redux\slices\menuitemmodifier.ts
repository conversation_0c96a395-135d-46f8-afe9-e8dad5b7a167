import { apiSlice } from "../apiSlice";

export const comboMenuApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getModifierMenus: builder.query({
      query: (params) => ({
        url: "/menu/menu-item-modifiers",
        method: "GET",
        params: params,
      }),
      providesTags: ["ComboMenu"],
    }),

    retrieveModifierMenu: builder.query({
      query: (id) => ({
        url: `/menu/menu-item-modifiers/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "ComboMenu", id }],
    }),

    addModifierMenu: builder.mutation({
      query: (payload) => ({
        url: "/menu/menu-item-modifiers",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ComboMenu"],
    }),

    patchModifierMenu: builder.mutation({
      query: (payload) => ({
        url: `/menu/menu-item-modifiers/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "ComboMenu", id },
        "ComboMenu",
      ],
    }),

    deleteModifierMenu: builder.mutation({
      query: (id) => ({
        url: `/menu/menu-item-modifiers/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["ComboMenu"],
    }),
  }),
});

export const {
    useGetModifierMenusQuery,
    useRetrieveModifierMenuQuery,
    useAddModifierMenuMutation,
    usePatchModifierMenuMutation,
    useDeleteModifierMenuMutation,
    useLazyGetModifierMenusQuery,
    useLazyRetrieveModifierMenuQuery,
} = comboMenuApiSlice;

