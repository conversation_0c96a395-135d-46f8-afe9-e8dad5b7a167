import React, { useState, useEffect } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { motion } from "framer-motion";
import { usePatchOrderMutation } from "@/redux/slices/order";

interface Order {
  id: number;
  order_number: string;
  order_type: string;
  status: 'Open' | 'In Progress' | 'Completed' | 'Cancelled' | 'Refunded';
  order_date: string;
  total_amount: string;
  payment_status: boolean;
  guest_count: number;
  table_number: number;
  created_by: string;
  tax_amount: string;
  service_charge: string;
  catering_levy: string;
  revenue_center: number;
  workstation: number;
  created_at: string;
  modified_at: string;
}

interface OrderTrackerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order;
}

function OrderTrackerPipeline({ open, onOpenChange, order }: OrderTrackerProps) {
  const [isBeeping, setIsBeeping] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [updateOrderStatus, { isLoading: isUpdating, error }] = usePatchOrderMutation();

  // Beeping animation effect
  useEffect(() => {
    const interval = setInterval(() => {
      setIsBeeping((prev) => !prev);
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // Handle mutation errors
  useEffect(() => {
    if (error) {
      console.error('API Error:', error);
      const message = error.status === 404
        ? `Order not found. Please verify the order details.`
        : `Failed to update order status: ${error.data?.message || 'Unknown error'}`;
      setErrorMessage(message);
    }
  }, [error]);

  // Define the actual status flow from your backend
  const statusFlow: Order['status'][] = ['Open', 'In Progress', 'Completed'];
  const currentStatusIndex = statusFlow.indexOf(order.status);

  const getNextStatus = (): Order['status'] | null => {
    if (order.status === 'Cancelled' || order.status === 'Refunded') return null;
    const nextIndex = currentStatusIndex + 1;
    return statusFlow[nextIndex] || null;
  };

  const statusDetails = {
    'Open': {
      name: "Order Placed",
      description: "Your order has been received",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      time: new Date(order.order_date).toLocaleTimeString(),
    },
    'In Progress': {
      name: "In Progress",
      description: "Your order is being prepared",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      time: "In progress",
    },
    'Completed': {
      name: "Completed",
      description: "Your order has been completed",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      time: "Completed",
    },
    'Cancelled': {
      name: "Cancelled",
      description: "Your order has been cancelled",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      ),
      time: "Cancelled",
    },
    'Refunded': {
      name: "Refunded",
      description: "Your order has been refunded",
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      time: "Refunded",
    },
  };

  const handleNextStatus = async () => {
    const nextStatus = getNextStatus();
    if (!nextStatus) return;

    try {
      console.log('Updating order status:', {
        id: order.id, // Using the numeric ID from your API response
        status: nextStatus
      });

      await updateOrderStatus({
        id: order.id, // Make sure your Redux endpoint expects this numeric ID
        status: nextStatus,
      }).unwrap();
      setErrorMessage(null);
    } catch (err: any) {
      console.error('Update failed:', {
        error: err,
        requestData: {
          id: order.id,
          status: nextStatus
        }
      });
      setErrorMessage(err.data?.message || 'Failed to update order status');
    }
  };

  const handleToggleCancel = async () => {
    const newStatus = order.status === 'Cancelled' ? 'Open' : 'Cancelled';

    try {
      await updateOrderStatus({
        id: order.id,
        status: newStatus,
      }).unwrap();
      setErrorMessage(null);
    } catch (err: any) {
      console.error('Cancellation failed:', err);
      setErrorMessage(err.data?.message || 'Failed to update order status');
    }
  };

  const getStatusClasses = (status: Order['status']) => {
    const baseClasses = "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300";

    if (status === 'Cancelled' || status === 'Refunded') {
      return `${baseClasses} bg-destructive/10 border-destructive/30 text-destructive`;
    }
    if (status === 'Completed') {
      return `${baseClasses} bg-primary border-primary text-white`;
    }
    if (status === order.status) {
      const beepClasses = isBeeping ? "ring-4 ring-secondary/20 scale-110" : "ring-2 ring-secondary/10";
      return `${baseClasses} bg-secondary border-secondary text-white ${beepClasses}`;
    }
    if (statusFlow.indexOf(status) < currentStatusIndex) {
      return `${baseClasses} bg-primary/10 border-primary/30 text-primary`;
    }
    return `${baseClasses} bg-muted border-outline text-muted-foreground`;
  };

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title={`Order #${order.id} Tracker`} // Using ID since order_number is empty in your example
      description={`${order.order_type} Order`}
      className="max-w-4xl"
      size="lg"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex flex-col gap-6 p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-md"
      >
        {/* Error Message */}
        {errorMessage && (
          <div className="px-6 py-2 bg-destructive/10 text-destructive text-sm rounded-md">
            {errorMessage}
          </div>
        )}

        {/* Order Status Banner */}
        <div className="px-6 py-4 bg-white border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                {statusDetails[order.status].name}
              </h2>
              <p className="text-sm text-gray-600">
                {statusDetails[order.status].description}
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={handleNextStatus}
                disabled={!getNextStatus() || isUpdating}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:bg-muted disabled:text-muted-foreground transition-colors text-sm"
              >
                {isUpdating ? "Updating..." : `Mark as ${getNextStatus()}`}
              </button>
              <button
                onClick={handleToggleCancel}
                disabled={isUpdating}
                className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                  order.status === 'Cancelled'
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "bg-destructive text-destructive-foreground hover:bg-destructive/90"
                }`}
              >
                {isUpdating ? "Updating..." : order.status === 'Cancelled' ? "Restore" : "Cancel"}
              </button>
            </div>
          </div>
        </div>

        {/* Progress Pipeline */}
        <div className="px-6 py-8 bg-white">
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Progress Line */}
              <div className="absolute top-6 left-6 right-6 h-1 bg-muted rounded-full">
                <div
                  className={`h-full rounded-full transition-all duration-700 ${
                    order.status === 'Cancelled' || order.status === 'Refunded'
                      ? "bg-destructive"
                      : "bg-gradient-to-r from-primary to-secondary"
                  }`}
                  style={{ 
                    width: order.status === 'Completed' 
                      ? '100%' 
                      : order.status === 'Cancelled' || order.status === 'Refunded'
                      ? '100%'
                      : `${(currentStatusIndex / (statusFlow.length - 1)) * 100}%` 
                  }}
                />
              </div>

              {/* Status Indicators */}
              <div className="relative flex justify-between">
                {statusFlow.map((status) => (
                  <div key={status} className="flex flex-col items-center">
                    {/* Status Circle */}
                    <div className={getStatusClasses(status)}>
                      {statusDetails[status].icon}
                    </div>

                    {/* Status Info */}
                    <div className="mt-4 text-center">
                      <h3
                        className={`text-sm font-semibold ${
                          status === order.status
                            ? "text-secondary"
                            : statusFlow.indexOf(status) < currentStatusIndex
                            ? "text-primary"
                            : order.status === 'Cancelled' || order.status === 'Refunded'
                            ? "text-destructive"
                            : "text-muted-foreground"
                        }`}
                      >
                        {statusDetails[status].name}
                      </h3>
                      <p className="text-xs text-muted-foreground mt-1 max-w-20">
                        {statusDetails[status].description}
                      </p>
                      <p className="text-xs text-muted-foreground/70 mt-1">
                        {status === order.status
                          ? statusDetails[status].time
                          : statusFlow.indexOf(status) < currentStatusIndex
                          ? "✓"
                          : statusDetails[status].time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Order Details Card */}
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">Order Details</h3>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  order.status === 'Cancelled' || order.status === 'Refunded'
                    ? "bg-destructive/10 text-destructive"
                    : order.status === 'Completed'
                    ? "bg-primary/10 text-primary"
                    : "bg-secondary/10 text-secondary"
                }`}
              >
                {order.status}
              </span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Order ID</p>
                <p className="font-medium">{order.id}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Order Type</p>
                <p className="font-medium">{order.order_type}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Order Time</p>
                <p className="font-medium">
                  {new Date(order.order_date).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Table Number</p>
                <p className="font-medium">{order.table_number}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Guest Count</p>
                <p className="font-medium">{order.guest_count}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Total Amount</p>
                <p className="font-medium">${order.total_amount}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Payment Status</p>
                <p className="font-medium">{order.payment_status ? "Paid" : "Pending"}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Created By</p>
                <p className="font-medium">{order.created_by}</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </BaseModal>
  );
}

export default OrderTrackerPipeline;