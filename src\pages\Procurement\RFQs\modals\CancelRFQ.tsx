import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { useCancelRFQMutation } from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface CancelRFQProps {
  isOpen: boolean;
  onClose: () => void;
  rfqId: number;
  rfqNumber?: string;
}

const CancelRFQ = ({ 
  isOpen, 
  onClose, 
  rfqId, 
  rfqNumber 
}: CancelRFQProps) => {
  const [reason, setReason] = useState("");
  const [cancelRFQ, { isLoading: cancelling }] = useCancelRFQMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!reason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }

    try {
      await cancelRFQ({ 
        id: rfqId, 
        reason: reason.trim() 
      }).unwrap();
      
      toast.success("RFQ cancelled successfully");
      onClose();
      setReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to cancel RFQ");
    }
  };

  const handleClose = () => {
    if (!cancelling) {
      setReason("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Cancel RFQ
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800">
              You are about to cancel RFQ{" "}
              <span className="font-semibold">
                {rfqNumber || `RFQ-${String(rfqId).padStart(4, '0')}`}
              </span>
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="reason">
                Reason for Cancellation <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder="Please provide a clear reason for cancelling this RFQ..."
                rows={4}
                disabled={cancelling}
                className="resize-none"
              />
              <p className="text-xs text-gray-500">
                This reason will be recorded and may be visible to relevant stakeholders.
              </p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={cancelling}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="destructive"
                disabled={cancelling || !reason.trim()}
              >
                {cancelling && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Cancel RFQ
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CancelRFQ;
