import { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader, Lock } from 'lucide-react';
import Logo from '@/assets/logo.png';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from '@/components/custom/Toast/MyToast';
import { usePostLoginMutation } from '@/redux/slices/auth';
import { useAuthHook } from '@/utils/useAuthHook';
import useLockScreenStore from '@/zustand/useLockScreenStore';
import { useNavigate } from 'react-router-dom';

const LockScreenPage = () => {
  // Prevent back navigation
  useEffect(() => {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
    const handlePopState = () => window.history.go(1);
    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  const navigate = useNavigate();
  const route = useLockScreenStore(state => state.route);
  const { user_details } = useAuthHook();
  const fullnames = user_details?.fullnames || '';

  const [showPassword, setShowPassword] = useState(false);
  const formSchema = z.object({ password: z.string().min(1, { message: 'Password is required.' }) });
  const { register, handleSubmit, formState: { errors } } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });

  const [postLogin, { isLoading }] = usePostLoginMutation();
  const onSubmit = async (data: any) => {
    try {
      await postLogin({ username: user_details?.email ?? '', ...data }).unwrap();
      toast.success('Unlock successful');
      navigate(route, { replace: true });
    } catch (err: any) {
      const { non_field_errors, username } = err.data || {};
      toast.error(
        (Array.isArray(non_field_errors) && non_field_errors[0]) ||
        (Array.isArray(username) && username[0]) ||
        'Something went wrong!'
      );
    }
  };

  return (
    <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8">
        {/* Logo */}
        <div className="flex justify-center mt-6">
          <img src={Logo} alt="Logo" className="h-16 w-auto" />
        </div>

        {/* Header */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-1">Hey There!</h2>
          <p className="text-gray-500 text-sm mb-6">
            Welcome back {fullnames}, re-enter your password to continue.
          </p>
        </div>

        {/* Lock Form */}
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label htmlFor="password" className="block text-sm text-gray-700 mb-1">Password</label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter your password"
                className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
                {...register('password')}
              />
              <button
                type="button"
                onClick={() => setShowPassword(prev => !prev)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                tabIndex={-1}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
          >
            {isLoading ? (
              <span className="flex items-center justify-center gap-2">
                <Loader className="animate-spin" size={22} />
                UNLOCKING...
              </span>
            ) : (
              'UNLOCK'
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default LockScreenPage;
