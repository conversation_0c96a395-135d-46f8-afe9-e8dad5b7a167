import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  Branch, 
  RevenueCenter, 
  Workstation, 
  TaxClass, 
  Printer,
  BranchFilters,
  RevenueCenterFilters,
  WorkstationFilters,
  TaxClassFilters,
  PrinterFilters
} from '@/types/pos';

interface POSState {
  // Data
  branches: Branch[];
  revenueCenters: RevenueCenter[];
  workstations: Workstation[];
  taxClasses: TaxClass[];
  printers: Printer[];
  
  // Loading states
  loading: {
    branches: boolean;
    revenueCenters: boolean;
    workstations: boolean;
    taxClasses: boolean;
    printers: boolean;
  };
  
  // Error states
  errors: {
    branches: string | null;
    revenueCenters: string | null;
    workstations: string | null;
    taxClasses: string | null;
    printers: string | null;
  };
  
  // Filters
  filters: {
    branches: BranchFilters;
    revenueCenters: RevenueCenterFilters;
    workstations: WorkstationFilters;
    taxClasses: TaxClassFilters;
    printers: PrinterFilters;
  };
  
  // Selected items
  selectedBranch: Branch | null;
  selectedRevenueCenter: RevenueCenter | null;
  selectedWorkstation: Workstation | null;
  selectedTaxClass: TaxClass | null;
  selectedPrinter: Printer | null;
}

interface POSActions {
  // Branch actions
  setBranches: (branches: Branch[]) => void;
  addBranch: (branch: Branch) => void;
  updateBranch: (id: string, updates: Partial<Branch>) => void;
  removeBranch: (id: string) => void;
  setSelectedBranch: (branch: Branch | null) => void;
  setBranchLoading: (loading: boolean) => void;
  setBranchError: (error: string | null) => void;
  setBranchFilters: (filters: Partial<BranchFilters>) => void;
  
  // Revenue Center actions
  setRevenueCenters: (revenueCenters: RevenueCenter[]) => void;
  addRevenueCenter: (revenueCenter: RevenueCenter) => void;
  updateRevenueCenter: (id: string, updates: Partial<RevenueCenter>) => void;
  removeRevenueCenter: (id: string) => void;
  setSelectedRevenueCenter: (revenueCenter: RevenueCenter | null) => void;
  setRevenueCenterLoading: (loading: boolean) => void;
  setRevenueCenterError: (error: string | null) => void;
  setRevenueCenterFilters: (filters: Partial<RevenueCenterFilters>) => void;
  
  // Workstation actions
  setWorkstations: (workstations: Workstation[]) => void;
  addWorkstation: (workstation: Workstation) => void;
  updateWorkstation: (id: string, updates: Partial<Workstation>) => void;
  removeWorkstation: (id: string) => void;
  setSelectedWorkstation: (workstation: Workstation | null) => void;
  setWorkstationLoading: (loading: boolean) => void;
  setWorkstationError: (error: string | null) => void;
  setWorkstationFilters: (filters: Partial<WorkstationFilters>) => void;
  
  // Tax Class actions
  setTaxClasses: (taxClasses: TaxClass[]) => void;
  addTaxClass: (taxClass: TaxClass) => void;
  updateTaxClass: (id: string, updates: Partial<TaxClass>) => void;
  removeTaxClass: (id: string) => void;
  setSelectedTaxClass: (taxClass: TaxClass | null) => void;
  setTaxClassLoading: (loading: boolean) => void;
  setTaxClassError: (error: string | null) => void;
  setTaxClassFilters: (filters: Partial<TaxClassFilters>) => void;
  
  // Printer actions
  setPrinters: (printers: Printer[]) => void;
  addPrinter: (printer: Printer) => void;
  updatePrinter: (id: string, updates: Partial<Printer>) => void;
  removePrinter: (id: string) => void;
  setSelectedPrinter: (printer: Printer | null) => void;
  setPrinterLoading: (loading: boolean) => void;
  setPrinterError: (error: string | null) => void;
  setPrinterFilters: (filters: Partial<PrinterFilters>) => void;
  
  // Utility actions
  clearAllErrors: () => void;
  resetFilters: () => void;
  clearSelections: () => void;
}

type POSStore = POSState & POSActions;

const initialState: POSState = {
  branches: [],
  revenueCenters: [],
  workstations: [],
  taxClasses: [],
  printers: [],
  
  loading: {
    branches: false,
    revenueCenters: false,
    workstations: false,
    taxClasses: false,
    printers: false,
  },
  
  errors: {
    branches: null,
    revenueCenters: null,
    workstations: null,
    taxClasses: null,
    printers: null,
  },
  
  filters: {
    branches: {},
    revenueCenters: {},
    workstations: {},
    taxClasses: {},
    printers: {},
  },
  
  selectedBranch: null,
  selectedRevenueCenter: null,
  selectedWorkstation: null,
  selectedTaxClass: null,
  selectedPrinter: null,
};

export const usePOSStore = create<POSStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // Branch actions
        setBranches: (branches) => set({ branches }),
        addBranch: (branch) => set((state) => ({ branches: [...state.branches, branch] })),
        updateBranch: (id, updates) => set((state) => ({
          branches: state.branches.map(branch => 
            branch.id === id ? { ...branch, ...updates } : branch
          )
        })),
        removeBranch: (id) => set((state) => ({
          branches: state.branches.filter(branch => branch.id !== id)
        })),
        setSelectedBranch: (branch) => set({ selectedBranch: branch }),
        setBranchLoading: (loading) => set((state) => ({
          loading: { ...state.loading, branches: loading }
        })),
        setBranchError: (error) => set((state) => ({
          errors: { ...state.errors, branches: error }
        })),
        setBranchFilters: (filters) => set((state) => ({
          filters: { ...state.filters, branches: { ...state.filters.branches, ...filters } }
        })),
        
        // Revenue Center actions
        setRevenueCenters: (revenueCenters) => set({ revenueCenters }),
        addRevenueCenter: (revenueCenter) => set((state) => ({ 
          revenueCenters: [...state.revenueCenters, revenueCenter] 
        })),
        updateRevenueCenter: (id, updates) => set((state) => ({
          revenueCenters: state.revenueCenters.map(rc => 
            rc.id === id ? { ...rc, ...updates } : rc
          )
        })),
        removeRevenueCenter: (id) => set((state) => ({
          revenueCenters: state.revenueCenters.filter(rc => rc.id !== id)
        })),
        setSelectedRevenueCenter: (revenueCenter) => set({ selectedRevenueCenter: revenueCenter }),
        setRevenueCenterLoading: (loading) => set((state) => ({
          loading: { ...state.loading, revenueCenters: loading }
        })),
        setRevenueCenterError: (error) => set((state) => ({
          errors: { ...state.errors, revenueCenters: error }
        })),
        setRevenueCenterFilters: (filters) => set((state) => ({
          filters: { ...state.filters, revenueCenters: { ...state.filters.revenueCenters, ...filters } }
        })),
        
        // Workstation actions
        setWorkstations: (workstations) => set({ workstations }),
        addWorkstation: (workstation) => set((state) => ({ 
          workstations: [...state.workstations, workstation] 
        })),
        updateWorkstation: (id, updates) => set((state) => ({
          workstations: state.workstations.map(ws =>
            ws.workstation_id === id ? { ...ws, ...updates } : ws
          )
        })),
        removeWorkstation: (id) => set((state) => ({
          workstations: state.workstations.filter(ws => ws.workstation_id !== id)
        })),
        setSelectedWorkstation: (workstation) => set({ selectedWorkstation: workstation }),
        setWorkstationLoading: (loading) => set((state) => ({
          loading: { ...state.loading, workstations: loading }
        })),
        setWorkstationError: (error) => set((state) => ({
          errors: { ...state.errors, workstations: error }
        })),
        setWorkstationFilters: (filters) => set((state) => ({
          filters: { ...state.filters, workstations: { ...state.filters.workstations, ...filters } }
        })),
        
        // Tax Class actions
        setTaxClasses: (taxClasses) => set({ taxClasses }),
        addTaxClass: (taxClass) => set((state) => ({ 
          taxClasses: [...state.taxClasses, taxClass] 
        })),
        updateTaxClass: (id, updates) => set((state) => ({
          taxClasses: state.taxClasses.map(tc => 
            tc.id === id ? { ...tc, ...updates } : tc
          )
        })),
        removeTaxClass: (id) => set((state) => ({
          taxClasses: state.taxClasses.filter(tc => tc.id !== id)
        })),
        setSelectedTaxClass: (taxClass) => set({ selectedTaxClass: taxClass }),
        setTaxClassLoading: (loading) => set((state) => ({
          loading: { ...state.loading, taxClasses: loading }
        })),
        setTaxClassError: (error) => set((state) => ({
          errors: { ...state.errors, taxClasses: error }
        })),
        setTaxClassFilters: (filters) => set((state) => ({
          filters: { ...state.filters, taxClasses: { ...state.filters.taxClasses, ...filters } }
        })),
        
        // Printer actions
        setPrinters: (printers) => set({ printers }),
        addPrinter: (printer) => set((state) => ({ 
          printers: [...state.printers, printer] 
        })),
        updatePrinter: (id, updates) => set((state) => ({
          printers: state.printers.map(p => 
            p.id === id ? { ...p, ...updates } : p
          )
        })),
        removePrinter: (id) => set((state) => ({
          printers: state.printers.filter(p => p.id !== id)
        })),
        setSelectedPrinter: (printer) => set({ selectedPrinter: printer }),
        setPrinterLoading: (loading) => set((state) => ({
          loading: { ...state.loading, printers: loading }
        })),
        setPrinterError: (error) => set((state) => ({
          errors: { ...state.errors, printers: error }
        })),
        setPrinterFilters: (filters) => set((state) => ({
          filters: { ...state.filters, printers: { ...state.filters.printers, ...filters } }
        })),
        
        // Utility actions
        clearAllErrors: () => set({
          errors: {
            branches: null,
            revenueCenters: null,
            workstations: null,
            taxClasses: null,
            printers: null,
          }
        }),
        
        resetFilters: () => set({
          filters: {
            branches: {},
            revenueCenters: {},
            workstations: {},
            taxClasses: {},
            printers: {},
          }
        }),
        
        clearSelections: () => set({
          selectedBranch: null,
          selectedRevenueCenter: null,
          selectedWorkstation: null,
          selectedTaxClass: null,
          selectedPrinter: null,
        }),
      }),
      {
        name: 'pos-store',
        partialize: (state) => ({
          // Only persist filters and selections, not the actual data
          filters: state.filters,
          selectedBranch: state.selectedBranch,
          selectedRevenueCenter: state.selectedRevenueCenter,
          selectedWorkstation: state.selectedWorkstation,
          selectedTaxClass: state.selectedTaxClass,
          selectedPrinter: state.selectedPrinter,
        }),
      }
    ),
    { name: 'pos-store' }
  )
);

// Selector hooks for better performance
export const useBranches = () => usePOSStore((state) => state.branches);
export const useRevenueCenters = () => usePOSStore((state) => state.revenueCenters);
export const useWorkstations = () => usePOSStore((state) => state.workstations);
export const useTaxClasses = () => usePOSStore((state) => state.taxClasses);
export const usePrinters = () => usePOSStore((state) => state.printers);

export const usePOSLoading = () => usePOSStore((state) => state.loading);
export const usePOSErrors = () => usePOSStore((state) => state.errors);
export const usePOSFilters = () => usePOSStore((state) => state.filters);

export const useSelectedBranch = () => usePOSStore((state) => state.selectedBranch);
export const useSelectedRevenueCenter = () => usePOSStore((state) => state.selectedRevenueCenter);
export const useSelectedWorkstation = () => usePOSStore((state) => state.selectedWorkstation);
export const useSelectedTaxClass = () => usePOSStore((state) => state.selectedTaxClass);
export const useSelectedPrinter = () => usePOSStore((state) => state.selectedPrinter);
