import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle } from "lucide-react";

import { CheckCard } from "./CheckCard";
import { GuestCheck } from "./types/types";

interface GuestChecksTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  activeChecks: GuestCheck[];
  closedChecks: GuestCheck[];
  onCloseCheck: (checkId: string) => void;
  onVoidItem: (checkId: string, itemIndex: number) => void;
  onSplitCheck: (check: GuestCheck) => void;
  onViewCheck: (check: GuestCheck) => void;
}

export const GuestChecksTabs: React.FC<GuestChecksTabsProps> = ({
  activeTab,
  setActiveTab,
  activeChecks,
  closedChecks,
  onCloseCheck,
  onVoidItem,
  onSplitCheck,
  onViewCheck,
}) => (
  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
    <TabsList className="grid w-full grid-cols-3 bg-white/70 backdrop-blur-sm p-1 h-12">
      <TabsTrigger
        value="active"
        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
      >
        Active ({activeChecks.length})
      </TabsTrigger>
      <TabsTrigger
        value="closed"
        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-green-600 data-[state=active]:text-white"
      >
        Closed ({closedChecks.length})
      </TabsTrigger>
      <TabsTrigger
        value="archive"
        className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-gray-500 data-[state=active]:to-gray-600 data-[state=active]:text-white"
      >
        Archive
      </TabsTrigger>
    </TabsList>
    <TabsContent value="active" className="mt-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
        {activeChecks.map((check) => (
          <CheckCard
            key={check.id}
            check={check}
            onCloseCheck={onCloseCheck}
            onVoidItem={onVoidItem}
            onSplitCheck={() => onSplitCheck(check)}
            onViewCheck={() => onViewCheck(check)}
          />
        ))}
      </div>
    </TabsContent>
    <TabsContent value="closed" className="mt-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
        {closedChecks.map((check) => (
          <CheckCard key={check.id} check={check} showActions={false} />
        ))}
      </div>
    </TabsContent>
    <TabsContent value="archive" className="mt-6">
      <Card className="bg-white/70 backdrop-blur-sm border-0">
        <CardContent className="p-12 text-center">
          <div className="text-gray-500 space-y-3">
            <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center mx-auto">
              <AlertCircle className="h-8 w-8 text-white" />
            </div>
            <p className="text-xl font-medium">No archived checks</p>
            <p className="text-sm">Checks older than 30 days will appear here</p>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  </Tabs>
);