import Logo from "@/assets/logo.png";
import { toast } from "@/components/custom/Toast/MyToast";
import { usePostLoginMutation } from "@/redux/slices/auth";
import { useAuthHook } from "@/utils/useAuthHook";
import { <PERSON>, EyeOff, Loader, Lock, Play } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import useLockScreenStore from "@/zustand/useLockScreenStore";
import { useNavigate } from "react-router-dom";

const ResetPage = () => {
  useEffect(() => {
    // Replace history so back button doesn't return to previous page
    window.history.replaceState(
      null,
      "",
      window.location.pathname + window.location.search
    );
    const handlePopState = (e: PopStateEvent) => {
      // Always push user forward if they try to go back
      window.history.go(1);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  // zustand
  const navigate = useNavigate();
  const route = useLockScreenStore((state) => state.route);

  // pass
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  // user
  const { user_details } = useAuthHook();
  const email = user_details?.email || "";
  const fullnames = user_details?.fullnames || "";

  // form
  const formSchema = z.object({
    password: z.string().min(1, { message: "Password is required." }),
    confirmPassword: z.string().min(1, { message: "Confirm Password is required." }),
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match.",
    path: ["confirmPassword"],
  });

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });
  const [postLogin, { isLoading, error }] = usePostLoginMutation();
  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await postLogin({ username: email, ...values }).unwrap();
      toast.success("Login successful");
      navigate(route, { replace: true });
      // console.log('Login successful:', response);
    } catch (err: any) {
      console.error("Login failed:", err);
      const { data } = err;
      const { non_field_errors, username } = data;
      toast.error(
        (Array.isArray(non_field_errors) && non_field_errors[0]) ||
          (Array.isArray(username) && username[0]) ||
          "Something went wrong!"
      );
      // console.error('Login failed:', err);
    }
  }

  return (
   <>
  <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
    <div className="w-full max-w-md flex flex-col items-center gap-8">
      {/* Logo */}
      <div className="flex justify-center mt-6 mb-2">
        <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
      </div>

      {/* Header */}
      <div className="text-center w-full">
        <h2 className="text-2xl font-bold text-gray-900 mb-1">Reset Your Password?</h2>
        <p className="text-gray-500 text-sm mb-6">
          Let’s lock in a fresh start
        </p>
      </div>

      {/* Reset Password Form */}
      <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
        {/* Password */}
        <div className="mb-4">
          <label className="block text-sm text-gray-700 mb-1" htmlFor="password">Password</label>
          <div className="relative">
            <input
              {...register("password")}
              id="password"
              type={showPassword ? "text" : "password"}
              placeholder="Enter your password"
              autoComplete="off"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-500 text-xs mt-1">{errors.password.message}</p>
          )}
        </div>

        {/* Confirm Password */}
        <div className="mb-6">
          <label className="block text-sm text-gray-700 mb-1" htmlFor="confirmPassword">Confirm Password</label>
          <div className="relative">
            <input
              {...register("confirmPassword")}
              id="confirmPassword"
              type={showConfirm ? "text" : "password"}
              placeholder="Enter your password"
              autoComplete="off"
              className="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-red-500 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowConfirm(!showConfirm)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
              tabIndex={-1}
            >
              {showConfirm ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-red-500 text-xs mt-1">{errors.confirmPassword.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? (
            <span className='flex items-center justify-center gap-2'>
              <Loader className="animate-spin" size={22} />
              Resetting...
            </span>
          ) : (
            <span>Reset Password</span>
          )}
        </button>
      </form>
    </div>
  </div>
</>

  );
};

export default ResetPage;

