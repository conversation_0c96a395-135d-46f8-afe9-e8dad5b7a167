import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Plus,
  Search,
  Users,
  Crown,
  Edit,
  Trash2,
  UserPlus,
  Settings,
  ArrowLeft,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Screen } from "@/app-components/layout/screen";

// Mock data for user groups
const mockGroups = [
  {
    id: 1,
    name: "Administrators",
    description: "Full system access and administrative privileges",
    memberCount: 3,
    groupHead: {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
      avatar: "/api/placeholder/40/40",
    },
    permissions: ["all_permissions"],
    members: [
      { id: 1, name: "John Doe", email: "<EMAIL>" },
      { id: 2, name: "Admin User", email: "<EMAIL>" },
    ],
  },
  {
    id: 2,
    name: "Telemarketers",
    description: "Telemarketing staff with prospect management access",
    memberCount: 15,
    groupHead: {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      avatar: "/api/placeholder/40/40",
    },
    permissions: ["view_prospects", "create_prospects", "edit_prospects", "call_prospects"],
    members: [
      { id: 2, name: "Jane Smith", email: "<EMAIL>" },
      { id: 3, name: "Bob Wilson", email: "<EMAIL>" },
    ],
  },
  {
    id: 3,
    name: "Marketers",
    description: "Marketing team with campaign and content management",
    memberCount: 8,
    groupHead: {
      id: 3,
      name: "Mike Johnson",
      email: "<EMAIL>",
      avatar: "/api/placeholder/40/40",
    },
    permissions: ["view_campaigns", "create_campaigns", "edit_campaigns", "view_analytics"],
    members: [
      { id: 3, name: "Mike Johnson", email: "<EMAIL>" },
      { id: 4, name: "Sarah Davis", email: "<EMAIL>" },
    ],
  },
  {
    id: 4,
    name: "Digital",
    description: "Digital marketing specialists and content creators",
    memberCount: 5,
    groupHead: {
      id: 4,
      name: "Sarah Wilson",
      email: "<EMAIL>",
      avatar: "/api/placeholder/40/40",
    },
    permissions: ["view_analytics", "create_content", "edit_content", "social_media"],
    members: [
      { id: 4, name: "Sarah Wilson", email: "<EMAIL>" },
      { id: 5, name: "Alex Brown", email: "<EMAIL>" },
    ],
  },
];

export default function UserGroups() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newGroup, setNewGroup] = useState({
    name: "",
    description: "",
  });
  const navigate = useNavigate();

  const filteredGroups = mockGroups.filter((group) =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateGroup = () => {
    // Handle group creation logic here
    console.log("Creating group:", newGroup);
    setIsCreateModalOpen(false);
    setNewGroup({ name: "", description: "" });
  };

  return (
    <Screen>
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/admin/users">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">User Groups</h1>
            <p className="text-muted-foreground">
              Manage user groups and their permissions
            </p>
          </div>
        </div>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Group
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Group</DialogTitle>
              <DialogDescription>
                Create a new user group with specific permissions and access levels.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="group-name">Group Name</Label>
                <Input
                  id="group-name"
                  placeholder="e.g., Sales Team"
                  value={newGroup.name}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="group-description">Description</Label>
                <Textarea
                  id="group-description"
                  placeholder="Describe the group's purpose and responsibilities..."
                  value={newGroup.description}
                  onChange={(e) => setNewGroup(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateGroup}>Create Group</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockGroups.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockGroups.reduce((sum, group) => sum + group.memberCount, 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Group Heads</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockGroups.filter(group => group.groupHead).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Groups Table */}
      <Card>
        <CardHeader>
          <CardTitle>Groups List</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Group Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Group Head</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredGroups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>
                    <div className="font-medium">{group.name}</div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate">{group.description}</div>
                  </TableCell>
                  <TableCell>
                    {group.groupHead ? (
                      <div className="flex items-center space-x-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={group.groupHead.avatar} />
                          <AvatarFallback>
                            {group.groupHead.name.split(" ").map(n => n[0]).join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium">{group.groupHead.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {group.groupHead.email}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <Badge variant="secondary">No Head Assigned</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{group.memberCount} members</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{group.permissions.length} permissions</Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/users/groups/${group.id}`)}
                        >
                          <Users className="mr-2 h-4 w-4" />
                          View Members
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/users/groups/${group.id}/edit`)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Group
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/users/groups/${group.id}/head`)}
                        >
                          <Crown className="mr-2 h-4 w-4" />
                          Change Group Head
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => navigate(`/admin/users/groups/${group.id}/add-member`)}
                        >
                          <UserPlus className="mr-2 h-4 w-4" />
                          Add Members
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Group
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
    </Screen>
  );
}