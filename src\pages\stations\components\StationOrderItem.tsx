import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus, Trash2, Edit3, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect, useState } from "react";
import { useLazyGetModifierMenusQuery } from "@/redux/slices/menuitemmodifier";

type Props = {
  item: any;
  noteText: string;
  calculateItemTotal: (item: any) => number;
  updateQuantity: (itemId: string, delta: number) => void;
  setOrderItems: React.Dispatch<React.SetStateAction<any[]>>;
  setNoteText: React.Dispatch<React.SetStateAction<string>>;
};

// Mock extras data
const availableExtras = [
  { id: "extra1", label: "Extra Sugar", price: 0.5 },
  { id: "extra2", label: "Extra Cream", price: 0.75 },
  { id: "extra3", label: "Extra Shot", price: 1.0 },
  { id: "extra4", label: "Caramel Syrup", price: 0.8 },
  { id: "extra5", label: "Vanilla Syrup", price: 0.8 },
];

const StationOrderItem = ({
  item,
  noteText,
  calculateItemTotal,
  updateQuantity,
  setOrderItems,
  setNoteText,
}: Props) => {
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const [
    getItemModifiers,
    { data: modifiers, isLoading: mdLoading, isFetching: mdFetching },
  ] = useLazyGetModifierMenusQuery();

  const handleFetchExtras = async () => {
    await getItemModifiers({ menu_item: item.id }).unwrap();
  };

  const handleAddNote = (itemId: string) => {
    setOrderItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, notes: noteText } : item
      )
    );
    setNoteText("");
    setSelectedItemId(null); // Close the modal by resetting selectedItemId
  };

  const handleAddExtras = (
    itemId: string,
    extra: { label: string; price: string; id: string },
    checked: boolean
  ) => {
    setOrderItems((prev) =>
      prev.map((item) => {
        if (item.id === itemId) {
          const currentExtras = item.extras || [];
          if (checked) {
            return {
              ...item,
              unit_price: item.price,
              extras: [...currentExtras, extra],
              price: item.price + extra?.price,
            };
          } else {
            return {
              ...item,
              unit_price: item.price,
              extras: currentExtras.filter((e: any) => e.id !== extra?.id),
              price: parseFloat(item.price) - parseFloat(extra?.price),
            };
          }
        }
        return item;
      })
    );
  };

  return (
    <div className="flex flex-col gap-3 mb-4 p-4 border border-border/50 rounded-lg hover:shadow-lg transition-all duration-300 bg-card/50 backdrop-blur-sm">
      <div className="flex justify-between items-center">
        <span className="font-bold text-sm text-foreground">{item.name}</span>
        <span className="text-primary text-sm font-bold bg-primary/10 px-2 py-1 rounded-md">
          Ksh. {calculateItemTotal(item)}
        </span>
      </div>

      <div className="flex items-center gap-3 flex-wrap">
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, -1)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-destructive/50 hover:text-destructive"
          >
            <Minus className="h-3 w-3" />
          </Button>
          <Input
            type="number"
            value={item.quantity}
            onChange={(e) => {
              const newQuantity = parseInt(e.target.value) || 0;
              updateQuantity(item.id, newQuantity - item.quantity);
            }}
            className="w-16 text-center h-8 border-border/50 bg-background/50"
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => updateQuantity(item.id, 1)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-primary/50 hover:text-primary"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>

        {item.notes && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded-md border border-border/30">
            <span className="font-medium">Note:</span> {item.notes}
          </div>
        )}

        {item.extras && item.extras.length > 0 && (
          <div className="text-xs text-muted-foreground bg-secondary/10 p-2 rounded-md border border-secondary/20">
            <span className="font-medium">Extras:</span>{" "}
            {item.extras
              .map((extra: any) => `${extra?.label} (+Ksh. ${extra?.price})`)
              .join(", ")}
          </div>
        )}

        <div className="flex gap-2 flex-wrap">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleFetchExtras()}
                className="h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-secondary/50 hover:text-secondary"
              >
                <PlusCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Extras</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground">
                  Add {item?.name} Extras
                </DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                {mdLoading || mdFetching ? (
                  <div className="text-center text-muted-foreground">
                    Loading extras...
                  </div>
                ) : modifiers?.data?.total_data < 1 ? (
                  <div className="text-center text-muted-foreground">
                    No extras available
                  </div>
                ) : (
                  modifiers?.data?.results?.map((extra: any) => (
                    <div
                      key={extra?.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <Checkbox
                        id={extra?.id}
                        checked={item?.extras?.some(
                          (e: any) => e.id === extra?.id
                        )}
                        onCheckedChange={(checked) => {
                          handleAddExtras(
                            item.id,
                            {
                              label: extra?.name,
                              price: extra?.price,
                              id: extra?.id,
                            },
                            checked as boolean
                          );
                        }}
                        className="border-border/50"
                      />
                      <Label
                        htmlFor={extra?.id}
                        className="text-foreground cursor-pointer flex-1"
                      >
                        {extra?.name}
                        <span className="text-primary font-semibold ml-2">
                          (+ Ksh. {extra?.price})
                        </span>
                      </Label>
                    </div>
                  ))
                )}
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedItemId(item.id);
                  setNoteText(item.notes || "");
                }}
                className="h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-blue-500/50 hover:text-blue-600"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                <span className="text-xs">Note</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground">Add Note</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Textarea
                  placeholder="Add special instructions..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  className="min-h-[100px] border-border/50 bg-background/50"
                />
                <DialogFooter className="flex flex-wrap gap-3 justify-end">
                  <DialogClose asChild>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setNoteText("");
                        setSelectedItemId(null);
                      }}
                      className="ml-2 hover:scale-105 transition-all duration-200"
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    onClick={() => handleAddNote(item.id)}
                    className="hover:scale-105 transition-all duration-200"
                  >
                    Save Note
                  </Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>

          <Button
            size="sm"
            variant="destructive"
            onClick={() => updateQuantity(item.id, -item.quantity)}
            className="h-8 w-8 p-0 hover:scale-110 transition-all duration-200 shadow-md"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StationOrderItem;
