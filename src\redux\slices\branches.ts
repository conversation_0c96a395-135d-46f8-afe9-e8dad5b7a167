import { apiSlice } from "../apiSlice";

// Types for Branch API
export interface Branch {
  id?: number;
  branch_code: string;
  name: string;
  location: string;
  timezone?: string;
  currency?: string;
  language?: string;
  is_active?: boolean;
}

export const branchApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all branches with optional filtering
    getBranches: builder.query<Branch[], {
      is_active?: boolean;
      search?: string;
      include_revenue_centers?: boolean;
      include_workstations?: boolean;
    }>({
      query: (params = {}) => ({
        url: "/setup/branches",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        // Debug: Log the raw response
        console.log("Raw branches API response:", response);

        // Handle different response structures
        if (Array.isArray(response)) {
          console.log("Response is direct array:", response);
          return response;
        } else if (response && response.data) {
          console.log("Response has data property:", response.data);
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (
            response.data.branches &&
            Array.isArray(response.data.branches)
          ) {
            return response.data.branches;
          } else if (
            response.data.results &&
            Array.isArray(response.data.results)
          ) {
            return response.data.results;
          } else {
            // If data is an object but not an array, wrap it in an array
            console.log("Wrapping single object in array:", response.data);
            return [response.data];
          }
        } else if (
          response &&
          response.results &&
          Array.isArray(response.results)
        ) {
          console.log("Response has results property:", response.results);
          return response.results;
        } else if (
          response &&
          response.branches &&
          Array.isArray(response.branches)
        ) {
          console.log("Response has branches property:", response.branches);
          return response.branches;
        } else {
          console.warn("Unexpected API response structure:", response);
          console.log("Returning empty array as fallback");
          return [];
        }
      },
      providesTags: ["Branches"],
    }),

    retrieveBranch: builder.query<Branch, string>({
      query: (id) => ({
        url: `/setup/branches/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Branches", id }],
    }),

    addBranch: builder.mutation<Branch, Partial<Branch>>({
      query: (payload) => ({
        url: "/setup/branches",
        method: "POST",
        body: payload,
      }),
      transformResponse: (response: any) => {
        console.log("Create branch response:", response);
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      invalidatesTags: ["Branches"],
    }),

    patchBranch: builder.mutation<
      Branch,
      { id: string; data: Partial<Branch> }
    >({
      query: ({ id, data }) => ({
        url: `/setup/branches/${id}`,
        method: "PATCH",
        body: data,
      }),
      transformResponse: (response: any) => {
        console.log("Update branch response:", response);
        if (response && response.data) {
          return response.data;
        }
        return response;
      },
      invalidatesTags: (result, error, { id }) => [
        { type: "Branches", id },
        "Branches",
      ],
    }),

    deleteBranch: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/branches/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Branches"],
    }),
  }),
});

export const {
  useGetBranchesQuery,
  useRetrieveBranchQuery,
  useAddBranchMutation,
  usePatchBranchMutation,
  useDeleteBranchMutation,

  useLazyGetBranchesQuery,
  useLazyRetrieveBranchQuery,
} = branchApiSlice;
