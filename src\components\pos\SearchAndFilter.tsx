import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter } from 'lucide-react';

interface FilterOption {
  label: string;
  value: string;
  component: React.ReactNode;
}

interface SearchAndFilterProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  title?: string;
  description?: string;
  filters?: FilterOption[];
  onFilterClick?: () => void;
  className?: string;
}

const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchValue,
  onSearchChange,
  searchPlaceholder = 'Search...',
  title = 'Search & Filter',
  description = 'Find items by name, code, or other criteria',
  filters = [],
  onFilterClick,
  className
}) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {filters.map((filter) => (
            <div key={filter.value}>
              {filter.component}
            </div>
          ))}
          
          {onFilterClick && (
            <Button variant="outline" onClick={onFilterClick}>
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchAndFilter;
