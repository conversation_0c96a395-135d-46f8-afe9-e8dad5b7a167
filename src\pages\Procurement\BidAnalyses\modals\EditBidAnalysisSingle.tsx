import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Award, FileText } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { 
  useUpdateBidAnalysisMutation,
  useGetRFQResponsesQuery 
} from "@/redux/slices/procurement";

interface EditBidAnalysisSingleProps {
  open: boolean;
  onClose: () => void;
  bidAnalysis: any;
  onSuccess?: () => void;
}

interface SingleBidAnalysisFormData {
  selected_responses: string;
  recommendation_notes: string;
  finalized_at: string;
}

const EditBidAnalysisSingle: React.FC<EditBidAnalysisSingleProps> = ({
  open,
  onClose,
  bidAnalysis,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<SingleBidAnalysisFormData>({
    selected_responses: "",
    recommendation_notes: "",
    finalized_at: "",
  });

  // API hooks
  const [updateBidAnalysis, { isLoading }] = useUpdateBidAnalysisMutation();
  const { data: rfqResponsesData } = useGetRFQResponsesQuery({
    rfq: bidAnalysis?.rfq
  });

  // Initialize form data when bidAnalysis changes
  useEffect(() => {
    if (bidAnalysis) {
      setFormData({
        selected_responses: bidAnalysis.selected_responses || "",
        recommendation_notes: bidAnalysis.recommendation_notes || "",
        finalized_at: bidAnalysis.finalized_at || "",
      });
    }
  }, [bidAnalysis]);

  const handleInputChange = (field: keyof SingleBidAnalysisFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.selected_responses) {
      toast.error("Please select a response");
      return;
    }

    if (!formData.recommendation_notes.trim()) {
      toast.error("Recommendation notes are required");
      return;
    }

    try {
      const payload = {
        selected_responses: formData.selected_responses,
        recommendation_notes: formData.recommendation_notes,
        finalized_at: formData.finalized_at || null,
      };

      await updateBidAnalysis({ 
        id: bidAnalysis.id, 
        ...payload 
      }).unwrap();

      toast.success("Bid Analysis updated successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating bid analysis:", error);
      toast.error(error?.data?.message || "Failed to update bid analysis");
    }
  };

  const handleFinalize = async () => {
    if (!formData.selected_responses) {
      toast.error("Please select a response before finalizing");
      return;
    }

    if (window.confirm("Finalize this bid analysis? This action cannot be undone.")) {
      try {
        const payload = {
          selected_responses: formData.selected_responses,
          recommendation_notes: formData.recommendation_notes,
          finalized_at: new Date().toISOString(),
        };

        await updateBidAnalysis({ 
          id: bidAnalysis.id, 
          ...payload 
        }).unwrap();

        toast.success("Bid Analysis finalized successfully");
        onSuccess?.();
        onClose();
      } catch (error: any) {
        console.error("Error finalizing bid analysis:", error);
        toast.error(error?.data?.message || "Failed to finalize bid analysis");
      }
    }
  };

  // Get available responses for the RFQ
  const availableResponses = rfqResponsesData?.results?.filter((response: any) => 
    response.rfq === bidAnalysis?.rfq
  ) || [];

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Edit Bid Analysis - Single Award ({bidAnalysis?.code})
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Bid Analysis Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Bid Analysis Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Code:</span> {bidAnalysis?.code}
              </div>
              <div>
                <span className="font-medium">RFQ:</span> {bidAnalysis?.rfq}
              </div>
              <div>
                <span className="font-medium">Award Type:</span> Single Award
              </div>
              <div>
                <span className="font-medium">Status:</span>{" "}
                <span className={bidAnalysis?.finalized_at ? "text-green-600" : "text-orange-600"}>
                  {bidAnalysis?.finalized_at ? "Finalized" : "Draft"}
                </span>
              </div>
            </div>
          </div>

          {/* Selected Response */}
          <div>
            <Label htmlFor="selected_responses">Selected Response *</Label>
            <Select
              value={formData.selected_responses}
              onValueChange={(value) => handleInputChange("selected_responses", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select winning response" />
              </SelectTrigger>
              <SelectContent>
                {availableResponses.length > 0 ? (
                  availableResponses.map((response: any) => (
                    <SelectItem key={response.code} value={response.code}>
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{response.code}</div>
                          <div className="text-sm text-gray-500">
                            Supplier: {response.supplier_name || response.supplier}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-responses" disabled>
                    No responses available for this RFQ
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-1">
              Select the winning response for this single award bid analysis.
            </p>
          </div>

          {/* Recommendation Notes */}
          <div>
            <Label htmlFor="recommendation_notes">Recommendation Notes *</Label>
            <Textarea
              id="recommendation_notes"
              value={formData.recommendation_notes}
              onChange={(e) => handleInputChange("recommendation_notes", e.target.value)}
              placeholder="Enter your recommendation notes and justification for the selected response..."
              rows={4}
              required
            />
          </div>

          {/* Finalized At */}
          <div>
            <Label htmlFor="finalized_at">Finalized At</Label>
            <input
              id="finalized_at"
              type="datetime-local"
              value={formData.finalized_at ? new Date(formData.finalized_at).toISOString().slice(0, 16) : ""}
              onChange={(e) => handleInputChange("finalized_at", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!!bidAnalysis?.finalized_at}
            />
            <p className="text-sm text-gray-500 mt-1">
              {bidAnalysis?.finalized_at 
                ? "This bid analysis has been finalized and cannot be changed."
                : "Leave empty to keep as draft, or use the Finalize button below."
              }
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            
            <div className="flex space-x-2">
              {!bidAnalysis?.finalized_at && (
                <Button 
                  type="button" 
                  variant="default"
                  onClick={handleFinalize}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Finalize Analysis
                </Button>
              )}
              
              <Button type="submit" disabled={isLoading || !!bidAnalysis?.finalized_at}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditBidAnalysisSingle;
