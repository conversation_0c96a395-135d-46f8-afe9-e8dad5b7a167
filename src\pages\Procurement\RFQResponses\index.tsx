import { Screen } from "@/app-components/layout/screen";
import { useState } from "react";
import {
  useGetRFQResponsesQuery,
  useSubmitRFQResponseMutation,
  useDeleteRFQResponseMutation,
} from "@/redux/slices/procurement";
import ViewRFQResponse from "./modals/ViewRFQResponse";
import EditRFQResponseComplete from "./modals/EditRFQResponseComplete";
import { ColumnDef } from "@tanstack/react-table";
import { RFQResponse } from "@/types/procurement";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  FileText,
  Building,
  Calendar,
  DollarSign,
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const RFQResponsesIndex = () => {
  const [filters, setFilters] = useState({
    search: "",
  });
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedResponse, setSelectedResponse] = useState<any>(null);

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: rfqResponsesData } = useGetRFQResponsesQuery(apiFilters);

  // Mutation hooks
  const [submitRFQResponse] = useSubmitRFQResponseMutation();
  const [deleteRFQResponse] = useDeleteRFQResponseMutation();

  // Handler functions
  const handleView = (response: any) => {
    setSelectedResponse(response);
    setViewModalOpen(true);
  };

  const handleEdit = (response: any) => {
    setSelectedResponse(response);
    setEditModalOpen(true);
  };

  const handleSubmit = async (response: any) => {
    if (response.submitted_at) {
      toast.error("Response has already been submitted");
      return;
    }

    try {
      await submitRFQResponse(response.id).unwrap();
      toast.success("RFQ response submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit RFQ response");
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this RFQ response?")) {
      try {
        await deleteRFQResponse(id).unwrap();
        toast.success("RFQ response deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete RFQ response");
      }
    }
  };

  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<RFQResponse>[] = [
    {
      accessorKey: "code",
      header: "Response Code",
      cell: ({ row }) => {
        const code = row.getValue("code") as string;
        const id = row.original.id;
        return (
          <div className="flex flex-col">
            <button
              onClick={() => handleView(row.original)}
              className="font-semibold text-blue-600 hover:text-blue-800 hover:underline text-left"
              title="Click to view details"
            >
              {code || `RESP-${String(id).padStart(5, '0')}`}
            </button>
            <span className="text-xs text-gray-500">
              ID: {id}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "rfq",
      header: "RFQ Number",
      cell: ({ row }) => {
        const rfq = row.getValue("rfq") as string;
        return (
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-gray-500" />
            <span className="font-medium">{rfq}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "supplier",
      header: "Supplier",
      cell: ({ row }) => {
        const supplier = row.getValue("supplier") as string;
        return supplier ? (
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-gray-500" />
            <span>{supplier}</span>
          </div>
        ) : (
          <Badge variant="secondary">Not Assigned</Badge>
        );
      },
    },
    {
      accessorKey: "submitted_at",
      header: "Submitted At",
      cell: ({ row }) => {
        const submittedAt = row.getValue("submitted_at") as string;
        return submittedAt ? (
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span>{new Date(submittedAt).toLocaleDateString()}</span>
          </div>
        ) : (
          <Badge variant="outline">Not Submitted</Badge>
        );
      },
    },
    {
      accessorKey: "submitted_by",
      header: "Submitted By",
      cell: ({ row }) => {
        const submittedBy = row.getValue("submitted_by") as string;
        return submittedBy || <Badge variant="secondary">Not Set</Badge>;
      },
    },
    {
      accessorKey: "response_items",
      header: "Items Count",
      cell: ({ row }) => {
        const items = row.getValue("response_items") as any[];
        return (
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-gray-500" />
            <span>{items?.length || 0} items</span>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const response = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(response)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              
              <DropdownMenuItem onClick={() => handleEdit(response)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Response
              </DropdownMenuItem>

              {!response.submitted_at && (
                <DropdownMenuItem
                  onClick={() => handleSubmit(response)}
                  className="text-green-600"
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Submit Response
                </DropdownMenuItem>
              )}

              <DropdownMenuItem
                onClick={() => handleDelete(response.id!)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">RFQ Responses</h1>
            <p className="text-gray-600 mt-1">Manage and track RFQ responses from suppliers</p>
          </div>
        </div>

        {/* Summary Cards */}
        {rfqResponsesData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Responses</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {rfqResponsesData.total_data || 0}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Submitted</p>
                  <p className="text-2xl font-bold text-green-600">
                    {rfqResponsesData.results?.filter((r: any) => r.submitted_at).length || 0}
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-green-500" />
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Draft</p>
                  <p className="text-2xl font-bold text-yellow-600">
                    {rfqResponsesData.results?.filter((r: any) => !r.submitted_at).length || 0}
                  </p>
                </div>
                <Edit className="h-8 w-8 text-yellow-500" />
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Suppliers</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {(() => {
                      console.log("RFQ Responses data for suppliers count:", rfqResponsesData.results);
                      const suppliers = rfqResponsesData.results?.map((r: any) => {
                        console.log("Response item:", r);
                        // Look for supplier code, ID, or name - any non-null value
                        return r.supplier || r.supplier_id || r.supplier_code || r.supplier_name;
                      }).filter(Boolean);
                      console.log("Extracted suppliers:", suppliers);
                      return new Set(suppliers).size || 0;
                    })()}
                  </p>
                </div>
                <Building className="h-8 w-8 text-purple-500" />
              </div>
            </div>
          </div>
        )}

        {/* Data Table */}
        <DataTable<RFQResponse>
          data={rfqResponsesData?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={filters.search}
              name="searchInput"
              type="search"
              onChange={(e) => handleSearch(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search RFQ responses..."
            />
          }
        />

        {/* View RFQ Response Modal */}
        {viewModalOpen && selectedResponse && (
          <ViewRFQResponse
            open={viewModalOpen}
            onClose={() => {
              setViewModalOpen(false);
              setSelectedResponse(null);
            }}
            response={selectedResponse}
            onSuccess={() => {
              // Trigger a refetch of the data
              // The query will automatically refetch due to cache invalidation
            }}
          />
        )}

        {/* Edit RFQ Response Modal */}
        {editModalOpen && selectedResponse && (
          <EditRFQResponseComplete
            open={editModalOpen}
            onClose={() => {
              setEditModalOpen(false);
              setSelectedResponse(null);
            }}
            response={selectedResponse}
            onSuccess={() => {
              // Trigger a refetch of the data
              // The query will automatically refetch due to cache invalidation
            }}
          />
        )}
      </div>
    </Screen>
  );
};

export default RFQResponsesIndex;
