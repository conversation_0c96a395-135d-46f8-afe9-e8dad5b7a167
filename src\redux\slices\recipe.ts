import { apiSlice } from "../apiSlice";

export const recipeApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRecipes: builder.query({
      query: (params) => ({
        url: "/menu/recipes",
        method: "GET",
        params: params,
      }),
      providesTags: ["Recipes"],
    }),

    retrieveRecipe: builder.query({
      query: (id) => ({
        url: `/menu/recipes/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Recipes", id }],
    }),

    addRecipe: builder.mutation({
      query: (payload) => ({
        url: "/menu/recipes",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Recipes"],
    }),

    patchRecipe: builder.mutation({
      query: (payload) => ({
        url: `/menu/recipes/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Recipes", id },
        "Recipes",
      ],
    }),

    deleteRecipe: builder.mutation({
      query: (id) => ({
        url: `/menu/recipes/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Recipes"],
    }),
  }),
});

export const {
  useGetRecipesQuery,
  useRetrieveRecipeQuery,
  useAddRecipeMutation,
  usePatchRecipeMutation,
  useDeleteRecipeMutation,

  useLazyGetRecipesQuery,
  useLazyRetrieveRecipeQuery,
} = recipeApiSlice;
