import React from "react";


import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

interface MealCardProps {
  title: string;
  description: string;
  imageUrl: string;
  price: number;
  onDrag? : ()=> void;
  // Optional: Add more props like onAddToCart, onViewDetails for interactivity
  onAddToCart?: () => void;
  onViewDetails?: () => void;
}

function FoodItem({
  title,
  description,
  imageUrl,
  price,
  onAddToCart,
  onViewDetails,
}: MealCardProps) {
  return (
    <Card className="w-full max-w-[350px] mx-auto">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="relative h-[200px] w-full">
          
        </div>
        <p className="mt-4 text-lg font-semibold">
          ${isNaN(price) ? "0.00" : price.toFixed(2)} {/* Handle invalid price */}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between">
        <PrimaryButton
          onClick={onAddToCart}
          aria-label={`Add ${title} to cart`}
          disabled={!onAddToCart} // Disable if no handler is provided
        >
          Add to Cart
        </PrimaryButton>
        <PrimaryButton
          variant="outline"
          onClick={onViewDetails}
          aria-label={`View details for ${title}`}
          disabled={!onViewDetails} // Disable if no handler is provided
        >
          View Details
        </PrimaryButton>
      </CardFooter>
    </Card>
  );
}

export default FoodItem;