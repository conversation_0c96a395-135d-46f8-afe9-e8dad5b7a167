import { apiSlice } from "../apiSlice";

export const recipeGroupApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getRecipeGroups: builder.query({
      query: (params) => ({
        url: "/menu/recipe-groups",
        method: "GET",
        params: params,
      }),
      providesTags: ["RecipeGroups"],
    }),

    retrieveRecipeGroup: builder.query({
      query: (id) => ({
        url: `/menu/recipe-groups/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "RecipeGroups", id }],
    }),

    addRecipeGroup: builder.mutation({
      query: (payload) => ({
        url: "/menu/recipe-groups",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RecipeGroups"],
    }),

    patchRecipeGroup: builder.mutation({
      query: (payload) => ({
        url: `/menu/recipe-groups/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RecipeGroups", id },
        "RecipeGroups",
      ],
    }),

    deleteRecipeGroup: builder.mutation({
      query: (id) => ({
        url: `/menu/recipe-groups/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RecipeGroups"],
    }),
  }),
});

export const {
  useGetRecipeGroupsQuery,
  useRetrieveRecipeGroupQuery,
  useAddRecipeGroupMutation,
  usePatchRecipeGroupMutation,
  useDeleteRecipeGroupMutation,

  useLazyGetRecipeGroupsQuery,
  useLazyRetrieveRecipeGroupQuery,
} = recipeGroupApiSlice;
