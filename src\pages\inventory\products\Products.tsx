import React, { useState, useMemo } from 'react';
import {
    Search,
    Plus,
    Filter,
    Eye,
    Edit,
    XCircle,
    Package,
    ShoppingCart,
    CheckCircle,
    AlertCircle,
    X,
    ChefHat
} from 'lucide-react';
import { Product, ProductFormData } from './product.type';
import { mockProducts, mockCategories, mockUnitsOfMeasure, mockSuppliers } from './mockData';
import { ProductForm } from './ProductForm';
import { ProductDetailView } from './ProductDetailView';
import { Screen } from '@/app-components/layout/screen';

function Products() {
    const [currentView, setCurrentView] = useState<string>('products');
    const [products, setProducts] = useState<Product[]>(mockProducts);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('All');
    const [perishableFilter, setPerishableFilter] = useState('All');
    const [statusFilter, setStatusFilter] = useState('All');
    const [showAddModal, setShowAddModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [editingProduct, setEditingProduct] = useState<Product | undefined>();
    const [viewingProduct, setViewingProduct] = useState<Product | undefined>();
    const [showFilters, setShowFilters] = useState(false);

    const categories = ['All', ...mockCategories.map(c => c.name)];

    const filteredProducts = useMemo(() => {
        return products.filter(product => {
            const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                product.code.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
            const matchesPerishable = perishableFilter === 'All' ||
                (perishableFilter === 'Perishable' && product.perishable) ||
                (perishableFilter === 'Non-Perishable' && !product.perishable);
            const matchesStatus = statusFilter === 'All' ||
                (statusFilter === 'Active' && product.active) ||
                (statusFilter === 'Inactive' && !product.active);

            return matchesSearch && matchesCategory && matchesPerishable && matchesStatus;
        });
    }, [products, searchTerm, selectedCategory, perishableFilter, statusFilter]);

    const handleSaveProduct = (productData: ProductFormData) => {
        if (editingProduct) {
            // Update existing product
            const updatedProduct: Product = {
                ...editingProduct,
                name: productData.name,
                code: productData.code,
                category: productData.category,
                unitOfMeasure: productData.unitOfMeasure,
                cost: parseFloat(productData.cost),
                taxCode: productData.taxCode,
                perishable: productData.perishable,
                shelfLifeDays: productData.shelfLifeDays ? parseInt(productData.shelfLifeDays) : undefined,
                expiryDate: productData.expiryDate || undefined,
                defaultSupplier: productData.defaultSupplier || undefined,
                active: productData.active,
                updatedAt: new Date().toISOString()
            };

            setProducts(prev => prev.map(p => p.id === editingProduct.id ? updatedProduct : p));
            setShowEditModal(false);
            setEditingProduct(undefined);
        } else {
            // Create new product
            const newProduct: Product = {
                id: (products.length + 1).toString(),
                name: productData.name,
                code: productData.code,
                category: productData.category,
                unitOfMeasure: productData.unitOfMeasure,
                cost: parseFloat(productData.cost),
                taxCode: productData.taxCode,
                perishable: productData.perishable,
                shelfLifeDays: productData.shelfLifeDays ? parseInt(productData.shelfLifeDays) : undefined,
                expiryDate: productData.expiryDate || undefined,
                defaultSupplier: productData.defaultSupplier || undefined,
                active: productData.active,
                stockLevel: 0,
                minStockLevel: 10,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            setProducts(prev => [...prev, newProduct]);
            setShowAddModal(false);
        }
    };

    const handleEditProduct = (product: Product) => {
        setEditingProduct(product);
        setShowEditModal(true);
    };

    const handleViewProduct = (product: Product) => {
        setViewingProduct(product);
        setShowDetailModal(true);
    };

    const handleEditFromDetail = () => {
        if (viewingProduct) {
            setEditingProduct(viewingProduct);
            setShowDetailModal(false);
            setShowEditModal(true);
        }
    };

    const handleToggleProductStatus = (productId: string) => {
        setProducts(prev => prev.map(p =>
            p.id === productId ? { ...p, active: !p.active, updatedAt: new Date().toISOString() } : p
        ));
    };

    const getStockStatus = (product: Product) => {
        if (!product.stockLevel || !product.minStockLevel) return 'unknown';
        if (product.stockLevel <= product.minStockLevel) return 'low';
        if (product.stockLevel <= product.minStockLevel * 1.5) return 'medium';
        return 'good';
    };

    const StockIndicator = ({ product }: { product: Product }) => {
        const status = getStockStatus(product);
        const colors = {
            good: 'bg-green-100 text-green-800',
            medium: 'bg-yellow-100 text-yellow-800',
            low: 'bg-red-100 text-red-800',
            unknown: 'bg-gray-100 text-gray-800'
        };

        return (
            <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${colors[status]}`}>
                    {product.stockLevel || 'N/A'}
                </span>
                {status === 'low' && <AlertCircle className="w-4 h-4 text-red-500" />}
            </div>
        );
    };

    return (
        <Screen>
            <div className="min-h-screen">
                <div className="max-w-7xl mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                                <Package className="w-8 h-8 text-blue-600" />
                                <h1 className="text-3xl font-bold text-gray-900">Product Master</h1>
                            </div>
                        </div>
                        <p className="text-gray-600">Manage all consumables and ingredients in your inventory</p>
                    </div>

                    {/* Controls */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                        <div className="p-6">
                            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                                <div className="flex-1 max-w-md">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                        <input
                                            type="text"
                                            placeholder="Search products by name or code..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        />
                                    </div>
                                </div>

                                <div className="flex gap-3">
                                    <button
                                        onClick={() => setShowFilters(!showFilters)}
                                        className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                                    >
                                        <Filter className="w-4 h-4" />
                                        Filters
                                    </button>
                                    <button
                                        onClick={() => setShowAddModal(true)}
                                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                    >
                                        <Plus className="w-4 h-4" />
                                        Add Product
                                    </button>
                                </div>
                            </div>

                            {/* Filters */}
                            {showFilters && (
                                <div className="mt-6 pt-6 border-t border-gray-200">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Category
                                            </label>
                                            <select
                                                value={selectedCategory}
                                                onChange={(e) => setSelectedCategory(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                {categories.map(category => (
                                                    <option key={category} value={category}>{category}</option>
                                                ))}
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Perishable Status
                                            </label>
                                            <select
                                                value={perishableFilter}
                                                onChange={(e) => setPerishableFilter(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                <option value="All">All</option>
                                                <option value="Perishable">Perishable</option>
                                                <option value="Non-Perishable">Non-Perishable</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Status
                                            </label>
                                            <select
                                                value={statusFilter}
                                                onChange={(e) => setStatusFilter(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            >
                                                <option value="All">All</option>
                                                <option value="Active">Active</option>
                                                <option value="Inactive">Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Results Summary */}
                    <div className="mb-4 flex justify-between items-center">
                        <p className="text-sm text-gray-600">
                            Showing {filteredProducts.length} of {products.length} products
                        </p>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span>Good Stock</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                <span>Medium Stock</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                <span>Low Stock</span>
                            </div>
                        </div>
                    </div>

                    {/* Products Table */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Product Info
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Category
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit & Cost
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Stock
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Tax
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Supplier
                                        </th>
                                        <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {filteredProducts.map((product) => (
                                        <tr key={product.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{product.name}</div>
                                                    <div className="text-sm text-gray-500">{product.code}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {product.category}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm text-gray-900">{product.unitOfMeasure}</div>
                                                <div className="text-sm font-medium text-gray-900">Ksh{product.cost.toFixed(2)}</div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="space-y-1">
                                                    <div className="flex items-center gap-2">
                                                        {product.active ? (
                                                            <CheckCircle className="w-4 h-4 text-green-500" />
                                                        ) : (
                                                            <XCircle className="w-4 h-4 text-red-500" />
                                                        )}
                                                        <span className="text-sm">
                                                            {product.active ? 'Active' : 'Inactive'}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${product.perishable ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                                                            }`}>
                                                            {product.perishable ? 'Perishable' : 'Non-Perishable'}
                                                        </span>
                                                    </div>


                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <StockIndicator product={product} />
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="flex items-center gap-2">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${product.taxCode === 'VAT' ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800'
                                                        }`}>
                                                        {product.taxCode}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                {product.defaultSupplier && (
                                                    <div className="text-xs text-gray-500">
                                                        Supplier: {product.defaultSupplier}
                                                    </div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        className="p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded"
                                                        title="View"
                                                        onClick={() => handleViewProduct(product)}
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        className="p-1 text-green-600 hover:text-green-900 hover:bg-green-50 rounded"
                                                        title="Edit"
                                                        onClick={() => handleEditProduct(product)}
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        className={`p-1 rounded ${product.active
                                                            ? 'text-red-600 hover:text-red-900 hover:bg-red-50'
                                                            : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                                                            }`}
                                                        title={product.active ? 'Deactivate' : 'Activate'}
                                                        onClick={() => handleToggleProductStatus(product.id)}
                                                    >
                                                        <XCircle className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {filteredProducts.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                            <ShoppingCart className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                            <p>No products found matching your criteria.</p>
                        </div>
                    )}
                </div>

                {showAddModal && (
                    <ProductForm
                        categories={mockCategories}
                        unitsOfMeasure={mockUnitsOfMeasure}
                        suppliers={mockSuppliers}
                        onSave={handleSaveProduct}
                        onCancel={() => setShowAddModal(false)}
                    />
                )}

                {showEditModal && editingProduct && (
                    <ProductForm
                        product={editingProduct}
                        categories={mockCategories}
                        unitsOfMeasure={mockUnitsOfMeasure}
                        suppliers={mockSuppliers}
                        onSave={handleSaveProduct}
                        onCancel={() => {
                            setShowEditModal(false);
                            setEditingProduct(undefined);
                        }}
                        isEditing={true}
                    />
                )}

                {showDetailModal && viewingProduct && (
                    <ProductDetailView
                        product={viewingProduct}
                        onClose={() => {
                            setShowDetailModal(false);
                            setViewingProduct(undefined);
                        }}
                        onEdit={handleEditFromDetail}
                    />
                )}
            </div>
        </Screen>
    );
}

export default Products;