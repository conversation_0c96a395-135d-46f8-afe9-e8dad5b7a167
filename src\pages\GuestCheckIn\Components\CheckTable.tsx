// src/components/guest-checks/CheckTable.tsx
import React from 'react';

import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatCurrency } from '../util/formarter';

interface Item {
  name: string;
  price: number;
  qty: number;
}

interface CheckTableProps {
  items: Item[];
  showActions?: boolean;
  onVoidItem?: (index: number) => void;
}

const CheckTable: React.FC<CheckTableProps> = ({ items, showActions, onVoidItem }) => (
  <div className="max-h-32 overflow-y-auto">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-xs">Item</TableHead>
          <TableHead className="text-xs text-right">Qty</TableHead>
          <TableHead className="text-xs text-right">Price</TableHead>
          {showActions && <TableHead className="text-xs text-right">Action</TableHead>}
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item, index) => (
          <TableRow key={index} className="text-sm">
            <TableCell>{item.name}</TableCell>
            <TableCell className="text-right">{item.qty}</TableCell>
            <TableCell className="text-right">{formatCurrency(item.price * item.qty)}</TableCell>
            {showActions && (
              <TableCell className="text-right">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onVoidItem?.(index)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </TableCell>
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
);

export default CheckTable;