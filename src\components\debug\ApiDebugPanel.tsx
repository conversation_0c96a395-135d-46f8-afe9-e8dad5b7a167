import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetSuppliersQuery } from '@/redux/slices/suppliers';

const ApiDebugPanel: React.FC = () => {
  const [showDebug, setShowDebug] = useState(false);
  
  const { 
    data: branches, 
    isLoading: branchesLoading, 
    error: branchesError,
    refetch: refetchBranches 
  } = useGetBranchesQuery({});
  
  const { 
    data: suppliers, 
    isLoading: suppliersLoading, 
    error: suppliersError,
    refetch: refetchSuppliers 
  } = useGetSuppliersQuery({});

  if (!showDebug) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => setShowDebug(true)}
        >
          🐛 Debug API
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96">
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            API Debug Panel
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowDebug(false)}
            >
              ✕
            </Button>
          </CardTitle>
          <CardDescription>
            Real-time API status and data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Branches Section */}
          <div className="border rounded p-3">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-semibold">Branches API</h4>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => refetchBranches()}
                disabled={branchesLoading}
              >
                🔄
              </Button>
            </div>
            <div className="text-sm space-y-1">
              <div>Status: {branchesLoading ? '🟡 Loading' : branchesError ? '🔴 Error' : '🟢 Success'}</div>
              <div>Count: {Array.isArray(branches) ? branches.length : 'N/A'}</div>
              <div>Type: {typeof branches}</div>
              {branchesError && (
                <div className="text-red-600 text-xs">
                  Error: {JSON.stringify(branchesError, null, 2)}
                </div>
              )}
              {branches && (
                <details className="text-xs">
                  <summary>Data Preview</summary>
                  <pre className="mt-1 p-2 bg-gray-100 rounded overflow-auto max-h-32">
                    {JSON.stringify(branches, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </div>

          {/* Suppliers Section */}
          <div className="border rounded p-3">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-semibold">Suppliers API</h4>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => refetchSuppliers()}
                disabled={suppliersLoading}
              >
                🔄
              </Button>
            </div>
            <div className="text-sm space-y-1">
              <div>Status: {suppliersLoading ? '🟡 Loading' : suppliersError ? '🔴 Error' : '🟢 Success'}</div>
              <div>Count: {Array.isArray(suppliers) ? suppliers.length : 'N/A'}</div>
              <div>Type: {typeof suppliers}</div>
              {suppliersError && (
                <div className="text-red-600 text-xs">
                  Error: {JSON.stringify(suppliersError, null, 2)}
                </div>
              )}
              {suppliers && (
                <details className="text-xs">
                  <summary>Data Preview</summary>
                  <pre className="mt-1 p-2 bg-gray-100 rounded overflow-auto max-h-32">
                    {JSON.stringify(suppliers, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </div>

          {/* Environment Info */}
          <div className="border rounded p-3">
            <h4 className="font-semibold mb-2">Environment</h4>
            <div className="text-sm space-y-1">
              <div>Mode: {import.meta.env.MODE}</div>
              <div>API URL: {import.meta.env.VITE_API_URL_DEV}</div>
              <div>Prod Mode: {import.meta.env.VITE_PROD}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ApiDebugPanel;
