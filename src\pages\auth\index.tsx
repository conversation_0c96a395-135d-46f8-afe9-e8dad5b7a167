import { Outlet } from "react-router-dom";
import DroneImg from '@/assets/drone.jpg'
import Logo from '@/assets/logo.png';

type Props = {};

const AuthIndex = ({ }: Props) => {

  return (
    <div className="min-h-screen bg-black flex">
      {/* Left Panel - Image Background */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        {/* Image Background */}
        <img
          src={DroneImg}
          alt="Drone view"
          className="absolute inset-0 w-full h-full object-cover"
        />

        {/* Image Overlay */}
        <div className="absolute inset-0 bg-black/20"></div>

        {/* Logo/Brand on image */}
        <div className="absolute top-6 left-6 z-10 bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded">
          <img src={Logo} alt="GMC Logo" className="w-32 h-auto" />
        </div>

        {/* Fallback background for when image is loading */}
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 via-red-500 to-teal-600 -z-10"></div>
      </div>

      <Outlet />

    </div>
  );
};

export default AuthIndex;


// import logo from "../../assets/logo.png";
// const AuthIndex = ({}: Props) => {
//   return (
//     <div className="flex flex-col items-center justify-start h-screen bg-gray-200 relative p-2 ">
//       <div className="w-full h-2/5 rounded-lg">
//         <img
//           src={loginBg}
//           alt="GMC login bg"
//           className="w-full h-full object-cover rounded-xl"
//         />
//       </div>
//       <div className="p-3 bg-white  dark:bg-[#152238] md:w-1/3 sm:w-4/5 min-h-[400px] absolute top-[25%] rounded-xl">
//         <div className="flex items-center justify-center py-4">
//           <img src={logo} alt="GMC Logo" />
//         </div>
//         <Outlet />

//         <div className="w-full text-center mt-4">
//           <Link to="/" className=" text-center text-xs hover:underline">
//             Navigate Home
//           </Link>
//         </div>
//       </div>
//     </div>
//   );
// };