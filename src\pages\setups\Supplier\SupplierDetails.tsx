import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useRetrieveSupplierQuery } from "@/redux/slices/suppliers";
import React from "react";
import { useLocation } from "react-router-dom";
import SupplierSideBar from "./components/SupplierSideBar";
import { supplierTestData } from "./supplierTestData";
import { Edit } from "lucide-react";

const SupplierDetails = () => {
  const location = useLocation();
  const supplierId = location.pathname.split("/")[2];

  const {
    data: supplier,
    isLoading: loading,
    isError,
  } = useRetrieveSupplierQuery(supplierId);

  if (loading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-[calc(100vh-100px)]">
          <SpinnerTemp type="spinner-double" size="md" />
        </div>
      </Screen>
    );
  }

  // if (isError) {
  //   return (
  //     <Screen>
  //       <div className="p-4">
  //         <Alert variant="destructive">
  //           <AlertDescription>
  //             Error loading propect details. Please try again later.
  //           </AlertDescription>
  //         </Alert>
  //       </div>
  //     </Screen>
  //   );
  // }

  return (
    <Screen>
      <div className="min-h-screen flex flex-col">
        <div className="flex flex-1 md:flex-row flex-col">
          <div className="w-full md:w-72 xl:w-72 border-r shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            {supplier && <SupplierSideBar supplier={supplier} />}
          </div>

          <div className=" flex flex-1 flex-col bg-gray-50 md:h-[calc(100vh-44px)] overflow-hidden">
            <div className="flex-1 overflow-auto dark:bg-gray-900">
              <div className="p-4 md:p-6 mx-auto space-y-4">
                {/* Body sections */}

                {/* bank info  */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold mb-6">Bank Details</h2>
                    <Edit />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        Bank Name
                      </label>
                      <p className="font-medium">
                        {supplier?.bank_details?.bank_name || "N/A"}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        Account Name
                      </label>
                      <p className="font-medium">
                        {supplier?.bank_details?.account_name || "N/A"}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        Account Number
                      </label>
                      <p className="font-medium">
                        {supplier?.bank_details?.account_number || "N/A"}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        SWIFT/IBAN
                      </label>
                      <p className="font-medium">
                        {supplier?.bank_details?.swift_iban || "N/A"}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        Currency
                      </label>
                      <p className="font-medium">
                        {supplier?.bank_details?.currency || "N/A"}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm text-gray-500 dark:text-gray-400">
                        Mobile Payment Details
                      </label>
                      <div className="mt-1 space-y-1">
                        <p className="text-sm">
                          Till:{" "}
                          {supplier?.bank_details?.mobile_payment_details
                            ?.till || "N/A"}
                        </p>
                        <p className="text-sm">
                          Paybill:{" "}
                          {supplier?.bank_details?.mobile_payment_details
                            ?.paybill || "N/A"}
                        </p>
                        <p className="text-sm">
                          Phone:{" "}
                          {supplier?.bank_details?.mobile_payment_details
                            ?.phone || "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Transactions Table */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                  <h2 className="text-xl font-semibold mb-6">Transactions</h2>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-4 py-3 text-left">Date</th>
                          <th className="px-4 py-3 text-left">Reference</th>
                          <th className="px-4 py-3 text-left">Description</th>
                          <th className="px-4 py-3 text-right">Amount</th>
                          <th className="px-4 py-3 text-left">Status</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        <tr className="text-gray-500 dark:text-gray-400">
                          <td className="px-4 py-3">No transactions found</td>
                          <td></td>
                          <td></td>
                          <td></td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* <SupplierDetailsBody prospect_id={prospect_id} /> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default SupplierDetails;
