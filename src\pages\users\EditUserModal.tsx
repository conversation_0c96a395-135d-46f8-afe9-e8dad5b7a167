import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { AlertCircle } from "lucide-react";
import { usePatchUserMutation } from "@/redux/slices/user"; // <-- import your mutation
import { useToast } from "@/hooks/use-toast";

export default function EditUserModal({
  isOpen,
  onOpenChange,
  user,
  onSave,
}: {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  user: any;
  onSave: (updated: any) => void;
}) {
  const [form, setForm] = useState({
    fullnames: user?.fullnames || "",
    email: user?.email || "",
    department: user?.department || "",
    designation: user?.designation || "",
  });

  const [updateUser, { isLoading }] = usePatchUserMutation();
  const { toast } = useToast();

  const handleComplete = async () => {
    try {
      await updateUser({
          id: user.id, ...form,
          user_id: 0
      }).unwrap();
      toast({
        title: "User updated",
        description: "User details have been updated successfully.",
      });
      onSave(form);
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: "Update failed",
        description: error?.data?.message || "Could not update user.",
        variant: "destructive",
      });
    }
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Edit User"
      description="Update user details"
      currentStep={0}
      onStepChange={() => {}}
      onComplete={handleComplete}
      steps={[
        {
          title: "Basic Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={form.fullnames}
                  onChange={e => setForm(f => ({ ...f, fullnames: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={form.email}
                  onChange={e => setForm(f => ({ ...f, email: e.target.value }))}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Details",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  value={form.department}
                  onChange={e => setForm(f => ({ ...f, department: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="designation">Designation</Label>
                <Input
                  id="designation"
                  value={form.designation}
                  onChange={e => setForm(f => ({ ...f, designation: e.target.value }))}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Ready to Save?</h3>
              <p className="text-muted-foreground mt-2">
                Please review and confirm your changes.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}