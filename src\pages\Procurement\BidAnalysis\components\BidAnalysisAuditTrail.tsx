import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Clock, 
  User, 
  FileText, 
  CheckCircle, 
  XCircle, 
  UserCheck,
  Edit,
  Crown,
  Package
} from "lucide-react";
import { useGetBidAnalysisAuditTrailQuery } from "@/redux/slices/procurement";

interface BidAnalysisAuditTrailProps {
  bidAnalysisId: number;
}

const BidAnalysisAuditTrail = ({ bidAnalysisId }: BidAnalysisAuditTrailProps) => {
  const { data: auditTrail, isLoading } = useGetBidAnalysisAuditTrailQuery(bidAnalysisId);

  const getActionIcon = (action: string) => {
    const iconMap = {
      "Created": FileText,
      "Updated": Edit,
      "Supplier Selected": Crown,
      "Approved": CheckCircle,
      "Rejected": XCircle,
      "Converted": Package,
    };
    
    const IconComponent = iconMap[action as keyof typeof iconMap] || FileText;
    return <IconComponent className="h-4 w-4" />;
  };

  const getActionColor = (action: string) => {
    const colorMap = {
      "Created": "bg-blue-100 text-blue-800",
      "Updated": "bg-yellow-100 text-yellow-800",
      "Supplier Selected": "bg-purple-100 text-purple-800",
      "Approved": "bg-green-100 text-green-800",
      "Rejected": "bg-red-100 text-red-800",
      "Converted": "bg-indigo-100 text-indigo-800",
    };
    
    return colorMap[action as keyof typeof colorMap] || "bg-gray-100 text-gray-800";
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const auditEntries = auditTrail?.data || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Audit Trail
        </CardTitle>
      </CardHeader>
      <CardContent>
        {auditEntries.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No audit entries found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {auditEntries.map((entry: any, index: number) => {
              const { date, time } = formatTimestamp(entry.timestamp);
              
              return (
                <div key={entry.id || index} className="flex gap-4 p-4 border rounded-lg">
                  {/* Timeline dot */}
                  <div className="flex flex-col items-center">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600">
                      {getActionIcon(entry.action)}
                    </div>
                    {index < auditEntries.length - 1 && (
                      <div className="w-px h-8 bg-gray-200 mt-2"></div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getActionColor(entry.action)}>
                        {entry.action}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {date} at {time}
                      </span>
                    </div>

                    <div className="flex items-center gap-2 mb-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium text-gray-900">
                        {entry.user_name || `User ${entry.user}`}
                      </span>
                    </div>

                    <p className="text-gray-700 mb-3">{entry.description}</p>

                    {/* Additional Details */}
                    {entry.details && (
                      <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                        {entry.details.item_id && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-600">Item ID:</span>
                            <span className="ml-2">{entry.details.item_id}</span>
                          </div>
                        )}
                        
                        {entry.details.supplier_id && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-600">Supplier ID:</span>
                            <span className="ml-2">{entry.details.supplier_id}</span>
                          </div>
                        )}

                        {entry.details.old_value && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-600">Previous Value:</span>
                            <span className="ml-2 text-red-600">
                              {typeof entry.details.old_value === 'object' 
                                ? JSON.stringify(entry.details.old_value)
                                : entry.details.old_value
                              }
                            </span>
                          </div>
                        )}

                        {entry.details.new_value && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-600">New Value:</span>
                            <span className="ml-2 text-green-600">
                              {typeof entry.details.new_value === 'object' 
                                ? JSON.stringify(entry.details.new_value)
                                : entry.details.new_value
                              }
                            </span>
                          </div>
                        )}

                        {entry.details.reason && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-600">Reason:</span>
                            <span className="ml-2">{entry.details.reason}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BidAnalysisAuditTrail;
