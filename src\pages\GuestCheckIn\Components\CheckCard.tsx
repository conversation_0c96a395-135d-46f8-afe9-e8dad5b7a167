import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, User, Clock, CheckCircle, Eye, Split } from "lucide-react";
import { GuestCheck } from "./types/types";


interface CheckCardProps {
  check: GuestCheck;
  onCloseCheck?: (checkId: string) => void;
  onVoidItem?: (checkId: string, itemIndex: number) => void;
  onSplitCheck?: () => void;
  onViewCheck?: () => void;
  showActions?: boolean;
}

export const CheckCard: React.FC<CheckCardProps> = ({
  check,
  onCloseCheck,
  onSplitCheck,
  onViewCheck,
  showActions = true,
}) => {
  const formatTime = (timeString: string) =>
    new Date(timeString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-emerald-100 text-emerald-700 border-emerald-200";
      case "closed":
        return "bg-gray-100 text-gray-700 border-gray-200";
      default:
        return "bg-blue-100 text-blue-700 border-blue-200";
    }
  };

  return (
    <Card className="group hover:shadow-2xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm hover:bg-white/90">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-red-500 via-yellow-400 to-orange-500 rounded-lg">
              <MapPin className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-bold text-gray-800">{check.tableNumber}</CardTitle>
              <p className="text-sm text-gray-500 flex items-center mt-1">
                <User className="h-3 w-3 mr-1" />
                {check.guestCount} guests
              </p>
            </div>
          </div>
          <Badge className={`${getStatusColor(check.status)} font-medium`}>{check.status}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center py-2 px-3 bg-orange-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-orange-600" />
            <span className="text-sm font-medium text-orange-700">{check.waiterName}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-500">{formatTime(check.orderTime)}</span>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-orange-600">Subtotal:</span>
            <span className="font-medium text-orange-700">${check.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-yellow-600">Tax:</span>
            <span className="font-medium text-yellow-700">${check.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-red-600">Service:</span>
            <span className="font-medium text-red-700">${check.serviceCharge.toFixed(2)}</span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between font-bold text-lg">
              <span>Total:</span>
              <span className="text-gradient bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 bg-clip-text text-transparent">${check.total.toFixed(2)}</span>
            </div>
          </div>
        </div>
        {showActions && (
          <div className="flex space-x-2 pt-3 border-t">
            <Button
              size="sm"
              variant="outline"
              className="flex-1 hover:bg-orange-50 hover:border-orange-300 text-orange-700"
              onClick={onViewCheck}
            >
              <Eye className="h-4 w-4 mr-1 text-orange-500" />
              View
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="flex-1 hover:bg-yellow-50 hover:border-yellow-300 text-yellow-700"
              onClick={onSplitCheck}
            >
              <Split className="h-4 w-4 mr-1 text-yellow-500" />
              Split
            </Button>
            <Button
              size="sm"
              className="flex-1 bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white"
              onClick={() => onCloseCheck?.(check.id)}
            >
              <CheckCircle className="h-4 w-4 mr-1 text-white" />
              Close
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};