import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  useUpdateRFQResponseMutation,
  useGetSuppliersQuery,
} from "@/redux/slices/procurement";

interface EditRFQResponseProps {
  open: boolean;
  onClose: () => void;
  response: any;
  onSuccess?: () => void;
}

const EditRFQResponse = ({ open, onClose, response, onSuccess }: EditRFQResponseProps) => {
  const [formData, setFormData] = useState({
    supplier: "",
    notes: "",
  });

  const [updateRFQResponse, { isLoading }] = useUpdateRFQResponseMutation();
  const { data: suppliers, isLoading: suppliersLoading, error: suppliersError } = useGetSuppliersQuery({});

  // Initialize form data when response changes
  useEffect(() => {
    if (response) {
      setFormData({
        supplier: response.supplier?.toString() || "",
        notes: response.notes || "",
      });
    }
  }, [response]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.supplier) {
      toast.error("Supplier is required");
      return;
    }

    try {
      const payload = {
        id: response.id,
        supplier: parseInt(formData.supplier),
        notes: formData.notes.trim() || undefined,
      };

      await updateRFQResponse(payload).unwrap();
      toast.success("RFQ response updated successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating RFQ response:", error);
      toast.error(error?.data?.message || "Failed to update RFQ response");
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit RFQ Response</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Response Information */}
          {response && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm text-gray-700">Response Details</h4>
              <p className="text-sm text-gray-600">
                RFQ: {response.rfq || 'N/A'}
              </p>
              <p className="text-sm text-gray-600">
                Code: {response.code || `RESP-${String(response.id).padStart(5, '0')}`}
              </p>
              {response.submitted_at && (
                <p className="text-sm text-orange-600 font-medium">
                  ⚠️ This response has been submitted and changes may be limited
                </p>
              )}
            </div>
          )}

          {/* Supplier Selection */}
          <div>
            <Label htmlFor="supplier">Supplier *</Label>
            <Select
              value={formData.supplier}
              onValueChange={(value) => handleInputChange("supplier", value)}
              disabled={response?.submitted_at} // Disable if submitted
            >
              <SelectTrigger>
                <SelectValue placeholder={suppliersLoading ? "Loading suppliers..." : "Select supplier"} />
              </SelectTrigger>
              <SelectContent>
                {suppliersLoading ? (
                  <SelectItem value="loading" disabled>Loading suppliers...</SelectItem>
                ) : suppliersError ? (
                  <SelectItem value="error" disabled>Error loading suppliers</SelectItem>
                ) : suppliers?.results?.length > 0 ? (
                  suppliers.results.map((supplier: any) => (
                    <SelectItem key={supplier.id} value={supplier.id.toString()}>
                      {supplier.name} ({supplier.code})
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="none" disabled>No suppliers available</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Enter any additional notes or comments"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={4}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? "Updating..." : "Update Response"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditRFQResponse;
