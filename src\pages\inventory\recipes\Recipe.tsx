import React, { useState, useMemo } from 'react';
import {
  Search,
  Plus,
  Filter,
  Eye,
  Edit,
  XCircle,
  ChefHat,
  Clock,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Download,
  FileText,
  Users
} from 'lucide-react';
import { Recipe } from './recipe.type';
import { mockRecipeCategories, mockRecipes } from './recipeData';
import { Screen } from '@/app-components/layout/screen';

 const RecipeList: React.FC = () => {
  const [recipes, setRecipes] = useState<Recipe[]>(mockRecipes);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [statusFilter, setStatusFilter] = useState('All');
  const [showFilters, setShowFilters] = useState(false);

  const recipeTypes = ['All', 'Prep', 'Final'];
  const categories = ['All', ...mockRecipeCategories.map(c => c.name)];

  const filteredRecipes = useMemo(() => {
    return recipes.filter(recipe => {
      const matchesSearch = recipe.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = selectedType === 'All' || recipe.type === selectedType;
      const matchesCategory = selectedCategory === 'All' || recipe.category === selectedCategory;
      const matchesStatus = statusFilter === 'All' ||
        (statusFilter === 'Active' && recipe.active) ||
        (statusFilter === 'Inactive' && !recipe.active);

      return matchesSearch && matchesType && matchesCategory && matchesStatus;
    });
  }, [recipes, searchTerm, selectedType, selectedCategory, statusFilter]);

  const handleToggleRecipeStatus = (recipeId: string) => {
    setRecipes(prev => prev.map(r =>
      r.id === recipeId ? { ...r, active: !r.active, updatedAt: new Date().toISOString() } : r
    ));
  };

  const handleExportCSV = () => {
    const csvContent = [
      ['Recipe Name', 'Type', 'Category', 'Portion Size', 'Cost per Portion', 'Status', 'Linked Menu Items'].join(','),
      ...filteredRecipes.map(recipe => [
        recipe.name,
        recipe.type,
        recipe.category,
        `${recipe.portionSize} ${recipe.portionUnit}`,
        `$${recipe.costPerPortion.toFixed(2)}`,
        recipe.active ? 'Active' : 'Inactive',
        recipe.linkedMenuItems?.join('; ') || 'None'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'recipes.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const RecipeTypeIndicator = ({ type }: { type: 'Prep' | 'Final' }) => (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${type === 'Prep' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
      }`}>
      {type}
    </span>
  );

  const LinkedMenuItems = ({ items }: { items?: string[] }) => {
    if (!items || items.length === 0) {
      return <span className="text-gray-400 text-sm">No linked items</span>;
    }

    return (
      <div className="space-y-1">
        {items.slice(0, 2).map((item, index) => (
          <div key={index} className="text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
            {item}
          </div>
        ))}
        {items.length > 2 && (
          <div className="text-xs text-gray-500">
            +{items.length - 2} more
          </div>
        )}
      </div>
    );
  };

  return (
    <Screen>
      <div className="min-h-screen">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-2">
              <ChefHat className="w-8 h-8 text-purple-600" />
              <h1 className="text-3xl font-bold text-gray-900">Recipe Management</h1>
            </div>
            <p className="text-gray-600">Manage prep recipes and final dishes with ingredient costing</p>
          </div>

          {/* Controls */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6">
              <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="Search recipes by name..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    <Filter className="w-4 h-4" />
                    Filters
                  </button>
                  <div className="flex gap-2">
                    <button
                      onClick={handleExportCSV}
                      className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      Export CSV
                    </button>
                    <button className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                      <FileText className="w-4 h-4" />
                      Export PDF
                    </button>
                  </div>
                  <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                    <Plus className="w-4 h-4" />
                    Add New Recipe
                  </button>
                </div>
              </div>

              {/* Filters */}
              {showFilters && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Recipe Type
                      </label>
                      <select
                        value={selectedType}
                        onChange={(e) => setSelectedType(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        {recipeTypes.map(type => (
                          <option key={type} value={type}>{type}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Category/Group
                      </label>
                      <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Status
                      </label>
                      <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="All">All</option>
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Results Summary */}
          <div className="mb-4 flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Showing {filteredRecipes.length} of {recipes.length} recipes
            </p>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Prep Recipes</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>Final Recipes</span>
              </div>
            </div>
          </div>

          {/* Recipes Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Recipe Info
                    </th>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type & Category
                    </th>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Portion & Cost
                    </th>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status & Timing
                    </th>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Linked Menu Items
                    </th>
                    <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredRecipes.map((recipe) => (
                    <tr key={recipe.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{recipe.name}</div>
                          <div className="text-sm text-gray-500">{recipe.description}</div>
                          {recipe.servings && (
                            <div className="flex items-center gap-1 mt-1">
                              <Users className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{recipe.servings} servings</span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          <RecipeTypeIndicator type={recipe.type} />
                          <div className="text-sm text-gray-600">{recipe.category}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="text-sm text-gray-900">
                            {recipe.portionSize} {recipe.portionUnit}
                          </div>
                          <div className="text-lg font-semibold text-green-600">
                            Ksh{recipe.costPerPortion.toFixed(2)}
                          </div>
                          <div className="text-xs text-gray-500">per portion</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            {recipe.active ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                            <span className="text-sm">
                              {recipe.active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          {(recipe.prepTime || recipe.cookTime) && (
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {recipe.prepTime && `${recipe.prepTime}m prep`}
                                {recipe.prepTime && recipe.cookTime && ' + '}
                                {recipe.cookTime && `${recipe.cookTime}m cook`}
                              </span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <LinkedMenuItems items={recipe.linkedMenuItems} />
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <button
                            className="p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded"
                            title="View"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            className="p-1 text-green-600 hover:text-green-900 hover:bg-green-50 rounded"
                            title="Edit"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            className={`p-1 rounded ${recipe.active
                                ? 'text-red-600 hover:text-red-900 hover:bg-red-50'
                                : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                              }`}
                            title={recipe.active ? 'Deactivate' : 'Activate'}
                            onClick={() => handleToggleRecipeStatus(recipe.id)}
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {filteredRecipes.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <ChefHat className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p>No recipes found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </Screen>
  );
};

export default RecipeList