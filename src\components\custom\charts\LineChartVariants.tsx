import { 
  <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  ReferenceLine, Brush, ReferenceArea, Label
} from 'recharts';
import React from 'react';

// Sample data
const data = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: '<PERSON>', value: 600 },
  { name: 'Apr', value: 800 },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 350 },
];

const multiData = [
  { name: 'Jan', uv: 4000, pv: 2400, amt: 2400 },
  { name: 'Feb', uv: 3000, pv: 1398, amt: 2210 },
  { name: 'Mar', uv: 2000, pv: 9800, amt: 2290 },
  { name: 'Apr', uv: 2780, pv: 3908, amt: 2000 },
  { name: 'May', uv: 1890, pv: 4800, amt: 2181 },
  { name: 'Jun', uv: 2390, pv: 3800, amt: 2500 },
];

const dataWithNulls = [
  { name: '<PERSON>', value: 400 },
  { name: 'Feb', value: null },
  { name: '<PERSON>', value: 600 },
  { name: 'Apr', value: null },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 350 },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  strokeDash: "3 3",
  axisLineStyle: { stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 },
  axisTick: { fill: 'hsl(var(--foreground))', fontSize: 12 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Line Chart
export const SimpleLineChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Tiny Line Chart
export const TinyLineChart = () => {
  return (
    <div style={{ ...chartStyles.containerStyle, height: 150 }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        >
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            dot={false}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Dashed Line Chart
export const DashedLineChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="pv"
            stroke="hsl(var(--primary))"
            strokeDasharray="5 5"
            strokeWidth={2}
            animationDuration={1500}
          />
          <Line
            type="monotone"
            dataKey="uv"
            stroke="hsl(var(--destructive))"
            strokeDasharray="3 4 5 2"
            strokeWidth={2}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Vertical Line Chart
export const VerticalLineChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          layout="vertical"
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            dataKey="name" 
            type="category"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Biaxial Line Chart
export const BiaxialLineChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            yAxisId="left"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="pv"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="uv"
            stroke="hsl(var(--destructive))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Vertical Line Chart With Specified Domain
export const VerticalLineChartWithSpecifiedDomain = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          layout="vertical"
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            type="number" 
            domain={[0, 1000]}
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            dataKey="name" 
            type="category"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Line Chart Connect Nulls
export const LineChartConnectNulls = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={dataWithNulls}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            connectNulls={true}
            activeDot={{ r: 8 }}
            animationDuration={1500}
            name="Value (connected nulls)"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Line Chart With X-Axis Padding
export const LineChartWithXAxisPadding = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            padding={{ left: 30, right: 30 }}
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Line Chart With Reference Lines
export const LineChartWithReferenceLines = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <ReferenceLine x="Mar" stroke="hsl(var(--destructive))" label="Month Ref" />
          <ReferenceLine y={500} stroke="hsl(var(--destructive))" label="Target" strokeDasharray="3 3" />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Customized Dot Line Chart
export const CustomizedDotLineChart = () => {
  const renderCustomizedDot = (props) => {
    const { cx, cy, value, index } = props;
    
    if (value > 500) {
      return (
        <svg key={`dot-${index}`} x={cx - 10} y={cy - 10} width={20} height={20} fill="hsl(var(--success))" viewBox="0 0 1024 1024">
          <path d="M512 1009.984c-274.912 0-497.76-222.848-497.76-497.76s222.848-497.76 497.76-497.76c274.912 0 497.76 222.848 497.76 497.76s-222.848 497.76-497.76 497.76zM340.768 295.936c-39.488 0-71.52 32.8-71.52 73.248s32.032 73.248 71.52 73.248c39.488 0 71.52-32.8 71.52-73.248s-32.032-73.248-71.52-73.248zM686.176 296.704c-39.488 0-71.52 32.8-71.52 73.248s32.032 73.248 71.52 73.248c39.488 0 71.52-32.8 71.52-73.248s-32.032-73.248-71.52-73.248zM772.928 555.392c-18.752-8.864-40.928-0.576-49.632 18.528-40.224 88.576-120.256 143.552-208.832 143.552-85.952 0-164.864-52.64-205.952-137.376-9.184-18.912-31.648-26.592-50.08-17.28-18.464 9.408-21.216 21.472-15.936 32.64 52.8 111.424 155.232 186.784 269.76 186.784 117.984 0 217.12-70.944 269.76-186.784 8.672-19.136 9.568-31.2-9.12-40.096z"/>
        </svg>
      );
    }

    return (
      <svg key={`dot-${index}`} x={cx - 10} y={cy - 10} width={20} height={20} fill="hsl(var(--destructive))" viewBox="0 0 1024 1024">
        <path d="M517.12 53.248q95.232 0 179.2 36.352t145.92 98.304 98.304 145.92 36.352 179.2-36.352 179.2-98.304 145.92-145.92 98.304-179.2 36.352-179.2-36.352-145.92-98.304-98.304-145.92-36.352-179.2 36.352-179.2 98.304-145.92 145.92-98.304 179.2-36.352zM663.552 261.12q-15.36 0-28.16 6.656t-23.04 18.432-15.872 27.648-5.632 33.28q0 35.84 21.504 61.44t51.2 25.6 51.2-25.6 21.504-61.44q0-17.408-5.632-33.28t-15.872-27.648-23.04-18.432-28.16-6.656zM373.76 261.12q-29.696 0-50.688 25.088t-20.992 60.928 20.992 61.44 50.688 25.6 50.176-25.6 20.48-61.44-20.48-60.928-50.176-25.088zM520.192 602.112q-51.2 0-97.28 9.728t-82.944 27.648-62.464 41.472-35.84 51.2q-1.024 1.024-1.024 2.048-1.024 3.072-1.024 8.704t2.56 11.776 7.168 11.264 12.8 6.144q25.6-27.648 62.464-50.176 31.744-19.456 79.36-35.328t114.176-15.872q67.584 0 116.736 15.872t81.92 35.328q37.888 22.528 63.488 50.176 17.408-5.12 19.968-18.944t0.512-18.944-3.072-7.168-1.024-3.072q-26.624-29.696-62.976-51.712t-96.256-38.912-129.024-16.896z"/>
      </svg>
    );
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            dot={renderCustomizedDot}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Customized Label Line Chart
export const CustomizedLabelLineChart = () => {
  const renderCustomizedLabel = (props) => {
    const { x, y, value, index } = props;
    
    return (
      <text key={`label-${index}`} x={x} y={y - 15} fill="hsl(var(--foreground))" textAnchor="middle">
        {value}
      </text>
    );
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            label={renderCustomizedLabel}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Synchronized Line Chart
export const SynchronizedLineChart = () => {
  return (
    <div>
      <div style={chartStyles.containerStyle}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={chartStyles.margin}
            syncId="anyId"
          >
            <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
            <XAxis 
              dataKey="name" 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <YAxis 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <Tooltip contentStyle={chartStyles.tooltipStyle} />
            <Line
              type="monotone"
              dataKey="value"
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              activeDot={{ r: 8 }}
              animationDuration={1500}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div style={{ ...chartStyles.containerStyle, marginTop: 10 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={multiData}
            margin={chartStyles.margin}
            syncId="anyId"
          >
            <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
            <XAxis 
              dataKey="name" 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <YAxis 
              axisLine={chartStyles.axisLineStyle}
              tick={chartStyles.axisTick}
            />
            <Tooltip contentStyle={chartStyles.tooltipStyle} />
            <Line
              type="monotone"
              dataKey="pv"
              stroke="hsl(var(--destructive))"
              strokeWidth={2}
              activeDot={{ r: 8 }}
              animationDuration={1500}
            />
            <Brush 
              dataKey="name" 
              height={30} 
              stroke="hsl(var(--muted-foreground))"
              fill="hsl(var(--secondary))"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

// Highlight And Zoom Line Chart
export const HighlightAndZoomLineChart = () => {
  const [refAreaLeft, setRefAreaLeft] = React.useState('');
  const [refAreaRight, setRefAreaRight] = React.useState('');
  const [xAxisDomain, setXAxisDomain] = React.useState(['dataMin', 'dataMax']);
  const [yAxisDomain, setYAxisDomain] = React.useState(['dataMin', 'dataMax']);

  const zoomOut = () => {
    setXAxisDomain(['dataMin', 'dataMax']);
    setYAxisDomain(['dataMin', 'dataMax']);
    setRefAreaLeft('');
    setRefAreaRight('');
  };

  const nameIndices = data.map(item => item.name);

  const zoom = () => {
    if (refAreaLeft === refAreaRight || !refAreaRight) {
      setRefAreaLeft('');
      setRefAreaRight('');
      return;
    }

    // Calculate domain values
    const leftIndex = nameIndices.indexOf(refAreaLeft);
    const rightIndex = nameIndices.indexOf(refAreaRight);
    
    const xSubDomain = [
      nameIndices[Math.min(leftIndex, rightIndex)],
      nameIndices[Math.max(leftIndex, rightIndex)]
    ];

    setXAxisDomain(xSubDomain);
    setRefAreaLeft('');
    setRefAreaRight('');
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={chartStyles.margin}
          onMouseDown={(e) => e && e.activeLabel && setRefAreaLeft(e.activeLabel)}
          onMouseMove={(e) => refAreaLeft && e && e.activeLabel && setRefAreaRight(e.activeLabel)}
          onMouseUp={zoom}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            domain={xAxisDomain}
            allowDataOverflow
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis
            domain={yAxisDomain}
            allowDataOverflow
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
          {refAreaLeft && refAreaRight && (
            <ReferenceArea
              x1={refAreaLeft}
              x2={refAreaRight}
              strokeOpacity={0.3}
              fill="hsl(var(--primary) / 0.3)"
            />
          )}
          <ReferenceLine 
            y={0} 
            stroke="hsl(var(--muted-foreground))" 
            strokeDasharray="3 3"
          >
            <Label 
              value="Double click to reset zoom" 
              position="insideBottomRight" 
              offset={10} 
              className="text-xs text-muted-foreground"
            />
          </ReferenceLine>
        </LineChart>
      </ResponsiveContainer>
      <button 
        onClick={zoomOut} 
        className="mt-2 px-3 py-1 text-sm rounded-md bg-muted text-muted-foreground hover:bg-muted/80"
        onDoubleClick={zoomOut}
      >
        Reset Zoom
      </button>
    </div>
  );
};

// Multi Series Line Chart
export const LineChartHasMultiSeries = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="uv"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
          <Line
            type="monotone"
            dataKey="pv"
            stroke="hsl(var(--destructive))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
          <Line
            type="monotone"
            dataKey="amt"
            stroke="hsl(var(--accent))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Line Chart Axis Interval
export const LineChartAxisInterval = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            interval="preserveEnd"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            interval={1}
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Line
            type="monotone"
            dataKey="uv"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Line Chart With Negative Values
export const LineChartNegativeValuesWithReferenceLines = () => {
  const negData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: -300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: -800 },
    { name: 'May', value: 500 },
    { name: 'Jun', value: -350 },
  ];

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={negData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <ReferenceLine y={0} stroke="hsl(var(--muted-foreground))" strokeDasharray="3 3" />
          <Line
            type="monotone"
            dataKey="value"
            stroke="hsl(var(--primary))"
            strokeWidth={2}
            activeDot={{ r: 8 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
