
import { apiSlice } from "../apiSlice";


export const userApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getUsers: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: "/users/users",
        method: "GET",
        params: params,
      }),
      providesTags: ["Users"],
    }),

    retrieveUser: builder.query<any, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "GET",
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: (result, error, id) => [{ type: "Users", id }],
    }),

    addUser: builder.mutation<any, Partial<any>>({
      query: (payload) => ({
        url: "/users/users",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Users"],
    }),

    patchUser: builder.mutation<
      any,
      { user_id: number } & Partial<any>
    >({
      query: ({ user_id, ...payload }) => ({
        url: `/users/users/${user_id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { user_id }) => [
        { type: "Users", id: user_id },
        "Users",
      ],
    }),

    deleteUser: builder.mutation<any, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Users"],
    }),

    // ── Departments ─────────────────────────────────────────────────────────
    getDepartments: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/departments`,
        method: "GET",
        params,
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
    getDepartment: builder.query<any, number>({
      query: (dp_id) => ({
        url: `/users/departments/${dp_id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Groups ──────────────────────────────────────────────────────────────
    getGroups: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/groups`,
        method: "GET",
        params,
       
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getGroup: builder.query<any, number>({
      query: (id) => ({
        url: `/users/groups/${id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),

    // ── Teams ───────────────────────────────────────────────────────────────
    getTeams: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `/users/teams`,
        method: "GET",
        params,
        
      }),
      // transformResponse: (raw: any) => {
      //   return raw.data?.results ?? [];
      // },
      providesTags: ["Users"],
    }),
    getTeam: builder.query<any, number>({
      query: (id) => ({
        url: `/users/teams/${id}`,
        method: "GET",
        
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Users"],
    }),
  }),
});

export const {
  useGetUsersQuery,
  useRetrieveUserQuery,
  useAddUserMutation,
  usePatchUserMutation,
  useDeleteUserMutation,

  useLazyGetUsersQuery,
  useLazyRetrieveUserQuery,

  useGetDepartmentsQuery,
  useGetDepartmentQuery,

  useGetGroupsQuery,
  useGetGroupQuery,

  useGetTeamsQuery,
  useGetTeamQuery,
} = userApiSlice;
