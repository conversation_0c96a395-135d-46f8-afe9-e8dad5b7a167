import { apiSlice } from "../apiSlice";

export const procurementApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Store Requisitions
    getStoreRequisitions: builder.query({
      query: (params) => ({
        url: "/procurement/store-requisitions",
        method: "GET",
        params: params,
      }),
      providesTags: ["StoreRequisitions"],
      transformResponse: (response: any) => {
        console.log("Store Requisitions API response:", response);
        return {
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          per_page: response.data?.per_page || 20,
          total_data: response.data?.total_data || 0,
          links: response.data?.links || { next: null, previous: null },
          results: response.data?.results || []
        };
      },
    }),

    getStoreRequisition: builder.query({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "StoreRequisitions", id }],
      transformResponse: (response: any) => {
        // Handle single item response
        return response.data || response;
      },
    }),

    createStoreRequisition: builder.mutation({
      query: (payload) => ({
        url: "/procurement/store-requisitions",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["StoreRequisitions"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateStoreRequisition: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["StoreRequisitions"],
    }),

    // Workflow actions using PATCH to update status
    submitStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Submitted",
          submitted_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    approveStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Approved",
          approved_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    rejectStoreRequisition: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Rejected",
          rejection_reason: reason,
          rejected_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    issueStoreRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Issued",
          issued_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Convert Store Requisition to Purchase Requisition using dedicated API endpoint
    convertToPurchaseRequisition: builder.mutation({
      query: (storeRequisitionData) => ({
        url: `/procurement/store-requisitions/${storeRequisitionData.id}/create-purchase-requisition`,
        method: "POST",
        body: {
          code: storeRequisitionData.code || "",
          requested_by: storeRequisitionData.requested_by || "",
          cost_center: storeRequisitionData.cost_center || "",
          store: storeRequisitionData.store || "",
          status: "Draft",
          purpose: storeRequisitionData.purpose || "",
          required_by: storeRequisitionData.required_by || new Date().toISOString().split('T')[0]
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Mark Store Requisition as converted after successful conversion
    markStoreRequisitionAsConverted: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "converted",
          converted_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "StoreRequisitions", id },
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Convert Purchase Requisition to RFQ using dedicated API endpoint
    convertPurchaseRequisitionToRFQ: builder.mutation({
      query: (purchaseRequisitionData) => ({
        url: `/procurement/purchase-requisitions/${purchaseRequisitionData.id}/create-rfq`,
        method: "POST",
        body: {
          code: purchaseRequisitionData.code || "",
          status: "Draft",
          store_requisition: purchaseRequisitionData.store_requisition || "",
          created_by: purchaseRequisitionData.created_by || ""
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Mark Purchase Requisition as approved after successful RFQ conversion
    markPurchaseRequisitionAsApproved: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Approved",
          approved_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Store Requisition Items
    getStoreRequisitionItems: builder.query({
      query: (params) => ({
        url: "/procurement/store-requisition-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["StoreRequisitionItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getStoreRequisitionItem: builder.query({
      query: (id) => ({
        url: `/procurement/store-requisition-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "StoreRequisitionItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createStoreRequisitionItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/store-requisition-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["StoreRequisitionItems", "StoreRequisitions"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateStoreRequisitionItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/store-requisition-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "StoreRequisitionItems", id },
        "StoreRequisitionItems",
        "StoreRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteStoreRequisitionItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/store-requisition-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["StoreRequisitionItems", "StoreRequisitions"],
    }),

    // Purchase Requisitions (Real API Endpoints)
    getPurchaseRequisitions: builder.query({
      query: (params) => ({
        url: "/procurement/purchase-requisitions",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseRequisitions"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getPurchaseRequisition: builder.query({
      query: (id) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseRequisitions", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createPurchaseRequisition: builder.mutation({
      query: (payload) => ({
        url: "/procurement/purchase-requisitions",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseRequisitions", "StoreRequisitions"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updatePurchaseRequisition: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deletePurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseRequisitions"],
    }),

    // Workflow actions using PATCH to update status
    submitPurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Submitted",
          submitted_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    approvePurchaseRequisition: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Approved",
          approved_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    rejectPurchaseRequisition: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/procurement/purchase-requisitions/${id}`,
        method: "PATCH",
        body: {
          status: "Rejected",
          rejection_reason: reason,
          rejected_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseRequisitions", id },
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Purchase Requisition Items (Real API Endpoints)
    getPurchaseRequisitionItems: builder.query({
      query: (params) => ({
        url: "/procurement/purchase-requisition-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseRequisitionItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getPurchaseRequisitionItem: builder.query({
      query: (id) => ({
        url: `/procurement/purchase-requisition-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseRequisitionItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createPurchaseRequisitionItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/purchase-requisition-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseRequisitionItems", "PurchaseRequisitions"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updatePurchaseRequisitionItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/purchase-requisition-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseRequisitionItems", id },
        "PurchaseRequisitionItems",
        "PurchaseRequisitions",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deletePurchaseRequisitionItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-requisition-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseRequisitionItems", "PurchaseRequisitions"],
    }),

    // Purchase Orders (Real API Endpoints)
    getPurchaseOrders: builder.query({
      query: (params) => ({
        url: "/procurement/purchase-orders",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseOrders"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getPurchaseOrder: builder.query({
      query: (id) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseOrders", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createPurchaseOrder: builder.mutation({
      query: (payload) => ({
        url: "/procurement/purchase-orders",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseOrders"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updatePurchaseOrder: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deletePurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseOrders"],
    }),

    // Workflow actions using PATCH to update status
    submitPurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "PATCH",
        body: {
          status: "Submitted",
          submitted_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    approvePurchaseOrder: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "PATCH",
        body: {
          status: "Approved",
          approved_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    cancelPurchaseOrder: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/procurement/purchase-orders/${id}`,
        method: "PATCH",
        body: {
          status: "Cancelled",
          cancellation_reason: reason,
          cancelled_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseOrders", id },
        "PurchaseOrders",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Purchase Order Items (Real API Endpoints)
    getPurchaseOrderItems: builder.query({
      query: (params) => ({
        url: "/procurement/purchase-order-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["PurchaseOrderItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getPurchaseOrderItem: builder.query({
      query: (id) => ({
        url: `/procurement/purchase-order-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "PurchaseOrderItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createPurchaseOrderItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/purchase-order-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["PurchaseOrderItems", "PurchaseOrders"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updatePurchaseOrderItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/purchase-order-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "PurchaseOrderItems", id },
        "PurchaseOrderItems",
        "PurchaseOrders",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deletePurchaseOrderItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/purchase-order-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["PurchaseOrderItems", "PurchaseOrders"],
    }),

    // RFQs (Real API Endpoints)
    getRFQs: builder.query({
      query: (params) => ({
        url: "/procurement/rfqs",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQs"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getRFQ: builder.query({
      query: (id) => ({
        url: `/procurement/rfqs/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "RFQs", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createRFQ: builder.mutation({
      query: (payload) => ({
        url: "/procurement/rfqs",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateRFQ: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/rfqs/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RFQs", id },
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteRFQ: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfqs/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQs"],
    }),

    // Workflow actions using PATCH to update status
    closeRFQ: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfqs/${id}`,
        method: "PATCH",
        body: {
          status: "Closed",
          closed_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, id) => [
        { type: "RFQs", id },
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    cancelRFQ: builder.mutation({
      query: ({ id, reason }) => ({
        url: `/procurement/rfqs/${id}`,
        method: "PATCH",
        body: {
          status: "Cancelled",
          cancellation_reason: reason,
          cancelled_at: new Date().toISOString()
        },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RFQs", id },
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // RFQ Items (Real API Endpoints)
    getRFQItems: builder.query({
      query: (params) => ({
        url: "/procurement/rfq-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getRFQItem: builder.query({
      query: (id) => ({
        url: `/procurement/rfq-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "RFQItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createRFQItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/rfq-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQItems", "RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateRFQItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/rfq-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RFQItems", id },
        "RFQItems",
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteRFQItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfq-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQItems", "RFQs"],
    }),

    // RFQ Responses (Real API Endpoints)
    getRFQResponses: builder.query({
      query: (params) => ({
        url: "/procurement/rfq-responses",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQResponses"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getRFQResponse: builder.query({
      query: (id) => ({
        url: `/procurement/rfq-responses/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "RFQResponses", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createRFQResponse: builder.mutation({
      query: (payload) => ({
        url: "/procurement/rfq-responses",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQResponses", "RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Create RFQ Response for specific RFQ (New API endpoint)
    createRFQResponseForRFQ: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/rfqs/${id}/create-response`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQResponses", "RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateRFQResponse: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/rfq-responses/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RFQResponses", id },
        "RFQResponses",
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Submit RFQ Response - PATCH to update status (triggers auto-generation of submitted_by and submitted_at)
    submitRFQResponse: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfq-responses/${id}`,
        method: "PATCH",
        body: { status: "submitted" }, // or whatever field indicates submission
      }),
      invalidatesTags: (result, error, id) => [
        { type: "RFQResponses", id },
        "RFQResponses",
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteRFQResponse: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfq-responses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQResponses", "RFQs"],
    }),

    // RFQ Response Items (Real API Endpoints)
    getRFQResponseItems: builder.query({
      query: (params) => ({
        url: "/procurement/rfq-response-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["RFQResponseItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getRFQResponseItem: builder.query({
      query: (id) => ({
        url: `/procurement/rfq-response-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "RFQResponseItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createRFQResponseItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/rfq-response-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["RFQResponseItems", "RFQResponses", "RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateRFQResponseItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/rfq-response-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "RFQResponseItems", id },
        "RFQResponseItems",
        "RFQResponses",
        "RFQs",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteRFQResponseItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/rfq-response-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["RFQResponseItems", "RFQResponses", "RFQs"],
    }),

    // Bid Analysis (Real API Endpoints)
    getBidAnalyses: builder.query({
      query: (params) => ({
        url: "/procurement/bid-analyses",
        method: "GET",
        params: params,
      }),
      providesTags: ["BidAnalyses"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getBidAnalysis: builder.query({
      query: (id) => ({
        url: `/procurement/bid-analyses/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "BidAnalyses", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createBidAnalysis: builder.mutation({
      query: (payload) => ({
        url: "/procurement/bid-analyses",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["BidAnalyses"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateBidAnalysis: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/bid-analyses/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "BidAnalyses", id },
        "BidAnalyses"
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteBidAnalysis: builder.mutation({
      query: (id) => ({
        url: `/procurement/bid-analyses/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["BidAnalyses"],
    }),

    createBidAnalysisFromRFQ: builder.mutation({
      query: ({ rfq_id, ...payload }) => ({
        url: `/procurement/bid-analyses/create-from-rfq/${rfq_id}`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["BidAnalyses", "RFQs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Bid Analysis Lines (Real API Endpoints)
    getBidAnalysisLines: builder.query({
      query: (params) => ({
        url: "/procurement/bid-analysis-lines",
        method: "GET",
        params: params,
      }),
      providesTags: ["BidAnalysisLines"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getBidAnalysisLine: builder.query({
      query: (id) => ({
        url: `/procurement/bid-analysis-lines/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "BidAnalysisLines", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createBidAnalysisLine: builder.mutation({
      query: (payload) => ({
        url: "/procurement/bid-analysis-lines",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["BidAnalysisLines", "BidAnalyses"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateBidAnalysisLine: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/bid-analysis-lines/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "BidAnalysisLines", id },
        "BidAnalysisLines",
        "BidAnalyses",
      ],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteBidAnalysisLine: builder.mutation({
      query: (id) => ({
        url: `/procurement/bid-analysis-lines/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["BidAnalysisLines", "BidAnalyses"],
    }),

    // GRN (Goods Received Note) Endpoints
    getGRNs: builder.query({
      query: (params) => ({
        url: "/procurement/grns",
        method: "GET",
        params: params,
      }),
      providesTags: ["GRNs"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getGRN: builder.query({
      query: (id) => ({
        url: `/procurement/grns/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "GRNs", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createGRN: builder.mutation({
      query: (payload) => ({
        url: "/procurement/grns",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["GRNs", "PurchaseOrders"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateGRN: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/grns/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: ["GRNs", "PurchaseOrders"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteGRN: builder.mutation({
      query: (id) => ({
        url: `/procurement/grns/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["GRNs", "PurchaseOrders"],
    }),

    // GRN Items Endpoints
    getGRNItems: builder.query({
      query: (params) => ({
        url: "/procurement/grn-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["GRNItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getGRNItem: builder.query({
      query: (id) => ({
        url: `/procurement/grn-items/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "GRNItems", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createGRNItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/grn-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["GRNItems", "GRNs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateGRNItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/grn-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: ["GRNItems", "GRNs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteGRNItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/grn-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["GRNItems", "GRNs"],
    }),

    // Purchase Invoice Endpoints
    getInvoices: builder.query({
      query: (params) => ({
        url: "/procurement/invoices",
        method: "GET",
        params: params,
      }),
      providesTags: ["Invoices"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    getInvoice: builder.query({
      query: (id) => ({
        url: `/procurement/invoices/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Invoices", id }],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createInvoice: builder.mutation({
      query: (payload) => ({
        url: "/procurement/invoices",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Invoices", "GRNs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateInvoice: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/invoices/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: ["Invoices", "GRNs"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteInvoice: builder.mutation({
      query: (id) => ({
        url: `/procurement/invoices/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Invoices"],
    }),

    // Invoice Workflow Endpoints
    submitInvoice: builder.mutation({
      query: (id) => ({
        url: `/procurement/invoices/${id}/submit`,
        method: "POST",
      }),
      invalidatesTags: ["Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    reviewInvoice: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/invoices/${id}/review`,
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    approveInvoice: builder.mutation({
      query: (id) => ({
        url: `/procurement/invoices/${id}/approve`,
        method: "POST",
      }),
      invalidatesTags: ["Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    createPaymentVoucher: builder.mutation({
      query: (id) => ({
        url: `/procurement/invoices/${id}/create-voucher`,
        method: "POST",
      }),
      invalidatesTags: ["Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Invoice Items Endpoints
    getInvoiceItems: builder.query({
      query: (params) => ({
        url: "/procurement/invoice-items",
        method: "GET",
        params: params,
      }),
      providesTags: ["InvoiceItems"],
      transformResponse: (response: any) => {
        return {
          ...response.data,
          results: response.data?.results || []
        };
      },
    }),

    createInvoiceItem: builder.mutation({
      query: (payload) => ({
        url: "/procurement/invoice-items",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["InvoiceItems", "Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    updateInvoiceItem: builder.mutation({
      query: ({ id, ...payload }) => ({
        url: `/procurement/invoice-items/${id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: ["InvoiceItems", "Invoices"],
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    deleteInvoiceItem: builder.mutation({
      query: (id) => ({
        url: `/procurement/invoice-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["InvoiceItems", "Invoices"],
    }),

    // Invoice File Upload
    uploadInvoiceFile: builder.mutation({
      query: (formData) => ({
        url: "/procurement/invoices/upload",
        method: "POST",
        body: formData,
      }),
      transformResponse: (response: any) => {
        return response.data || response;
      },
    }),

    // Invoice Audit Log
    getInvoiceAuditLog: builder.query({
      query: (id) => ({
        url: `/procurement/invoices/${id}/audit-log`,
        method: "GET",
      }),
      transformResponse: (response: any) => {
        return response.data?.results || [];
      },
    }),

    // Supporting Data Endpoints
    getProducts: builder.query({
      query: (params) => ({
        url: "/inventory/products",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Products API response:", response);
        // Handle the full API response structure
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getGLAccounts: builder.query({
      query: (params) => ({
        url: "/setup/gl-accounts",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("GL Accounts API response:", response);
        // Handle the full API response structure
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getCostCenters: builder.query({
      query: (params) => ({
        url: "/setup/cost-centers",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Cost Centers API response:", response);
        // Handle the full API response structure
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getStores: builder.query({
      query: (params) => ({
        url: "/setup/stores",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Stores API response:", response);
        // Handle the full API response structure
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getUnitsOfMeasure: builder.query({
      query: (params) => ({
        url: "/setup/units-of-measure",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Units of Measure API response:", response);
        // Handle the full API response structure
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    // Purchase Requisition Supporting Data
    getProcurementOfficers: builder.query({
      query: (params) => ({
        url: "/setup/procurement-officers",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Procurement Officers API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getDepartments: builder.query({
      query: (params) => ({
        url: "/setup/departments",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Departments API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getSupplierCategories: builder.query({
      query: (params) => ({
        url: "/setup/supplier-categories",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Supplier Categories API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    // Purchase Order Supporting Data
    getSuppliers: builder.query({
      query: (params) => ({
        url: "/setup/suppliers",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Suppliers API response:", response);
        console.log("Response structure:", {
          hasData: !!response.data,
          hasMessage: !!response.message,
          keys: Object.keys(response || {})
        });

        // Handle the actual API response structure: {message: "...", data: {...}}
        if (response.data) {
          const transformed = {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
          console.log("Transformed suppliers:", transformed);
          return transformed;
        }

        console.log("No data found, returning empty results");
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    getPaymentTerms: builder.query({
      query: (params) => ({
        url: "/setup/payment-terms",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Payment Terms API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    // getCurrencies API removed - using default KES currency

    // Tax Rates
    getTaxRates: builder.query({
      query: (params) => ({
        url: "/setup/tax-rates",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Tax Rates API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),

    // Users for requested_by field
    getUsers: builder.query({
      query: (params) => ({
        url: "/users/users",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log("Users API response:", response);
        if (response.data) {
          return {
            current_page: response.data.current_page || 1,
            last_page: response.data.last_page || 1,
            per_page: response.data.per_page || 20,
            total_data: response.data.total_data || 0,
            links: response.data.links || { next: null, previous: null },
            results: response.data.results || []
          };
        }
        return {
          current_page: 1,
          last_page: 1,
          per_page: 20,
          total_data: 0,
          links: { next: null, previous: null },
          results: []
        };
      },
    }),
  }),
});

export const {
  // Store Requisitions
  useGetStoreRequisitionsQuery,
  useGetStoreRequisitionQuery,
  useCreateStoreRequisitionMutation,
  useUpdateStoreRequisitionMutation,
  useDeleteStoreRequisitionMutation,
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useIssueStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation,
  useMarkStoreRequisitionAsConvertedMutation,
  useConvertPurchaseRequisitionToRFQMutation,
  useMarkPurchaseRequisitionAsApprovedMutation,

  // Store Requisition Items
  useGetStoreRequisitionItemsQuery,
  useGetStoreRequisitionItemQuery,
  useCreateStoreRequisitionItemMutation,
  useUpdateStoreRequisitionItemMutation,
  useDeleteStoreRequisitionItemMutation,

  // Purchase Requisitions
  useGetPurchaseRequisitionsQuery,
  useGetPurchaseRequisitionQuery,
  useCreatePurchaseRequisitionMutation,
  useUpdatePurchaseRequisitionMutation,
  useDeletePurchaseRequisitionMutation,
  useSubmitPurchaseRequisitionMutation,
  useApprovePurchaseRequisitionMutation,
  useRejectPurchaseRequisitionMutation,

  // Purchase Requisition Items
  useGetPurchaseRequisitionItemsQuery,
  useGetPurchaseRequisitionItemQuery,
  useCreatePurchaseRequisitionItemMutation,
  useUpdatePurchaseRequisitionItemMutation,
  useDeletePurchaseRequisitionItemMutation,

  // Purchase Orders
  useGetPurchaseOrdersQuery,
  useGetPurchaseOrderQuery,
  useCreatePurchaseOrderMutation,
  useUpdatePurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useSubmitPurchaseOrderMutation,
  useApprovePurchaseOrderMutation,
  useCancelPurchaseOrderMutation,

  // Purchase Order Items
  useGetPurchaseOrderItemsQuery,
  useGetPurchaseOrderItemQuery,
  useCreatePurchaseOrderItemMutation,
  useUpdatePurchaseOrderItemMutation,
  useDeletePurchaseOrderItemMutation,

  // RFQs
  useGetRFQsQuery,
  useGetRFQQuery,
  useCreateRFQMutation,
  useUpdateRFQMutation,
  useDeleteRFQMutation,
  useCloseRFQMutation,
  useCancelRFQMutation,

  // RFQ Items
  useGetRFQItemsQuery,
  useGetRFQItemQuery,
  useCreateRFQItemMutation,
  useUpdateRFQItemMutation,
  useDeleteRFQItemMutation,

  // RFQ Responses
  useGetRFQResponsesQuery,
  useGetRFQResponseQuery,
  useCreateRFQResponseMutation,
  useCreateRFQResponseForRFQMutation,
  useUpdateRFQResponseMutation,
  useSubmitRFQResponseMutation,
  useDeleteRFQResponseMutation,

  // RFQ Response Items
  useGetRFQResponseItemsQuery,
  useGetRFQResponseItemQuery,
  useCreateRFQResponseItemMutation,
  useUpdateRFQResponseItemMutation,
  useDeleteRFQResponseItemMutation,

  // Bid Analysis
  useGetBidAnalysesQuery,
  useGetBidAnalysisQuery,
  useCreateBidAnalysisMutation,
  useUpdateBidAnalysisMutation,
  useDeleteBidAnalysisMutation,
  useCreateBidAnalysisFromRFQMutation,

  // Bid Analysis Lines
  useGetBidAnalysisLinesQuery,
  useGetBidAnalysisLineQuery,
  useCreateBidAnalysisLineMutation,
  useUpdateBidAnalysisLineMutation,
  useDeleteBidAnalysisLineMutation,

  // GRN (Goods Received Note) Hooks
  useGetGRNsQuery,
  useGetGRNQuery,
  useCreateGRNMutation,
  useUpdateGRNMutation,
  useDeleteGRNMutation,

  // GRN Items Hooks
  useGetGRNItemsQuery,
  useGetGRNItemQuery,
  useCreateGRNItemMutation,
  useUpdateGRNItemMutation,
  useDeleteGRNItemMutation,

  // Invoice Hooks
  useGetInvoicesQuery,
  useGetInvoiceQuery,
  useCreateInvoiceMutation,
  useUpdateInvoiceMutation,
  useDeleteInvoiceMutation,

  // Invoice Workflow Hooks
  useSubmitInvoiceMutation,
  useReviewInvoiceMutation,
  useApproveInvoiceMutation,
  useCreatePaymentVoucherMutation,

  // Invoice Items Hooks
  useGetInvoiceItemsQuery,
  useCreateInvoiceItemMutation,
  useUpdateInvoiceItemMutation,
  useDeleteInvoiceItemMutation,

  // Invoice File Upload
  useUploadInvoiceFileMutation,

  // Invoice Audit Log
  useGetInvoiceAuditLogQuery,

  // Supporting Data - Unique names for procurement
  useGetProductsQuery: useGetProcurementProductsQuery,
  useGetCostCentersQuery: useGetProcurementCostCentersQuery,
  useGetStoresQuery: useGetProcurementStoresQuery,
  useGetUnitsOfMeasureQuery: useGetProcurementUnitsOfMeasureQuery,
  useGetProcurementOfficersQuery,
  useGetDepartmentsQuery,
  useGetSupplierCategoriesQuery,
  useGetSuppliersQuery,
  useGetPaymentTermsQuery,
  useGetTaxRatesQuery,
  useGetUsersQuery: useGetProcurementUsersQuery,
  useGetGLAccountsQuery,
} = procurementApiSlice;
