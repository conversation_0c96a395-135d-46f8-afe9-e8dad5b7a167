import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement<LucideIcon>;
  trend?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, trend }) => (
  <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/70 backdrop-blur-sm hover:bg-white/90">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {trend && (
            <p className="text-sm text-emerald-600 flex items-center mt-1">
              <svg
                className="h-3 w-3 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                />
              </svg>
              {trend}
            </p>
          )}
        </div>
        <div className="p-3 bg-gradient-to-br from-red-500 via-yellow-400 to-orange-500 rounded-xl group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
      </div>
    </CardContent>
  </Card>
);