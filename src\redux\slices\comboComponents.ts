import { apiSlice } from "../apiSlice";

export const comboComponentApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getComboComponents: builder.query({
      query: (params) => ({
        url: "/combo-menu",
        method: "GET",
        params: params,
      }),
      providesTags: ["ComboComponent"],
    }),

    retrieveComboComponent: builder.query({
      query: (id) => ({
        url: `/combo-menu/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "ComboComponent", id }],
    }),

    addComboComponent: builder.mutation({
      query: (payload) => ({
        url: "/combo-menu",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ComboComponent"],
    }),

    patchComboComponent: builder.mutation({
      query: (payload) => ({
        url: `/combo-menu/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "ComboComponent", id },
        "ComboComponent",
      ],
    }),

    deleteComboComponent: builder.mutation({
      query: (id) => ({
        url: `/combo-menu/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["ComboComponent"],
    }),
  }),
});

export const {
  useGetComboComponentsQuery,
  useRetrieveComboComponentQuery,
  useAddComboComponentMutation,
  usePatchComboComponentMutation,
  useDeleteComboComponentMutation,

  useLazyGetComboComponentsQuery,
  useLazyRetrieveComboComponentQuery,
} = comboComponentApiSlice;