import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Edit, Package, DollarSign } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  useUpdateRFQResponseMutation,
  useUpdateRFQResponseItemMutation,
  useGetSuppliersQuery,
  useGetTaxRatesQuery,
  useGetRFQResponseQuery,
  useGetRFQResponseItemsQuery,
} from "@/redux/slices/procurement";

interface EditRFQResponseCompleteProps {
  open: boolean;
  onClose: () => void;
  response: any;
  onSuccess?: () => void;
}

const EditRFQResponseComplete = ({ open, onClose, response, onSuccess }: EditRFQResponseCompleteProps) => {
  const [formData, setFormData] = useState({
    supplier: "",
    notes: "",
  });

  const [itemsData, setItemsData] = useState<any[]>([]);
  const [editingItemId, setEditingItemId] = useState<number | null>(null);

  const [updateRFQResponse, { isLoading: updatingResponse }] = useUpdateRFQResponseMutation();
  const [updateRFQResponseItem, { isLoading: updatingItem }] = useUpdateRFQResponseItemMutation();
  const { data: suppliers, isLoading: suppliersLoading, error: suppliersError } = useGetSuppliersQuery({});

  // Debug suppliers query
  useEffect(() => {
    console.log("Suppliers query state:");
    console.log("- Loading:", suppliersLoading);
    console.log("- Error:", suppliersError);
    console.log("- Data:", suppliers);
  }, [suppliers, suppliersLoading, suppliersError]);
  const { data: taxRates } = useGetTaxRatesQuery({});

  // Fetch complete RFQ response data with items
  const { data: fullResponse, isLoading: responseLoading } = useGetRFQResponseQuery(
    response?.id,
    { skip: !response?.id }
  );

  // Also fetch response items separately
  const { data: responseItems, isLoading: itemsLoading } = useGetRFQResponseItemsQuery(
    { rfq_response_id: response?.id },
    { skip: !response?.id }
  );

  // Default currencies - only KES for now
  const currencies = {
    results: [
      { id: 1, code: "KES", name: "Kenya Shillings" }
    ]
  };

  // Initialize form data when response changes
  useEffect(() => {
    // Use fullResponse if available, otherwise fallback to response
    const responseData = fullResponse || response;

    if (responseData) {
      console.log("Response data:", response);
      console.log("Full response data:", fullResponse);
      console.log("Suppliers data:", suppliers);
      console.log("Suppliers loading:", suppliersLoading);
      console.log("Suppliers error:", suppliersError);

      setFormData({
        supplier: responseData.supplier?.toString() || "",
        notes: responseData.notes || "",
      });

      // Initialize items data - check multiple possible locations
      let items = responseData.rfq_response_items || responseData.response_items || responseData.items || [];

      // If no items in response, use separately fetched items
      if (items.length === 0 && responseItems?.results) {
        items = responseItems.results;
      }

      console.log("Items data sources:");
      console.log("- responseData.rfq_response_items:", responseData.rfq_response_items);
      console.log("- responseData.response_items:", responseData.response_items);
      console.log("- responseData.items:", responseData.items);
      console.log("- responseItems?.results:", responseItems?.results);
      console.log("- Final items:", items);

      setItemsData(items);
    }
  }, [response, fullResponse, responseItems, suppliers, suppliersLoading, suppliersError]);

  const calculateTotalPrice = (unitPrice: string, quantity: string, taxRate: string) => {
    const price = parseFloat(unitPrice) || 0;
    const qty = parseFloat(quantity) || 0;
    const tax = parseFloat(taxRate) || 0;
    
    const subtotal = price * qty;
    const taxAmount = (subtotal * tax) / 100;
    const total = subtotal + taxAmount;
    
    return total.toFixed(2);
  };

  const handleItemChange = (itemId: number, field: string, value: any) => {
    setItemsData(prev => prev.map(item => {
      if (item.id === itemId) {
        const updatedItem = { ...item, [field]: value };
        
        // Auto-calculate total price when unit_price, quantity, or tax_rate changes
        if (field === 'unit_price' || field === 'quantity' || field === 'tax_rate') {
          const totalPrice = calculateTotalPrice(
            field === 'unit_price' ? value : updatedItem.unit_price?.toString() || "0",
            field === 'quantity' ? value : updatedItem.quantity?.toString() || "0",
            field === 'tax_rate' ? value : updatedItem.tax_rate?.toString() || "0"
          );
          updatedItem.total_price = parseFloat(totalPrice);
        }
        
        return updatedItem;
      }
      return item;
    }));
  };

  const handleSubmitResponse = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.supplier) {
      toast.error("Supplier is required");
      return;
    }

    try {
      // Find the supplier code from the selected supplier ID
      const selectedSupplier = suppliers?.results?.find((s: any) => s.id.toString() === formData.supplier) ||
                              suppliers?.data?.results?.find((s: any) => s.id.toString() === formData.supplier);

      if (!selectedSupplier) {
        toast.error("Selected supplier not found");
        return;
      }

      const payload = {
        supplier: selectedSupplier.code, // Try 'supplier' field instead of 'supplier_id'
        notes: formData.notes,
      };

      console.log("Updating RFQ Response with payload:", payload);
      console.log("Response ID:", response.id);
      console.log("Form data supplier:", formData.supplier);
      console.log("Selected supplier:", selectedSupplier);

      await updateRFQResponse({
        id: response.id,
        ...payload
      }).unwrap();

      toast.success("RFQ response updated successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating RFQ response:", error);
      toast.error(error?.data?.message || "Failed to update RFQ response");
    }
  };

  const handleSubmitItem = async (itemId: number) => {
    const item = itemsData.find(i => i.id === itemId);
    if (!item) return;

    try {
      const payload = {
        unit_price: parseFloat(item.unit_price) || 0,
        quantity: parseFloat(item.quantity) || 0,
        delivery_time_days: item.delivery_time_days || null,
        currency: item.currency || "KES",
        ...(item.tax_rate && item.tax_rate !== "0" && {
          tax_rate: parseInt(item.tax_rate)
        }),
        total_price: item.total_price || 0,
      };

      await updateRFQResponseItem({ 
        id: itemId, 
        ...payload 
      }).unwrap();

      toast.success("RFQ response item updated successfully");
      setEditingItemId(null);
      onSuccess?.();
    } catch (error: any) {
      console.error("Error updating RFQ response item:", error);
      toast.error(error?.data?.message || "Failed to update RFQ response item");
    }
  };

  const resetForm = () => {
    setFormData({
      supplier: "",
      notes: "",
    });
    setItemsData([]);
    setEditingItemId(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit RFQ Response - {response?.code}
          </DialogTitle>
        </DialogHeader>

        {(responseLoading || itemsLoading) && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Loading response details...
          </div>
        )}

        {!responseLoading && !itemsLoading && (
          <div className="space-y-6">


          {/* RFQ Response Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Response Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmitResponse} className="space-y-4">
                {/* Supplier */}
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplier}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, supplier: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliersLoading ? (
                        <SelectItem value="loading" disabled>Loading suppliers...</SelectItem>
                      ) : suppliersError ? (
                        <SelectItem value="error" disabled>Error loading suppliers</SelectItem>
                      ) : (() => {
                        console.log("Rendering suppliers dropdown - suppliers data:", suppliers);

                        // Try multiple data access patterns
                        let suppliersList = null;
                        if (suppliers?.results && Array.isArray(suppliers.results) && suppliers.results.length > 0) {
                          suppliersList = suppliers.results;
                          console.log("Using suppliers.results:", suppliersList);
                        } else if (suppliers?.data?.results && Array.isArray(suppliers.data.results) && suppliers.data.results.length > 0) {
                          suppliersList = suppliers.data.results;
                          console.log("Using suppliers.data.results:", suppliersList);
                        }

                        if (suppliersList) {
                          console.log("Rendering suppliers list:", suppliersList);
                          return suppliersList.map((supplier: any) => (
                            <SelectItem key={supplier.id} value={supplier.id.toString()}>
                              {supplier.name} ({supplier.code})
                            </SelectItem>
                          ));
                        }

                        console.log("No suppliers found - showing fallback");
                        return <SelectItem value="none" disabled>No suppliers available</SelectItem>;
                      })()}
                    </SelectContent>
                  </Select>
                </div>

                {/* Notes */}
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="Enter any notes or comments"
                    rows={3}
                  />
                </div>

                <Button type="submit" disabled={updatingResponse}>
                  {updatingResponse && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Response Details
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* RFQ Response Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Response Items ({itemsData.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {itemsData.map((item) => (
                <div key={item.id} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{item.rfq_item?.product?.name || 'Unknown Product'}</h4>
                      <p className="text-sm text-gray-600">
                        Requested: {item.rfq_item?.quantity} {item.rfq_item?.unit_of_measure?.name}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingItemId(editingItemId === item.id ? null : item.id)}
                    >
                      {editingItemId === item.id ? "Cancel" : "Edit"}
                    </Button>
                  </div>

                  {editingItemId === item.id ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* Unit Price */}
                      <div>
                        <Label>Unit Price *</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={item.unit_price || ""}
                          onChange={(e) => handleItemChange(item.id, "unit_price", e.target.value)}
                          placeholder="0.00"
                        />
                      </div>

                      {/* Quantity */}
                      <div>
                        <Label>Quantity *</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={item.quantity || ""}
                          onChange={(e) => handleItemChange(item.id, "quantity", e.target.value)}
                          placeholder="0"
                        />
                      </div>

                      {/* Currency */}
                      <div>
                        <Label>Currency</Label>
                        <Select
                          value={item.currency || "KES"}
                          onValueChange={(value) => handleItemChange(item.id, "currency", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            {currencies.results.map((currency: any) => (
                              <SelectItem key={currency.id} value={currency.code}>
                                {currency.code} - {currency.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Delivery Time */}
                      <div>
                        <Label>Delivery Time (Days)</Label>
                        <Input
                          type="number"
                          value={item.delivery_time_days || ""}
                          onChange={(e) => handleItemChange(item.id, "delivery_time_days", e.target.value)}
                          placeholder="0"
                        />
                      </div>

                      {/* Tax Rate */}
                      <div>
                        <Label>Tax Rate</Label>
                        <Select
                          value={item.tax_rate?.toString() || "0"}
                          onValueChange={(value) => handleItemChange(item.id, "tax_rate", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select tax rate" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">No Tax</SelectItem>
                            {taxRates?.results?.map((taxRate: any) => (
                              <SelectItem key={taxRate.id} value={taxRate.percentage.toString()}>
                                {taxRate.name} ({taxRate.percentage}%)
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Total Price (Read-only) */}
                      <div>
                        <Label>Total Price</Label>
                        <Input
                          type="text"
                          value={`${item.currency || 'KES'} ${parseFloat(item.total_price || '0').toFixed(2)}`}
                          readOnly
                          className="bg-gray-50"
                        />
                      </div>

                      <div className="col-span-full">
                        <Button 
                          onClick={() => handleSubmitItem(item.id)}
                          disabled={updatingItem}
                          className="w-full"
                        >
                          {updatingItem && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                          Update Item
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Unit Price:</span>
                        <p>{item.currency || 'KES'} {parseFloat(item.unit_price || '0').toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Quantity:</span>
                        <p>{item.quantity || 0}</p>
                      </div>
                      <div>
                        <span className="font-medium">Tax Rate:</span>
                        <p>{item.tax_rate || 0}%</p>
                      </div>
                      <div>
                        <span className="font-medium">Total Price:</span>
                        <p className="font-semibold">{item.currency || 'KES'} {parseFloat(item.total_price || '0').toFixed(2)}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EditRFQResponseComplete;
