import { useRef, useState, useEffect } from 'react';
import Logo from '@/assets/logo.png'; // Adjust path as needed

const OTP_LENGTH = 6;

const OtpVerifyPage = () => {
  const [otp, setOtp] = useState(Array(OTP_LENGTH).fill(''));
  const [timer, setTimer] = useState(59);
  const inputRefs = useRef<HTMLInputElement[]>([]);

  // Timer logic
  useEffect(() => {
    if (timer === 0) return;
    const interval = setInterval(() => setTimer(t => t - 1), 1000);
    return () => clearInterval(interval);
  }, [timer]);

  // Format timer as MM:SS
  const formatTimer = (s: number) => `00:${s.toString().padStart(2, '0')}`;

  const handleChange = (value: string, idx: number) => {
    if (!/^[0-9]?$/.test(value)) return;
    const newOtp = [...otp];
    newOtp[idx] = value;
    setOtp(newOtp);
    if (value && idx < OTP_LENGTH - 1) {
      inputRefs.current[idx + 1]?.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, idx: number) => {
    if (e.key === 'Backspace' && !otp[idx] && idx > 0) {
      inputRefs.current[idx - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const data = e.clipboardData.getData('text').slice(0, OTP_LENGTH);
    if (!/^\d+$/.test(data)) return;
    setOtp(data.split('').concat(Array(OTP_LENGTH - data.length).fill('')));
    setTimeout(() => {
      inputRefs.current[Math.min(data.length, OTP_LENGTH - 1)]?.focus();
    }, 10);
  };

  const handleResend = () => {
    if (timer === 0) setTimer(59);
    // Call resend OTP API here
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const code = otp.join('');
    // Submit OTP here
    console.log('Submitted OTP:', code);
  };

  return (
    <>
      {/* Right Panel (OTP Form) */}
     <div className="w-full min-h-screen lg:w-1/2 flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8">
          {/* Logo */}
          <div className="flex justify-center mt-6 mb-2">
            <img src={Logo} alt="GMC Logo" className="h-16 w-auto" />
          </div>
          {/* Header */}
          <div className="text-center w-full">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Verify It’s Really You</h2>
            <p className="text-gray-500 text-sm mb-2">
              Just a little safety check before we let you in.
            </p>
            <p className="text-gray-500 text-sm mb-4">
              We&apos;ve sent a 6-digit code to your email.<br />Enter below to continue.
            </p>
          </div>
          {/* OTP Form */}
          <form className="w-full flex flex-col items-center" onSubmit={handleSubmit}>
            <div className="flex items-center justify-center gap-2 mb-3">
              {otp.map((digit, idx) => (
                <input
                  key={idx}
                  ref={el => (inputRefs.current[idx] = el!)}
                  value={digit}
                  onChange={e => handleChange(e.target.value, idx)}
                  onKeyDown={e => handleKeyDown(e, idx)}
                  onPaste={idx === 0 ? handlePaste : undefined}
                  maxLength={1}
                  className="w-12 h-12 border rounded-md text-2xl text-center font-semibold focus:outline-none focus:ring-2 focus:ring-red-500"
                  type="text"
                  inputMode="numeric"
                  autoFocus={idx === 0}
                />
              ))}
            </div>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-gray-500 text-xs">Resend code in</span>
              <span className="text-red-500 text-xs font-medium">
                {formatTimer(timer)}
              </span>
            </div>
            <div className="mb-4 text-xs text-gray-500">
              Didn&apos;t get it?{' '}
              <button
                type="button"
                className={`font-medium ${timer === 0 ? 'text-black hover:underline' : 'text-gray-400 cursor-not-allowed'}`}
                disabled={timer !== 0}
                onClick={handleResend}
              >
                RESEND OTP
              </button>
            </div>
            <button
              type="submit"
              className="w-full bg-black text-white font-semibold py-2 rounded-md hover:bg-gray-900 transition-colors mb-2"
              disabled={otp.some(d => d === '')}
            >
              Verify Code
            </button>
          </form>
        </div>
      </div>
    </>
  );
};

export default OtpVerifyPage;
