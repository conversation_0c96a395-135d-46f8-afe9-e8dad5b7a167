import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface NameAvatarProps {
  name?: string;
  image?: string;
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
}

const NameAvatar = ({
  name = "",
  image,
  size = "md",
  className,
}: NameAvatarProps) => {
  // Safely handle when name is undefined or empty
  const initials =
    name && name.trim()
      ? name
          .trim()
          .split(" ")
          .map((n) => n[0] || "")
          .join("")
          .toUpperCase()
          .slice(0, 2)
      : "?";

  const sizeClasses = {
    sm: "h-8 w-8 text-xs",
    md: "h-12 w-12 text-sm",
    lg: "h-16 w-16 text-lg",
    xl: "h-24 w-24 text-xl",
  };

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage src={image} alt={name || "Customer"} />
      <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
        {initials}
      </AvatarFallback>
    </Avatar>
  );
};

export default NameAvatar;
