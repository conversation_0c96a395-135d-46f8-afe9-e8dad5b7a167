import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Users, 
  DollarSign, 
  Building,
  Package,
  Loader2,
  Crown,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { BidAnalysis } from "@/types/procurement";

interface BulkSelectionModalProps {
  open: boolean;
  onClose: () => void;
  selectedItems: number[];
  bidAnalysis: BidAnalysis;
  onBulkSelect: (selectionData: any) => Promise<void>;
}

const BulkSelectionModal = ({ 
  open, 
  onClose, 
  selectedItems, 
  bidAnalysis,
  onBulkSelect 
}: BulkSelectionModalProps) => {
  const [selectedSupplier, setSelectedSupplier] = useState<number | null>(null);
  const [selectionReason, setSelectionReason] = useState("");
  const [applyToAll, setApplyToAll] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const resetForm = () => {
    setSelectedSupplier(null);
    setSelectionReason("");
    setApplyToAll(false);
  };

  const getAvailableSuppliers = () => {
    const supplierMap = new Map();
    
    bidAnalysis.items?.forEach(item => {
      if (selectedItems.includes(item.id!) || applyToAll) {
        item.supplier_quotes?.forEach(quote => {
          if (quote.is_available && quote.unit_price) {
            if (!supplierMap.has(quote.supplier)) {
              supplierMap.set(quote.supplier, {
                id: quote.supplier,
                name: quote.supplier_name,
                itemCount: 0,
                totalValue: 0,
                avgPrice: 0,
                canQuoteAll: true
              });
            }
            
            const supplier = supplierMap.get(quote.supplier);
            supplier.itemCount++;
            supplier.totalValue += quote.total_price || 0;
          }
        });
      }
    });

    // Calculate averages and check if supplier can quote all items
    const targetItemCount = applyToAll ? bidAnalysis.items?.length || 0 : selectedItems.length;
    
    return Array.from(supplierMap.values()).map(supplier => ({
      ...supplier,
      avgPrice: supplier.totalValue / supplier.itemCount,
      canQuoteAll: supplier.itemCount === targetItemCount,
      coverage: (supplier.itemCount / targetItemCount) * 100
    }));
  };

  const getSelectedItemsDetails = () => {
    const targetItems = applyToAll 
      ? bidAnalysis.items || []
      : bidAnalysis.items?.filter(item => selectedItems.includes(item.id!)) || [];

    return targetItems.map(item => {
      const supplierQuote = item.supplier_quotes?.find(q => q.supplier === selectedSupplier);
      return {
        ...item,
        selectedQuote: supplierQuote,
        hasQuote: !!supplierQuote?.is_available
      };
    });
  };

  const calculateTotalValue = () => {
    const itemsDetails = getSelectedItemsDetails();
    return itemsDetails.reduce((total, item) => {
      return total + (item.selectedQuote?.total_price || 0);
    }, 0);
  };

  const handleSubmit = async () => {
    if (!selectedSupplier) {
      return;
    }

    const itemsToUpdate = applyToAll 
      ? bidAnalysis.items?.map(item => item.id!) || []
      : selectedItems;

    const selectionData = {
      supplier_id: selectedSupplier,
      item_ids: itemsToUpdate,
      reason: selectionReason,
      apply_to_all: applyToAll
    };

    setIsLoading(true);
    try {
      await onBulkSelect(selectionData);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const availableSuppliers = getAvailableSuppliers();
  const selectedItemsDetails = getSelectedItemsDetails();
  const totalValue = calculateTotalValue();
  const targetItemCount = applyToAll ? bidAnalysis.items?.length || 0 : selectedItems.length;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Supplier Selection
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Selection Scope */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selection Scope</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="apply-to-all"
                    checked={applyToAll}
                    onCheckedChange={(checked) => setApplyToAll(checked as boolean)}
                  />
                  <Label htmlFor="apply-to-all">
                    Apply to all items in this analysis
                  </Label>
                </div>
                
                <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                  <div className="flex items-center gap-2 text-blue-800 font-medium">
                    <Package className="h-4 w-4" />
                    {applyToAll ? "All Items" : "Selected Items"}
                  </div>
                  <p className="text-blue-700 text-sm mt-1">
                    {applyToAll 
                      ? `This will apply the selected supplier to all ${bidAnalysis.items?.length || 0} items in the analysis`
                      : `This will apply the selected supplier to ${selectedItems.length} selected items`
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supplier Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Available Suppliers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {availableSuppliers.map((supplier) => (
                  <div 
                    key={supplier.id}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedSupplier === supplier.id 
                        ? "border-blue-500 bg-blue-50" 
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => setSelectedSupplier(supplier.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <input
                          type="radio"
                          checked={selectedSupplier === supplier.id}
                          onChange={() => setSelectedSupplier(supplier.id)}
                          className="text-blue-600"
                        />
                        <div>
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{supplier.name}</span>
                            {supplier.canQuoteAll && (
                              <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                Can Quote All
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            Coverage: {supplier.coverage.toFixed(0)}% ({supplier.itemCount}/{targetItemCount} items)
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-1 text-lg font-bold">
                          <DollarSign className="h-4 w-4" />
                          {supplier.avgPrice.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Avg. Unit Price</div>
                        <div className="text-sm font-medium text-green-600">
                          Total: ${supplier.totalValue.toLocaleString()}
                        </div>
                      </div>
                    </div>
                    
                    {!supplier.canQuoteAll && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="text-yellow-800 text-sm">
                          This supplier cannot quote all selected items
                        </span>
                      </div>
                    )}
                  </div>
                ))}
                
                {availableSuppliers.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Building className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No suppliers can quote the selected items</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Selection Preview */}
          {selectedSupplier && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Selection Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{targetItemCount}</div>
                      <div className="text-sm text-gray-600">Items</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        ${totalValue.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Total Value</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedItemsDetails.filter(item => item.hasQuote).length}
                      </div>
                      <div className="text-sm text-gray-600">With Quotes</div>
                    </div>
                  </div>

                  {/* Items without quotes warning */}
                  {selectedItemsDetails.some(item => !item.hasQuote) && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded">
                      <div className="flex items-center gap-2 text-red-800 font-medium mb-2">
                        <AlertTriangle className="h-4 w-4" />
                        Items Without Quotes
                      </div>
                      <div className="space-y-1">
                        {selectedItemsDetails
                          .filter(item => !item.hasQuote)
                          .map(item => (
                            <div key={item.id} className="text-red-700 text-sm">
                              • {item.product_name}
                            </div>
                          ))
                        }
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Selection Reason */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selection Reason</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="bulk-reason">Why was this supplier selected for bulk assignment?</Label>
                <Textarea
                  id="bulk-reason"
                  placeholder="Provide reasoning for the bulk supplier selection (e.g., best overall pricing, preferred vendor, strategic partnership)..."
                  value={selectionReason}
                  onChange={(e) => setSelectionReason(e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || !selectedSupplier || !selectionReason.trim()}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Applying Selection...
              </>
            ) : (
              <>
                <Crown className="mr-2 h-4 w-4" />
                Apply Bulk Selection
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BulkSelectionModal;
