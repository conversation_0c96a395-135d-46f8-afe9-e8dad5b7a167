import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';

import { Checkbox } from '@/components/ui/checkbox';
import { Screen } from '@/app-components/layout/screen';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowLeft, Save, X, Printer, TestTube } from 'lucide-react';
import { PrinterFormData, PrinterType, PrinterInterfaceType } from '@/types/pos';
import {
  useAddPrinterMutation,
  usePatchPrinterMutation,
  useRetrievePrinterQuery,
  useGetPrintersQuery
} from '@/redux/slices/printers';
import { useGetWorkstationsQuery } from '@/redux/slices/workstations';
import { useGetReceiptTemplatesQuery } from '@/redux/slices/receiptTemplates';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetRevenueCentersQuery } from '@/redux/slices/revenueCenters';
// Menu-related imports commented out for now - can be added later when needed
// import { useGetMenusQuery } from '@/redux/slices/menuMake';
// import { useGetMenuGroupsQuery } from '@/redux/slices/menuGroup';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

interface PrinterFormProps {
  mode: 'create' | 'edit';
}

const PrinterForm: React.FC<PrinterFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks
  const [createPrinter, { isLoading: isCreating }] = useAddPrinterMutation();
  const [updatePrinter, { isLoading: isUpdating }] = usePatchPrinterMutation();
  const { data: printerData, isLoading: isLoadingPrinter } = useRetrievePrinterQuery(id!, {
    skip: mode === 'create' || !id,
  });
  const { data: workstations = [], isLoading: loadingWorkstations } = useGetWorkstationsQuery({});
  const { data: receiptTemplates = [], isLoading: loadingTemplates } = useGetReceiptTemplatesQuery({});
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: revenueCenters = [], isLoading: loadingRevenueCenters } = useGetRevenueCentersQuery({});
  const { data: printers = [], isLoading: loadingPrinters } = useGetPrintersQuery({});
  // Menu-related queries commented out for now - can be added later when needed
  // const { data: menus = [], isLoading: loadingMenus } = useGetMenusQuery({});
  // const { data: menuGroups = [], isLoading: loadingMenuGroups } = useGetMenuGroupsQuery({});

  const form = useForm<PrinterFormData>({
    defaultValues: {
      name: '',
      branchId: '',
      revenueCenterId: '',
      type: PrinterType.RECEIPT,
      interfaceType: PrinterInterfaceType.LAN,
      ipAddress: '',
      port: 9100,
      deviceName: '',
      workstationIds: [],
      menuItemRouting: [],
      categoryRouting: [],
      backupPrinterId: '',
      location_note: '',
      is_backup: false,
      is_active: true,
      receipt_template: undefined,
      print_language: ''
    },
  });

  // Load printer data for edit mode
  useEffect(() => {
    if (mode === 'edit' && printerData) {
      form.reset({
        name: printerData.name,
        branchId: '',
        revenueCenterId: '',
        type: printerData.printer_purpose || PrinterType.RECEIPT,
        interfaceType: printerData.interface_type || PrinterInterfaceType.LAN,
        ipAddress: printerData.ip_address || '',
        port: printerData.port || 9100,
        deviceName: '',
        workstationIds: [],
        menuItemRouting: [],
        categoryRouting: [],
        backupPrinterId: '',
        location_note: printerData.location_note || '',
        is_backup: printerData.is_backup_printer || false,
        is_active: printerData.is_active ?? true,
        receipt_template: printerData.receipt_template,
        print_language: ''
      });
    }
  }, [printerData, form, mode]);

  const watchWorkstation = form.watch('workstationIds');
  const watchInterfaceType = form.watch('interfaceType');
  const watchBranchId = form.watch('branchId');

  // Mock data for menu items and categories (replace with real API data when available)
  const menuItemOptions = [
    'appetizers', 'main-courses', 'desserts', 'beverages', 'specials'
  ];

  const categoryOptions = [
    'food', 'drinks', 'alcohol', 'desserts', 'snacks'
  ];

  // Filter revenue centers by selected branch
  const filteredRevenueCenters = revenueCenters.filter(rc =>
    !watchBranchId || rc.branch?.toString() === watchBranchId
  );

  const onSubmit = async (data: PrinterFormData) => {
    console.log('Form submitted with data:', data);
    console.log('Form mode:', mode);

    try {
      // Validate required fields
      if (!data.name || !data.type) {
        console.error('Validation failed - missing required fields:', { name: data.name, type: data.type });
        handleApiError({ message: 'Name and printer type are required fields' }, 'validate form');
        return;
      }

      // Map form data to API format
      const printerPayload = {
        name: data.name,
        ip_address: data.ipAddress || '',
        port: data.port || 9100,
        is_active: data.is_active ?? true,
        printer_purpose: data.type,
        interface_type: data.interfaceType,
        location_note: data.location_note || '',
        is_backup_printer: data.is_backup || false,
        receipt_template: data.receipt_template,
        // Handle backup printer - convert "none" to null/undefined
        backup_printer: data.backupPrinterId && data.backupPrinterId !== 'none' ? data.backupPrinterId : null
      };

      console.log('Submitting printer payload:', printerPayload);

      if (mode === 'create') {
        const result = await createPrinter(printerPayload).unwrap();
        console.log('Printer created successfully:', result);
        handleApiSuccess('Printer created successfully!', result);
      } else if (mode === 'edit' && id) {
        const result = await updatePrinter({ id, data: printerPayload }).unwrap();
        console.log('Printer updated successfully:', result);
        handleApiSuccess('Printer updated successfully!', result);
      }

      navigate('/pos/printers');
    } catch (error: any) {
      handleApiError(error, mode === 'create' ? 'create printer' : 'update printer');
    }
  };



  const testPrinter = async () => {
    // Simulate printer test
    console.log('Testing printer connection...');
    handleApiSuccess('Printer test completed successfully!');
  };

  const testFormData = () => {
    const currentData = form.getValues();
    console.log('Current form data:', currentData);
    console.log('Form errors:', form.formState.errors);
    console.log('Form is valid:', form.formState.isValid);
  };

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={() => navigate('/pos/printers')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {mode === 'create' ? 'Add Printer' : 'Edit Printer'}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' 
              ? 'Configure a new thermal or receipt printer'
              : 'Update printer configuration and settings'
            }
          </p>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Enter the basic details for the printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'Printer name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Printer Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Kitchen Printer 1, Receipt Printer" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branchId"
                rules={{ required: 'Branch assignment is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Branch *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingBranches ? (
                          <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                        ) : branches.length > 0 ? (
                          branches
                            .filter(branch => branch.branch_code) // Filter out branches without codes
                            .map((branch) => (
                              <SelectItem key={branch.id} value={branch.branch_code}>
                                {branch.name} ({branch.branch_code})
                              </SelectItem>
                            ))
                        ) : (
                          <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="revenueCenterId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assign to Revenue Center</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={!watchBranchId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select revenue center (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingRevenueCenters ? (
                          <SelectItem value="loading" disabled>Loading revenue centers...</SelectItem>
                        ) : filteredRevenueCenters.length > 0 ? (
                          filteredRevenueCenters
                            .filter(rc => rc.id) // Filter out revenue centers without IDs
                            .map((rc) => (
                              <SelectItem key={rc.id} value={rc.id.toString()}>
                                {rc.name} ({rc.revenue_center_code})
                              </SelectItem>
                            ))
                        ) : (
                          <SelectItem value="no-centers" disabled>
                            {watchBranchId ? 'No revenue centers for selected branch' : 'Select a branch first'}
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Optional: Assign to a specific revenue center within the branch
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  rules={{ required: 'Printer type is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Printer Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select printer type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PrinterType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="interfaceType"
                  rules={{ required: 'Interface type is required' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Interface Type *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select interface type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(PrinterInterfaceType).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Network Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>
                Configure network settings for the printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {watchInterfaceType !== PrinterInterfaceType.BLUETOOTH && (
                  <FormField
                    control={form.control}
                    name="ipAddress"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IP Address</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="e.g., *************" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Network IP address for LAN/Wi-Fi printers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="deviceName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Device Name</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g., EPSON-TM-T88V, STAR-TSP143" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Printer model or device identifier
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {mode === 'edit' && (
                <div className="flex items-center space-x-2">
                  <Button type="button" variant="outline" onClick={testPrinter}>
                    <TestTube className="h-4 w-4 mr-2" />
                    Test Printer
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Test the printer connection and print a sample receipt
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Workstation Assignment */}
          <Card>
            <CardHeader>
              <CardTitle>Workstation Assignment</CardTitle>
              <CardDescription>
                Assign this printer to specific workstations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="workstationIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Workstations</FormLabel>
                    <FormDescription>
                      Select workstations that can use this printer
                    </FormDescription>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      {loadingWorkstations ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          Loading workstations...
                        </div>
                      ) : workstations.length === 0 ? (
                        <div className="col-span-2 text-center text-muted-foreground">
                          No workstations available
                        </div>
                      ) : (
                        workstations.map((workstation) => (
                          <div key={workstation.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                            <Checkbox
                              id={workstation.id?.toString()}
                              checked={field.value?.includes(workstation.id?.toString()) || false}
                              onCheckedChange={(checked) => {
                                const currentValue = field.value || [];
                                const workstationId = workstation.id?.toString();
                                if (checked && workstationId) {
                                  field.onChange([...currentValue, workstationId]);
                                } else if (workstationId) {
                                  field.onChange(currentValue.filter(id => id !== workstationId));
                                }
                              }}
                            />
                            <Label htmlFor={workstation.id?.toString()} className="font-medium">
                              {workstation.name} ({workstation.Workstation_code})
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Print Routing */}
          <Card>
            <CardHeader>
              <CardTitle>Print Job Routing</CardTitle>
              <CardDescription>
                Configure which menu items and categories route to this printer
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="menuItemRouting"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Menu Items</FormLabel>
                    <FormDescription>
                      Select specific menu items that should print to this printer
                    </FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                      {menuItemOptions.map((item) => (
                        <div key={item} className="flex items-center space-x-2">
                          <Checkbox
                            id={`menu-${item}`}
                            checked={field.value.includes(item)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, item]);
                              } else {
                                field.onChange(field.value.filter(i => i !== item));
                              }
                            }}
                          />
                          <Label htmlFor={`menu-${item}`} className="text-sm capitalize">
                            {item.replace('-', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryRouting"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categories</FormLabel>
                    <FormDescription>
                      Select categories that should route to this printer
                    </FormDescription>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                      {categoryOptions.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={field.value.includes(category)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                field.onChange([...field.value, category]);
                              } else {
                                field.onChange(field.value.filter(c => c !== category));
                              }
                            }}
                          />
                          <Label htmlFor={`category-${category}`} className="text-sm capitalize">
                            {category.replace('-', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="backupPrinterId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Backup Printer</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select backup printer (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="none">No backup printer</SelectItem>
                        {loadingPrinters ? (
                          <SelectItem value="loading" disabled>Loading printers...</SelectItem>
                        ) : printers.length > 0 ? (
                          printers
                            .filter(printer => printer.id) // Filter out printers without IDs
                            .map((printer) => (
                              <SelectItem key={printer.id} value={printer.id.toString()}>
                                {printer.name}
                              </SelectItem>
                            ))
                        ) : (
                          <SelectItem value="no-printers" disabled>No other printers available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Fallback printer if this printer becomes unavailable
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button
              type="button"
              variant="secondary"
              onClick={testFormData}
            >
              🐛 Debug Form
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/pos/printers')}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || isUpdating || isLoadingPrinter}>
              <Save className="h-4 w-4 mr-2" />
              {(isCreating || isUpdating) ? 'Saving...' : mode === 'create' ? 'Create Printer' : 'Update Printer'}
            </Button>
          </div>
        </form>
      </Form>
      </div>
    </Screen>
  );
};

export default PrinterForm;
