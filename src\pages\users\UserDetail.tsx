import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Shield, Trash2, User, Mail, Phone, Calendar, Building, UserCheck } from 'lucide-react';
import { useRetrieveUserQuery } from '@/redux/slices/user';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetUserRolesQuery } from '@/redux/slices/userRoles';
import { Skeleton } from '@/components/ui/skeleton';

export default function UserDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // API hooks
  const { data: user, isLoading: loadingUser, error: userError } = useRetrieveUserQuery(Number(id));
  const { data: branches = [] } = useGetBranchesQuery({});
  const { data: userRoles = [] } = useGetUserRolesQuery({});

  // Find related data
  const userBranch = branches.find(branch => branch.id === user?.branch);
  const userRole = userRoles.find(role => role.id === user?.role);

  if (loadingUser) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  if (userError || !user) {
    return (
      <div className="container mx-auto py-6">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading User</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              {userError ? 'Failed to load user details.' : 'User not found.'}
            </p>
            <Button 
              variant="outline" 
              onClick={() => navigate('/admin/users')}
              className="mt-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const fullName = user.fullnames || (user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : 'Unknown User');

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{fullName}</h1>
            <p className="text-muted-foreground">User Details</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/admin/users/${id}/edit`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit User
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate(`/admin/users/${id}/permissions`)}
          >
            <Shield className="h-4 w-4 mr-2" />
            Permissions
          </Button>
        </div>
      </div>

      {/* User Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Full Name</label>
              <p className="text-sm">{fullName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Username</label>
              <p className="text-sm">{user.username || 'N/A'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Employee Number</label>
              <p className="text-sm">{user.employee_no || 'N/A'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Email</label>
              <p className="text-sm flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                {user.email || 'N/A'}
              </p>
            </div>
            {user.phone && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Phone</label>
                <p className="text-sm flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  {user.phone}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Work Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Work Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Role</label>
              <p className="text-sm">
                {userRole ? userRole.name : `Role ID: ${user.role}`}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Branch</label>
              <p className="text-sm">
                {userBranch ? `${userBranch.name} (${userBranch.branch_code})` : `Branch ID: ${user.branch}`}
              </p>
            </div>
            {user.revenue_center && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Revenue Center</label>
                <p className="text-sm">Revenue Center ID: {user.revenue_center}</p>
              </div>
            )}
            {user.work_station && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Workstation</label>
                <p className="text-sm">Workstation ID: {user.work_station}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserCheck className="h-5 w-5 mr-2" />
              Additional Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {user.dob && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Date of Birth</label>
                <p className="text-sm flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  {new Date(user.dob).toLocaleDateString()}
                </p>
              </div>
            )}
            {user.hire_date && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Hire Date</label>
                <p className="text-sm flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  {new Date(user.hire_date).toLocaleDateString()}
                </p>
              </div>
            )}
            {user.pin && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">PIN</label>
                <p className="text-sm">****</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-muted-foreground">Salary Paid</label>
              <Badge variant={user.is_saliry_paid ? "default" : "secondary"}>
                {user.is_saliry_paid ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Pays Tax</label>
              <Badge variant={user.pays_tax ? "default" : "secondary"}>
                {user.pays_tax ? "Yes" : "No"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Permissions Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Permissions Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Groups</label>
              <p className="text-sm">
                {user.groups && user.groups.length > 0 
                  ? `${user.groups.length} group(s)` 
                  : 'No groups assigned'
                }
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Direct Permissions</label>
              <p className="text-sm">
                {user.user_permissions && user.user_permissions.length > 0 
                  ? `${user.user_permissions.length} permission(s)` 
                  : 'No direct permissions'
                }
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate(`/admin/users/${id}/permissions`)}
              className="w-full"
            >
              <Shield className="h-4 w-4 mr-2" />
              Manage Permissions
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
