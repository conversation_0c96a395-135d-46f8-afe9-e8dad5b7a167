import BaseModal from "@/components/custom/modals/BaseModal";
import { UoMTypes } from "@/types/UoM";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  uom: UoMTypes | null;
}

const UoMDetails = ({ isOpen, onClose, uom }: propTypes) => {
  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Unit of Measure Details"
      description="View unit of measure details"
    >
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm text-gray-500">Name</h3>
            <p className="text-lg font-medium">{uom?.name}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Symbol</h3>
            <p className="text-lg font-medium">{uom?.symbol}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Conversion Base</h3>
            <p className="text-lg font-medium">{uom?.conversion_base}</p>
          </div>
        </div>
      </div>
    </BaseModal>
  );
};

export default UoMDetails;
