import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/custom/Toast/MyToast";
import { useSubmitRFQResponseMutation } from "@/redux/slices/procurement";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Building, 
  Calendar, 
  User, 
  FileText, 
  Edit, 
  MoreHorizontal,
  DollarSign,
  Clock,
  Package
} from "lucide-react";
import EditRFQResponseItem from "./EditRFQResponseItem";

interface ViewRFQResponseProps {
  open: boolean;
  onClose: () => void;
  response: any;
  onSuccess?: () => void;
}

const ViewRFQResponse = ({ open, onClose, response, onSuccess }: ViewRFQResponseProps) => {
  const [editItemModal, setEditItemModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const [submitRFQResponse, { isLoading: isSubmitting }] = useSubmitRFQResponseMutation();

  const handleEditItem = (item: any) => {
    setSelectedItem(item);
    setEditItemModal(true);
  };

  const handleEditItemSuccess = () => {
    // Refresh the response data if needed
    // This would typically trigger a refetch of the response
    onSuccess?.();
  };

  const handleSubmitResponse = async () => {
    if (!response?.id) return;

    try {
      await submitRFQResponse(response.id).unwrap();
      toast.success("RFQ response submitted successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error submitting RFQ response:", error);
      toast.error(error?.data?.message || "Failed to submit RFQ response");
    }
  };

  const formatCurrency = (amount: string | number, currency: string = 'KES') => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${currency} ${numAmount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              RFQ Response Details
            </DialogTitle>
          </DialogHeader>

          {response && (
            <div className="space-y-6">
              {/* Response Header Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Response Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">
                        {response.code || `RESP-${String(response.id).padStart(5, '0')}`}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <span>RFQ: {response.rfq}</span>
                    </div>
                    {response.notes && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-600">Notes:</p>
                        <p className="text-sm">{response.notes}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Supplier & Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <span>{response.supplier || 'Not Assigned'}</span>
                    </div>
                    {response.submitted_at ? (
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">
                            Submitted: {formatDate(response.submitted_at)}
                          </span>
                        </div>
                        {response.submitted_by && (
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="text-sm">By: {response.submitted_by}</span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <Badge variant="outline">Draft</Badge>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Response Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Response Items ({response.response_items?.length || 0})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {response.response_items && response.response_items.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Product</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Unit Price</TableHead>
                          <TableHead>Currency</TableHead>
                          <TableHead>Delivery Time</TableHead>
                          <TableHead>Tax Rate</TableHead>
                          <TableHead>Total</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {response.response_items.map((item: any, index: number) => {
                          const total = (parseFloat(item.unit_price || '0') * parseFloat(item.quantity || '0'));
                          return (
                            <TableRow key={index}>
                              <TableCell>
                                <div>
                                  <p className="font-medium">{item.product || 'N/A'}</p>
                                  <p className="text-sm text-gray-500">
                                    Unit: {item.unit_of_measure || 'N/A'}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>{item.quantity || 'N/A'}</TableCell>
                              <TableCell>
                                {item.unit_price ? (
                                  <span className="font-medium">
                                    {formatCurrency(item.unit_price, item.currency)}
                                  </span>
                                ) : (
                                  <Badge variant="outline" className="text-orange-600">
                                    Not Set
                                  </Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {item.currency ? (
                                  <Badge variant="secondary">{item.currency}</Badge>
                                ) : (
                                  <Badge variant="outline">Not Set</Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {item.delivery_time_days ? (
                                  <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3 text-gray-500" />
                                    <span>{item.delivery_time_days} days</span>
                                  </div>
                                ) : (
                                  <Badge variant="outline">Not Set</Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {item.tax_rate ? (
                                  <Badge variant="secondary">{item.tax_rate}%</Badge>
                                ) : (
                                  <Badge variant="outline">No Tax</Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                {item.total_price ? (
                                  <div className="flex items-center gap-1">
                                    <DollarSign className="h-3 w-3 text-green-600" />
                                    <span className="font-medium text-green-600">
                                      {formatCurrency(item.total_price, item.currency)}
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center gap-1">
                                    <DollarSign className="h-3 w-3 text-gray-400" />
                                    <span className="text-gray-400">
                                      {formatCurrency(total, item.currency)}
                                    </span>
                                  </div>
                                )}
                              </TableCell>
                              <TableCell>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleEditItem(item)}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Item
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>No items in this response</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2">
                {!response.submitted_at && (
                  <Button
                    onClick={handleSubmitResponse}
                    disabled={isSubmitting}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? "Submitting..." : "Submit Response"}
                  </Button>
                )}
                <Button variant="outline" onClick={onClose}>Close</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Item Modal */}
      {editItemModal && selectedItem && (
        <EditRFQResponseItem
          open={editItemModal}
          onClose={() => {
            setEditItemModal(false);
            setSelectedItem(null);
          }}
          item={selectedItem}
          onSuccess={handleEditItemSuccess}
        />
      )}
    </>
  );
};

export default ViewRFQResponse;
