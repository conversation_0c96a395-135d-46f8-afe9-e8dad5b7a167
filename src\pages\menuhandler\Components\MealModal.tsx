import React, { useState } from "react";

interface Meal {
  id?: string;
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  rating?: number;
  prepTime?: string;
  inStock?: boolean;
  revenueCenter?: string; // New field for revenue center
  printer?: 'kitchen' | 'bar' | null; // New field for printer assignment
  archived?: boolean; // New field for archive status
}

interface CartItem {
  id: string;
  title: string;
  price: string;
  quantity: number;
  imageUrl: string;
}

interface MealModalProps {
  meals: Meal[];
  onClose: () => void;
}

export function MealModal({ meals, onClose }: MealModalProps) {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [stockStatus, setStockStatus] = useState<{ [key: string]: boolean }>({});
  const [printerAssignments, setPrinterAssignments] = useState<{ [key: string]: 'kitchen' | 'bar' | null }>({});
  const [archivedMeals, setArchivedMeals] = useState<{ [key: string]: boolean }>({});

  // Initialize stock status, printer assignments, and archived status for meals
  React.useEffect(() => {
    const initialStock: { [key: string]: boolean } = {};
    const initialPrinters: { [key: string]: 'kitchen' | 'bar' | null } = {};
    const initialArchived: { [key: string]: boolean } = {};
    meals.forEach((meal, idx) => {
      const mealId = meal.id || `meal-${idx}`;
      initialStock[mealId] = meal.inStock !== false;
      initialPrinters[mealId] = meal.printer || null;
      initialArchived[mealId] = meal.archived || false;
    });
    setStockStatus(initialStock);
    setPrinterAssignments(initialPrinters);
    setArchivedMeals(initialArchived);
  }, [meals]);

  const addToCart = (meal: Meal, index: number) => {
    const mealId = meal.id || `meal-${index}`;
    const price = meal.price || '$0';

    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === mealId);
      if (existingItem) {
        return prevCart.map(item =>
          item.id === mealId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prevCart, {
          id: mealId,
          title: meal.title,
          price,
          quantity: 1,
          imageUrl: meal.imageUrl
        }];
      }
    });
  };

  const toggleStock = (mealId: string) => {
    setStockStatus(prev => ({
      ...prev,
      [mealId]: !prev[mealId]
    }));
  };

  const toggleArchive = (mealId: string) => {
    setArchivedMeals(prev => ({
      ...prev,
      [mealId]: !prev[mealId]
    }));
  };

  const assignPrinter = (mealId: string, printer: 'kitchen' | 'bar' | null) => {
    setPrinterAssignments(prev => ({
      ...prev,
      [mealId]: printer
    }));
  };

  const removeFromCart = (mealId: string) => {
    setCart(prevCart => prevCart.filter(item => item.id !== mealId));
  };

  const updateQuantity = (mealId: string, newQuantity: number) => {
    if (newQuantity === 0) {
      removeFromCart(mealId);
      return;
    }
    setCart(prevCart =>
      prevCart.map(item =>
        item.id === mealId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0);
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => {
      const price = parseFloat(item.price.replace('$', ''));
      return total + (price * item.quantity);
    }, 0);
  };

  const getCartItemQuantity = (mealId: string) => {
    const item = cart.find(item => item.id === mealId);
    return item ? item.quantity : 0;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
        <div className="sticky top-0 z-10 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 p-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Meals</h2>
            <div className="flex items-center gap-4">
              {cart.length > 0 && (
                <div className="bg-white/20 rounded-full px-4 py-2 text-white">
                  <span className="font-semibold">🛒 {getTotalItems()} items</span>
                  <span className="ml-2">${getTotalPrice().toFixed(2)}</span>
                </div>
              )}
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200 text-white text-xl"
              >
                ✕
              </button>
            </div>
          </div>
        </div>

        <div className="flex h-[calc(90vh-100px)]">
          {/* Meals Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {meals.map((meal, idx) => {
                const mealId = meal.id || `meal-${idx}`;
                const isInStock = stockStatus[mealId];
                const isArchived = archivedMeals[mealId];
                const cartQuantity = getCartItemQuantity(mealId);
                const assignedPrinter = printerAssignments[mealId];

                return (
                  <div
                    key={idx}
                    className={`bg-gradient-to-br from-white to-gray-50 rounded-xl shadow p-4 flex flex-col transition-all duration-200 relative ${
                      !isInStock ? 'opacity-60 grayscale' : 'hover:shadow-md'
                    } ${isArchived ? 'border-2 border-red-300' : ''}`}
                  >
                    {/* Archive Badge */}
                    {isArchived && (
                      <span className="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        Archived
                      </span>
                    )}
                    
                    <div className="relative">
                      <img
                        src={meal.imageUrl}
                        alt={meal.title}
                        className="object-cover w-full h-24 rounded mb-3"
                      />
                      {!isInStock && (
                        <div className="absolute inset-0 bg-black/50 rounded flex items-center justify-center">
                          <span className="text-white font-bold text-sm">OUT OF STOCK</span>
                        </div>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold">{meal.title}</h3>
                    <p className="text-gray-600 text-sm mb-2 flex-1">{meal.description}</p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                      <span>{meal.category}</span>
                      <span>⭐ {meal.rating}</span>
                      <span>⏱ {meal.prepTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between mb-3">
                      <div className="text-orange-600 font-bold text-lg">{meal.price}</div>
                      <div className="flex gap-2">
                        <button
                          onClick={() => toggleStock(mealId)}
                          className={`text-xs px-2 py-1 rounded transition-colors ${
                            isInStock
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                          }`}
                        >
                          {isInStock ? 'In Stock' : 'Out of Stock'}
                        </button>
                        <button
                          onClick={() => toggleArchive(mealId)}
                          className={`text-xs px-2 py-1 rounded transition-colors ${
                            isArchived
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                        >
                          {isArchived ? 'Unarchive' : 'Archive'}
                        </button>
                      </div>
                    </div>

                    {/* Revenue Center and Printer Assignment */}
                    <div className="mb-3 text-sm">
                      <p className="text-gray-600">
                        Revenue Center: {meal.revenueCenter || 'Not assigned'}
                      </p>
                      <div className="flex gap-2 mt-1">
                        <button
                          onClick={() => assignPrinter(mealId, 'kitchen')}
                          className={`text-xs px-2 py-1 rounded transition-colors ${
                            assignedPrinter === 'kitchen'
                              ? 'bg-blue-500 text-white'
                              : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                          }`}
                        >
                          Kitchen
                        </button>
                        <button
                          onClick={() => assignPrinter(mealId, 'bar')}
                          className={`text-xs px-2 py-1 rounded transition-colors ${
                            assignedPrinter === 'bar'
                              ? 'bg-purple-500 text-white'
                              : 'bg-purple-100 text-purple-800 hover:bg-purple-200'
                          }`}
                        >
                          Bar
                        </button>
                        {assignedPrinter && (
                          <button
                            onClick={() => assignPrinter(mealId, null)}
                            className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-800 hover:bg-gray-200"
                          >
                            Clear
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {/* Add to Cart / Quantity Controls */}
                    <div className="flex items-center gap-2">
                      {cartQuantity === 0 ? (
                        <button
                          onClick={() => addToCart(meal, idx)}
                          disabled={!isInStock || isArchived}
                          className={`flex-1 py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${
                            isInStock && !isArchived
                              ? 'bg-orange-500 text-white hover:bg-orange-600 active:scale-95'
                              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          {isInStock ? (isArchived ? 'Archived' : 'Add to Cart') : 'Out of Stock'}
                        </button>
                      ) : (
                        <div className="flex-1 flex items-center justify-center gap-3 bg-orange-50 rounded-lg p-2">
                          <button
                            onClick={() => updateQuantity(mealId, cartQuantity - 1)}
                            className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center hover:bg-orange-600 transition-colors"
                          >
                            -
                          </button>
                          <span className="font-semibold text-orange-600 min-w-[2rem] text-center">
                            {cartQuantity}
                          </span>
                          <button
                            onClick={() => updateQuantity(mealId, cartQuantity + 1)}
                            className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center hover:bg-orange-600 transition-colors"
                          >
                            +
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Cart Sidebar */}
          {cart.length > 0 && (
            <div className="w-80 bg-gray-50 border-l border-gray-200 p-4 overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Your Cart</h3>
              <div className="space-y-3">
                {cart.map((item) => (
                  <div key={item.id} className="bg-white rounded-lg p-3 shadow-sm">
                    <div className="flex items-center gap-3">
                      <img
                        src={item.imageUrl}
                        alt={item.title}
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">{item.title}</h4>
                        <p className="text-orange-600 font-semibold text-sm">{item.price}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                        >
                          -
                        </button>
                        <span className="text-sm font-medium w-6 text-center">{item.quantity}</span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="w-6 h-6 bg-gray-200 rounded text-xs hover:bg-gray-300"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-semibold text-gray-800">Total:</span>
                  <span className="font-bold text-xl text-orange-600">
                    ${getTotalPrice().toFixed(2)}
                  </span>
                </div>
                <button className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-lg font-semibold hover:from-orange-600 hover:to-red-600 transition-all duration-200 active:scale-95">
                  Proceed to Checkout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Demo component to show how to use the modal
export default function App() {
  const [showModal, setShowModal] = useState(false);
  
  const sampleMeals: Meal[] = [
    {
      id: "meal-1",
      title: "Grilled Chicken Breast",
      description: "Tender grilled chicken breast with herbs and spices",
      imageUrl: "https://images.unsplash.com/photo-1532550907401-a500c9a57435?w=300&h=200&fit=crop",
      price: "$12.99",
      category: "Main Course",
      rating: 4.5,
      prepTime: "20 min",
      inStock: true,
      revenueCenter: "Main Kitchen",
      printer: "kitchen",
      archived: false
    },
    {
      id: "meal-2",
      title: "Beef Burger Deluxe",
      description: "Juicy beef patty with lettuce, tomato, and special sauce",
      imageUrl: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300&h=200&fit=crop",
      price: "$15.99",
      category: "Fast Food",
      rating: 4.2,
      prepTime: "15 min",
      inStock: false,
      revenueCenter: "Fast Food Counter",
      printer: "kitchen",
      archived: false
    },
    {
      id: "meal-3",
      title: "Caesar Salad",
      description: "Fresh romaine lettuce with parmesan and croutons",
      imageUrl: "https://images.unsplash.com/photo-1546793665-c74683f339c1?w=300&h=200&fit=crop",
      price: "$8.99",
      category: "Salad",
      rating: 4.0,
      prepTime: "10 min",
      inStock: true,
      revenueCenter: "Salad Station",
      printer: "kitchen",
      archived: false
    },
    {
      id: "meal-4",
      title: "Margherita Pizza",
      description: "Classic pizza with tomato sauce, mozzarella, and fresh basil",
      imageUrl: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop",
      price: "$14.99",
      category: "Pizza",
      rating: 4.7,
      prepTime: "25 min",
      inStock: true,
      revenueCenter: "Pizza Oven",
      printer: "kitchen",
      archived: false
    },
    {
      id: "meal-5",
      title: "Chocolate Cake",
      description: "Rich chocolate cake with creamy frosting",
      imageUrl: "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=300&h=200&fit=crop",
      price: "$6.99",
      category: "Dessert",
      rating: 4.8,
      prepTime: "5 min",
      inStock: true,
      revenueCenter: "Dessert Counter",
      printer: null,
      archived: false
    },
    {
      id: "meal-6",
      title: "Fish and Chips",
      description: "Crispy battered fish with golden fries",
      imageUrl: "https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=300&h=200&fit=crop",
      price: "$13.99",
      category: "Main Course",
      rating: 4.3,
      prepTime: "18 min",
      inStock: false,
      revenueCenter: "Main Kitchen",
      printer: "kitchen",
      archived: true
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-8">
      <div className="max-w-md mx-auto bg-white rounded-2xl shadow-lg p-8">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Restaurant Menu
        </h1>
        <button
          onClick={() => setShowModal(true)}
          className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-4 rounded-xl font-semibold text-lg hover:from-orange-600 hover:to-red-600 transition-all duration-200 active:scale-95 shadow-lg"
        >
          View Menu & Order
        </button>
        
        <div className="mt-6 text-center text-gray-600">
          <p className="text-sm">✨ Features:</p>
          <ul className="text-xs mt-2 space-y-1">
            <li>• Add items to cart with quantity controls</li>
            <li>• Toggle stock availability</li>
            <li>• Archive/unarchive items</li>
            <li>• Assign printer (Kitchen/Bar)</li>
            <li>• Revenue center assignment</li>
            <li>• Real-time cart updates</li>
            <li>• Responsive design</li>
          </ul>
        </div>
      </div>

      {showModal && (
        <MealModal
          meals={sampleMeals}
          onClose={() => setShowModal(false)}
        />
      )}
    </div>
  );
}