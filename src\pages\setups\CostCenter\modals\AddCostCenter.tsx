import BaseModal from "@/components/custom/modals/BaseModal";
import CustomSelectField from "@/components/CustomSelectField";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  useAddCostCentersMutation,
  usePatchCostCentersMutation,
} from "@/redux/slices/costCenter";
import { useLazyGetStoresQuery } from "@/redux/slices/store";
import { costCenterType } from "@/types/costCenter";
import { genRandomString } from "@/utils/helpers";
import { Loader2, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: costCenterType;
  refetch: () => void;
}

const AddCostCenter = ({ isOpen, onClose, updateData, refetch }: propTypes) => {
  const [createCostCenter, { isLoading: loading }] =
    useAddCostCentersMutation();
  const [updateCostCenter, { isLoading: loadingUpdate }] =
    usePatchCostCentersMutation();

  const [fetchStores, { data: stores, isLoading: loadingStores }] =
    useLazyGetStoresQuery();

  const [formData, setFormData] = useState({
    name: updateData ? updateData.name : "",
    description: updateData ? updateData.description : "",
    default_store: updateData ? updateData.default_store : "",
    budget_limit: updateData ? updateData.budget_limit : "",
    budget_frequency: updateData ? updateData.budget_frequency : "MONTHLY",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCheckboxChange = (name: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: !prev[name as keyof typeof prev],
    }));
  };

  const handleAddCostCenter = async (e: React.FormEvent) => {
    e.preventDefault();
    const code = genRandomString(6);
    try {
      let res: any;
      if (updateData) {
        res = await updateCostCenter({
          ...formData,
          id: updateData.id,
        }).unwrap();
      } else {
        res = await createCostCenter({
          ...formData,
          code: `CC${code.toLowerCase()}`,
        }).unwrap();
      }
      if (res?.id) {
        setFormData({
          name: updateData ? updateData.name : "",
          description: updateData ? updateData.description : "",
          default_store: updateData ? updateData.default_store : "",
          budget_limit: updateData ? updateData.budget_limit : "",
          budget_frequency: updateData
            ? updateData.budget_frequency
            : "MONTHLY",
        });
        onClose();
        refetch();
        toast.success("Cost Center added successfully");
      }
    } catch (error: any) {
      let errorMsg =
        error?.data?.message || "Failed to add cost center. Please try again.";
      toast.error(
        typeof errorMsg === "string" ? errorMsg : JSON.stringify(errorMsg)
      );
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Cost Center" : "Add Cost Center"}
      description="Enter cost center details"
    >
      <form onSubmit={handleAddCostCenter}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter cost center name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="default_store">
              Store*
              {updateData && (
                <span className="text-sm text-muted-foreground">
                  (Current: {updateData?.default_store})
                </span>
              )}
            </Label>

            <CustomSelectField
              setValue={(e: any) =>
                handleInputChange({
                  target: { name: "default_store", value: e },
                } as any)
              }
              useSearchField
              valueField="code"
              labelField="name"
              data={stores?.data?.results || []}
              queryFunc={fetchStores}
              loader={loadingStores}
              isMultiple={false}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="budget_limit">Budget Limit*</Label>
            <Input
              id="budget_limit"
              name="budget_limit"
              type="number"
              value={formData.budget_limit}
              onChange={handleInputChange}
              placeholder="Enter budget limit"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="budget_frequency">Budget Frequency*</Label>
            <Select
              name="budget_frequency"
              value={formData.budget_frequency}
              onValueChange={(value) =>
                handleInputChange({
                  target: { name: "budget_frequency", value },
                } as any)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select frequency" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MONTHLY">Monthly</SelectItem>
                <SelectItem value="ANNUAL">Annual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter description"
            />
          </div>
        </div>

        <div className="w-full flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {loading || loadingUpdate ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </Button>
          ) : (
            <Button type="submit">
              {updateData ? "Update Cost Center" : "Add Cost Center"}
              <Send className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddCostCenter;
