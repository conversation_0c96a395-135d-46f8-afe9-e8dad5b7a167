import { apiSlice } from "../apiSlice";

// Types for Workstation API based on API specification
export interface Workstation {
  id?: number;
  workstation_code: string;
  name: string;
  is_active?: boolean;
  role?: 'POS' | 'Order Only' | 'self Service' | 'Kitchen dislay' | 'Bar' | 'Tablet' | 'Mobile';
  ip_address?: string;
  hostname?: string;
  supports_magnetic_card?: boolean;
  supports_employee_login?: boolean;
  language?: string;
  branch: string;
  revenue_center?: string;
  linked_printer?: string;
}

export const workstationApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all workstations with optional filtering
    getWorkstations: builder.query<Workstation[], {
      branch?: string;
      branch_id?: string;
      revenue_center?: string;
      revenue_center_id?: string;
      is_active?: boolean;
      role?: string;
      search?: string;
    }>({
      query: (params = {}) => ({
        url: "/setup/workstations",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        // Handle different response structures
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          } else if (response.data.workstations && Array.isArray(response.data.workstations)) {
            return response.data.workstations;
          }
        }
        // Fallback to empty array if structure is unexpected
        console.warn("Unexpected workstations API response structure:", response);
        return [];
      },
      providesTags: ["Workstations"],
    }),

    retrieveWorkstation: builder.query<Workstation, string>({
      query: (id) => ({
        url: `/setup/workstations/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Workstations", id }],
    }),

    addWorkstation: builder.mutation<Workstation, Partial<Workstation>>({
      query: (payload) => ({
        url: "/setup/workstations",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Workstations"],
    }),

    patchWorkstation: builder.mutation<Workstation, { id: string; data: Partial<Workstation> }>({
      query: ({ id, data }) => ({
        url: `/setup/workstations/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Workstations", id }, "Workstations"],
    }),

    deleteWorkstation: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/workstations/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Workstations"],
    }),
  }),
});

export const {
  useGetWorkstationsQuery,
  useRetrieveWorkstationQuery,
  useAddWorkstationMutation,
  usePatchWorkstationMutation,
  useDeleteWorkstationMutation,

  useLazyGetWorkstationsQuery,
  useLazyRetrieveWorkstationQuery,
} = workstationApiSlice;
