export interface Product {
  id: string;
  name: string;
  code: string;
  category: string;
  unitOfMeasure: string;
  cost: number;
  taxCode: 'VAT' | 'No VAT';
  perishable: boolean;
  shelfLifeDays?: number;
  expiryDate?: string;
  defaultSupplier?: string;
  active: boolean;
  stockLevel?: number;
  minStockLevel?: number;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
}

export interface UnitOfMeasure {
  id: string;
  name: string;
  abbreviation: string;
  type: 'weight' | 'volume' | 'count' | 'length';
}

export interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
}

export interface ProductFormData {
  name: string;
  code: string;
  category: string;
  unitOfMeasure: string;
  cost: string;
  taxCode: 'VAT' | 'No VAT';
  perishable: boolean;
  shelfLifeDays: string;
  expiryDate: string;
  defaultSupplier: string;
  active: boolean;
}

export interface FormErrors {
  name?: string;
  code?: string;
  category?: string;
  unitOfMeasure?: string;
  cost?: string;
  shelfLifeDays?: string;
  expiryDate?: string;
}