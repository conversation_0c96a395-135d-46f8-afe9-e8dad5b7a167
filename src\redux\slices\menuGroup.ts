import { apiSlice } from "../apiSlice";

export const menuGroupApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getMenuGroups: builder.query({
      query: (params) => ({
        url: "/menu/menu-groups",
        method: "GET",
        params: params,
      }),
      providesTags: ["MenuGroups"],
    }),

    retrieveMenuGroup: builder.query({
      query: (id) => ({
        url: `/menu/menu-groups/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "MenuGroups", id }],
    }),

    addMenuGroup: builder.mutation({
      query: (payload) => ({
        url: "/menu/menu-groups",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["MenuGroups"],
    }),

    patchMenuGroup: builder.mutation({
      query: (payload) => ({
        url: `/menu/menu-groups/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "MenuGroups", id },
        "MenuGroups",
      ],
    }),

    deleteMenuGroup: builder.mutation({
      query: (id) => ({
        url: `/menu/menu-groups/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["MenuGroups"],
    }),
  }),
});

export const {
  useGetMenuGroupsQuery,
  useRetrieveMenuGroupQuery,
  useAddMenuGroupMutation,
  usePatchMenuGroupMutation,
  useDeleteMenuGroupMutation,

  useLazyGetMenuGroupsQuery,
  useLazyRetrieveMenuGroupQuery,
} = menuGroupApiSlice;
