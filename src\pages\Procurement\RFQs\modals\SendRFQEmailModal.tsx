import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Mail, 
  Plus, 
  X, 
  Loader2, 
  FileText, 
  AlertTriangle,
  Send,
  Users
} from "lucide-react";
import { RFQ } from "@/types/procurement";
import { 
  createRFQEmailData, 
  validateEmailAddresses, 
  getDefaultRFQEmailRecipients,
  generateRFQEmailTemplate 
} from "@/utils/rfqEmail";
import { toast } from "@/components/custom/Toast/MyToast";

interface SendRFQEmailModalProps {
  open: boolean;
  onClose: () => void;
  rfq: RFQ;
  onSend: (emailData: any) => Promise<void>;
  isLoading?: boolean;
}

const SendRFQEmailModal = ({ 
  open, 
  onClose, 
  rfq, 
  onSend, 
  isLoading = false 
}: SendRFQEmailModalProps) => {
  const [emailData, setEmailData] = useState({
    to: getDefaultRFQEmailRecipients(rfq),
    cc: [] as string[],
    bcc: [] as string[],
    template: 'standard' as 'standard' | 'urgent' | 'reminder' | 'cancellation',
    customMessage: '',
    attachPDF: true,
  });

  const [newEmail, setNewEmail] = useState('');
  const [emailType, setEmailType] = useState<'to' | 'cc' | 'bcc'>('to');
  const [previewMode, setPreviewMode] = useState(false);

  const addEmail = () => {
    if (!newEmail.trim()) return;
    
    if (!validateEmailAddresses([newEmail])) {
      toast.error("Please enter a valid email address");
      return;
    }

    const currentEmails = emailData[emailType];
    if (currentEmails.includes(newEmail)) {
      toast.error("Email address already added");
      return;
    }

    setEmailData(prev => ({
      ...prev,
      [emailType]: [...prev[emailType], newEmail]
    }));
    setNewEmail('');
  };

  const removeEmail = (email: string, type: 'to' | 'cc' | 'bcc') => {
    setEmailData(prev => ({
      ...prev,
      [type]: prev[type].filter(e => e !== email)
    }));
  };

  const handleSend = async () => {
    if (emailData.to.length === 0) {
      toast.error("Please add at least one recipient");
      return;
    }

    if (!validateEmailAddresses([...emailData.to, ...emailData.cc, ...emailData.bcc])) {
      toast.error("Please check all email addresses are valid");
      return;
    }

    try {
      const emailPayload = createRFQEmailData(rfq, {
        to: emailData.to,
        cc: emailData.cc,
        bcc: emailData.bcc,
        template: emailData.template,
        customMessage: emailData.customMessage,
        attachPDF: emailData.attachPDF,
      });

      await onSend(emailPayload);
      onClose();
      toast.success("RFQ sent successfully");
    } catch (error: any) {
      toast.error(error?.message || "Failed to send RFQ");
    }
  };

  const getTemplateDescription = (template: string) => {
    const descriptions = {
      standard: "Standard RFQ invitation with all details",
      urgent: "Urgent RFQ requiring immediate attention",
      reminder: "Reminder for approaching deadline",
      cancellation: "RFQ cancellation notification"
    };
    return descriptions[template as keyof typeof descriptions] || descriptions.standard;
  };

  const getTemplateColor = (template: string) => {
    const colors = {
      standard: "bg-blue-100 text-blue-800",
      urgent: "bg-red-100 text-red-800",
      reminder: "bg-yellow-100 text-yellow-800",
      cancellation: "bg-gray-100 text-gray-800"
    };
    return colors[template as keyof typeof colors] || colors.standard;
  };

  const previewTemplate = generateRFQEmailTemplate(
    rfq,
    emailData.template,
    emailData.customMessage
  );

  // Check if deadline is approaching (within 24 hours)
  const isDeadlineApproaching = rfq.response_deadline && 
    new Date(rfq.response_deadline).getTime() - new Date().getTime() < 24 * 60 * 60 * 1000;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Send RFQ - {rfq.rfq_number}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {isDeadlineApproaching && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Deadline Approaching</span>
              </div>
              <p className="text-yellow-700 text-sm mt-1">
                The response deadline for this RFQ is within 24 hours. Consider using the "Urgent" template.
              </p>
            </div>
          )}

          {!previewMode ? (
            <>
              {/* Email Recipients */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Recipients
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Add Email Input */}
                  <div className="flex gap-2">
                    <Select value={emailType} onValueChange={(value: 'to' | 'cc' | 'bcc') => setEmailType(value)}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="to">To</SelectItem>
                        <SelectItem value="cc">CC</SelectItem>
                        <SelectItem value="bcc">BCC</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Enter supplier email address"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addEmail()}
                      className="flex-1"
                    />
                    <Button onClick={addEmail} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Email Lists */}
                  {(['to', 'cc', 'bcc'] as const).map(type => (
                    emailData[type].length > 0 && (
                      <div key={type}>
                        <Label className="text-sm font-medium capitalize">{type}:</Label>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {emailData[type].map(email => (
                            <Badge key={email} variant="secondary" className="flex items-center gap-1">
                              {email}
                              <X 
                                className="h-3 w-3 cursor-pointer" 
                                onClick={() => removeEmail(email, type)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )
                  ))}

                  {rfq.supplier_names && rfq.supplier_names.length > 0 && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                      <p className="text-sm text-blue-800 font-medium mb-2">Selected Suppliers:</p>
                      <div className="flex flex-wrap gap-2">
                        {rfq.supplier_names.map((supplier, index) => (
                          <Badge key={index} variant="outline" className="text-blue-700">
                            {supplier}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-xs text-blue-600 mt-2">
                        Add supplier email addresses above to send this RFQ
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Email Template */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Email Template</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Template Type</Label>
                    <Select 
                      value={emailData.template} 
                      onValueChange={(value: 'standard' | 'urgent' | 'reminder' | 'cancellation') => 
                        setEmailData(prev => ({ ...prev, template: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">Standard RFQ</SelectItem>
                        <SelectItem value="urgent">Urgent RFQ</SelectItem>
                        <SelectItem value="reminder">Deadline Reminder</SelectItem>
                        <SelectItem value="cancellation">Cancellation</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-600 mt-1">
                      {getTemplateDescription(emailData.template)}
                    </p>
                  </div>

                  <div>
                    <Label>Custom Message (Optional)</Label>
                    <Textarea
                      placeholder="Add any additional message or special instructions..."
                      value={emailData.customMessage}
                      onChange={(e) => setEmailData(prev => ({ ...prev, customMessage: e.target.value }))}
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attachPDF"
                      checked={emailData.attachPDF}
                      onCheckedChange={(checked) => 
                        setEmailData(prev => ({ ...prev, attachPDF: checked as boolean }))
                      }
                    />
                    <Label htmlFor="attachPDF" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Attach PDF copy of RFQ
                    </Label>
                  </div>
                </CardContent>
              </Card>

              {/* Email Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Email Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="font-medium">Subject:</span>
                      <span className="text-sm">{previewTemplate.subject}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Template:</span>
                      <Badge className={getTemplateColor(emailData.template)}>
                        {emailData.template.charAt(0).toUpperCase() + emailData.template.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Recipients:</span>
                      <span className="text-sm">{emailData.to.length} recipient(s)</span>
                    </div>
                    {emailData.attachPDF && (
                      <div className="flex justify-between">
                        <span className="font-medium">Attachment:</span>
                        <span className="text-sm flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          PDF RFQ Document
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            /* Email Preview */
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Email Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
                  <div className="mb-4 pb-2 border-b">
                    <p><strong>Subject:</strong> {previewTemplate.subject}</p>
                    <p><strong>To:</strong> {emailData.to.join(', ')}</p>
                    {emailData.cc.length > 0 && <p><strong>CC:</strong> {emailData.cc.join(', ')}</p>}
                  </div>
                  <div 
                    dangerouslySetInnerHTML={{ __html: previewTemplate.body }}
                    className="prose prose-sm max-w-none"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setPreviewMode(!previewMode)}
            >
              {previewMode ? 'Edit' : 'Preview'}
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSend} 
              disabled={isLoading || emailData.to.length === 0}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send RFQ
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SendRFQEmailModal;
