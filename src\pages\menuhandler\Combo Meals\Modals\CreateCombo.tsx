import React, { useState, useEffect } from "react";
import { X, Utensils, Clock, Plus, Trash2, ShoppingCart, Star, Zap } from "lucide-react";
import { useGetRecipesQuery } from "@/redux/slices/recipe";
import { useGetRecipeGroupsQuery } from "@/redux/slices/recipeGroup";
import { useAddComboMenuMutation } from "@/redux/slices/comboMenu";
import { useGetTaxRatesQuery } from "@/redux/slices/taxRates";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";

interface ComboMealItem {
  id: string;
  name: string;
  category: string;
  price: number;
  quantity: number;
  recipeId: number;
}

interface CreateComboMealModalProps {
  onClose: () => void;
  onSave: (newCombo: {
    name: string;
    code: string;
    price: string;
    description: string;
    tax_class: number;
    menu_group: number;
    items: ComboMealItem[];
    imageUrl: string;
    prepTime: string;
  }) => void;
}

interface RecipeGroup {
  id: number;
  name: string;
  description: string;
}

interface MenuGroup {
  id: number;
  name: string;
  sales_category: string;
  position: number;
  menu: number;
  tax_class: number;
}

interface Recipe {
  id: number;
  name: string;
  recipe_type: string;
  portion_size: string;
  image: string;
  preparation_time: number;
  cooking_time: number;
  instructions: string;
  tools_required: string;
  dietary_flags: string[];
  is_active: boolean;
  recipe_group: number;
  location: string;
  menu_item: number;
}

export function CreateComboMealModal({ onClose, onSave }: CreateComboMealModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    price: "0.00",
    description: "",
    tax_class: 0,
    menu_group: 0,
    sales_category: "",
    items: [] as ComboMealItem[],
    imageUrl: "",
    prepTime: "",
  });

  const { data: recipeGroupData } = useGetRecipeGroupsQuery({});
  const { data: recipeData } = useGetRecipesQuery({});
  const { data: menuGroups } = useGetMenuGroupsQuery({});
  const { data: taxData } = useGetTaxRatesQuery({});
  const [addCombo, { isLoading }] = useAddComboMenuMutation();

  const [selectedRecipeGroup, setSelectedRecipeGroup] = useState("");
  const [selectedRecipe, setSelectedRecipe] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [filteredRecipes, setFilteredRecipes] = useState<Recipe[]>([]);

  const recipeGroups: RecipeGroup[] = recipeGroupData?.data?.results || [];
  const recipes: Recipe[] = recipeData?.data?.results || [];
  const foodMenuGroups = menuGroups?.data?.results?.filter(group => group.sales_category === "FOOD") || [];

  useEffect(() => {
    if (selectedRecipeGroup) {
      const groupId = parseInt(selectedRecipeGroup);
      const filtered = recipes.filter(recipe => recipe.recipe_group === groupId && recipe.is_active);
      setFilteredRecipes(filtered);
    } else {
      setFilteredRecipes([]);
    }
    setSelectedRecipe("");
  }, [selectedRecipeGroup, recipes]);

  const totalPrice = formData.items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, imageUrl }));
    }
  };

  const handleAddItem = () => {
    if (!selectedRecipe || quantity < 1) return;

    const recipe = filteredRecipes.find(r => r.id.toString() === selectedRecipe);
    const recipeGroup = recipeGroups.find(rg => rg.id.toString() === selectedRecipeGroup);
    
    if (recipe && recipeGroup) {
      const basePrice = recipe.recipe_type === "Serving" ? 12.99 : 7.99;
      const timeMultiplier = recipe.preparation_time > 30 ? 1.2 : 1.0;
      const finalPrice = basePrice * timeMultiplier;
      
      setFormData(prev => ({
        ...prev,
        items: [
          ...prev.items,
          { 
            id: `${recipe.id}-${Date.now()}`,
            name: recipe.name, 
            category: recipeGroup.name, 
            price: finalPrice,
            quantity,
            recipeId: recipe.id
          },
        ],
      }));
      setSelectedRecipe("");
      setQuantity(1);
    }
  };

  const handleRemoveItem = (id: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id),
    }));
  };

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      ),
    }));
  };

  const handleMenuGroupChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = parseInt(e.target.value);
    const selectedGroup = foodMenuGroups.find(group => group.id === selectedId);
    
    setFormData(prev => ({
      ...prev,
      menu_group: selectedId,
      tax_class: selectedGroup?.tax_class || 0,
      sales_category: selectedGroup?.sales_category || ""
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const comboData = {
        name: formData.name,
        code: formData.code,
        price: totalPrice.toFixed(2),
        description: formData.description,
        tax_class: formData.tax_class,
        menu_group: formData.menu_group,
        sales_category: formData.sales_category,
        items: formData.items.map(item => ({
          recipe: item.recipeId,
          quantity: item.quantity
        }))
      };

      await addCombo(comboData).unwrap();
      onSave({ ...formData, price: totalPrice.toFixed(2) });
      onClose();
    } catch (error) {
      console.error('Failed to create combo meal:', error);
    }
  };

  const selectedTaxRate = taxData?.data?.results?.find(tax => tax.id === formData.tax_class);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-gradient-to-br from-orange-900/50 via-red-900/50 to-yellow-900/50 backdrop-blur-md animate-in fade-in duration-500"
        onClick={onClose}
      />

      <div className="relative bg-white rounded-3xl shadow-2xl w-full max-w-2xl max-h-[95vh] overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-500">
        <div className="relative bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 p-8 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
            <div className="absolute top-8 right-8 w-16 h-16 bg-white/10 rounded-full animate-bounce delay-300"></div>
            <div className="absolute bottom-4 left-1/3 w-12 h-12 bg-white/10 rounded-full animate-ping delay-700"></div>
          </div>
          
          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/30">
                <Utensils className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-3xl font-bold text-white">Create Combo Meal</h2>
                <p className="text-white/90 text-sm flex items-center mt-1">
                  <Star className="h-4 w-4 mr-1" />
                  Build your ultimate food combination
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-xl transition-all duration-200 hover:scale-110"
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(95vh-200px)]">
          <form onSubmit={handleSubmit} className="p-8 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="group">
                <label className="block text-sm font-bold text-gray-800 mb-2 flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-purple-600" />
                  Combo Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Enter your awesome combo name..."
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300 bg-gradient-to-r from-gray-50 to-white group-hover:shadow-md"
                />
              </div>
              <div className="group">
                <label className="block text-sm font-bold text-gray-800 mb-2 flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-purple-600" />
                  Combo Code
                </label>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleChange}
                  required
                  placeholder="Enter unique combo code..."
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300 bg-gradient-to-r from-gray-50 to-white group-hover:shadow-md"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-bold text-gray-800 mb-2">Menu Group</label>
              <select
                name="menu_group"
                value={formData.menu_group}
                onChange={handleMenuGroupChange}
                className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-200"
                required
              >
                <option value="0">Select Menu Group</option>
                {foodMenuGroups.map(group => (
                  <option key={group.id} value={group.id}>
                    {group.name}
                  </option>
                ))}
              </select>
            </div>

            {formData.tax_class > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm font-medium text-gray-700">
                  Tax Class: <span className="font-semibold">
                    {selectedTaxRate?.name || 'N/A'} ({selectedTaxRate?.rate || 0}%)
                  </span>
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Automatically set based on selected menu group
                </p>
              </div>
            )}

            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <Plus className="h-5 w-5 mr-2 text-blue-600" />
                Add Items to Combo
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipe Category</label>
                  <select
                    value={selectedRecipeGroup}
                    onChange={(e) => setSelectedRecipeGroup(e.target.value)}
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200"
                  >
                    <option value="">Choose category</option>
                    {recipeGroups.map(group => (
                      <option key={group.id} value={group.id}>
                        {group.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Recipe Item</label>
                  <select
                    value={selectedRecipe}
                    onChange={(e) => setSelectedRecipe(e.target.value)}
                    disabled={!selectedRecipeGroup}
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200 disabled:bg-gray-100"
                  >
                    <option value="">
                      {selectedRecipeGroup ? "Select recipe" : "Choose category first"}
                    </option>
                    {filteredRecipes.map(recipe => (
                      <option key={recipe.id} value={recipe.id}>
                        {recipe.name} ({recipe.recipe_type})
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="flex items-end space-x-3 mt-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                  <input
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                    min="1"
                    className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-0 transition-all duration-200"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleAddItem}
                  disabled={!selectedRecipe}
                  className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:from-gray-300 disabled:to-gray-400 transition-all duration-200 hover:shadow-lg transform hover:scale-105 disabled:transform-none flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Item</span>
                </button>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-800 flex items-center">
                  <ShoppingCart className="h-5 w-5 mr-2 text-green-600" />
                  Selected Items ({formData.items.length})
                </h3>
                <div className="text-2xl font-bold text-green-600">
                  ${totalPrice.toFixed(2)}
                </div>
              </div>
              
              {formData.items.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                  <p className="text-gray-500">No items added yet. Start building your combo!</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {formData.items.map(item => (
                    <div key={item.id} className="bg-white rounded-xl p-4 shadow-sm border border-green-100 hover:shadow-md transition-all duration-200">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-800">{item.name}</h4>
                          <p className="text-sm text-gray-600">{item.category}</p>
                          <p className="text-lg font-bold text-green-600">${(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            <label className="text-sm font-medium text-gray-700">Qty:</label>
                            <input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                              min="1"
                              className="w-16 px-2 py-1 rounded border border-gray-300 focus:border-green-500 focus:ring-0 text-center"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveItem(item.id)}
                            className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200 hover:scale-110"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Description</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows={4}
                  placeholder="Describe the combo, including ingredients and nutritional info..."
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300 resize-none"
                />
              </div>
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2 flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-purple-600" />
                  Preparation Time
                </label>
                <input
                  type="text"
                  name="prepTime"
                  value={formData.prepTime}
                  onChange={handleChange}
                  required
                  placeholder="e.g., 20-25 minutes"
                  className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 focus:border-purple-500 focus:ring-0 transition-all duration-300"
                />
              </div>
              <div>
                <label className="block text-sm font-bold text-gray-800 mb-2">Combo Image</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="w-full px-4 py-3 rounded-xl border-2 border-dashed border-gray-300 focus:border-purple-500 transition-all duration-300 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-purple-100 file:text-purple-700 hover:file:bg-purple-200"
                  />
                  {formData.imageUrl && (
                    <div className="mt-3">
                      <img 
                        src={formData.imageUrl} 
                        alt="Preview" 
                        className="h-24 w-24 object-cover rounded-xl shadow-md border-2 border-purple-200" 
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="pt-6">
              <button
                type="submit"
                disabled={formData.items.length === 0 || isLoading}
                className="w-full px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02] disabled:transform-none disabled:from-gray-400 disabled:to-gray-500 flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <Star className="h-5 w-5" />
                    <span className="text-lg">Create Combo Meal</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}