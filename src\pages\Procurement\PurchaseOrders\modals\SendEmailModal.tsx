import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Mail, 
  Plus, 
  X, 
  Loader2, 
  FileText, 
  AlertTriangle,
  CheckCircle,
  Send
} from "lucide-react";
import { PurchaseOrder } from "@/types/procurement";
import { 
  createEmailData, 
  validateEmailAddresses, 
  getDefaultEmailRec<PERSON><PERSON>,
  generateEmailTemplate 
} from "@/utils/purchaseOrderEmail";
import { toast } from "@/components/custom/Toast/MyToast";

interface SendEmailModalProps {
  open: boolean;
  onClose: () => void;
  purchaseOrder: PurchaseOrder;
  onSend: (emailData: any) => Promise<void>;
  isLoading?: boolean;
}

const SendEmailModal = ({ 
  open, 
  onClose, 
  purchaseOrder, 
  onSend, 
  isLoading = false 
}: SendEmailModalProps) => {
  const [emailData, setEmailData] = useState({
    to: getDefaultEmailRecipients(purchaseOrder),
    cc: [] as string[],
    bcc: [] as string[],
    template: 'standard' as 'standard' | 'urgent' | 'revision' | 'cancellation',
    customMessage: '',
    attachPDF: true,
  });

  const [newEmail, setNewEmail] = useState('');
  const [emailType, setEmailType] = useState<'to' | 'cc' | 'bcc'>('to');
  const [previewMode, setPreviewMode] = useState(false);

  const addEmail = () => {
    if (!newEmail.trim()) return;
    
    if (!validateEmailAddresses([newEmail])) {
      toast.error("Please enter a valid email address");
      return;
    }

    const currentEmails = emailData[emailType];
    if (currentEmails.includes(newEmail)) {
      toast.error("Email address already added");
      return;
    }

    setEmailData(prev => ({
      ...prev,
      [emailType]: [...prev[emailType], newEmail]
    }));
    setNewEmail('');
  };

  const removeEmail = (email: string, type: 'to' | 'cc' | 'bcc') => {
    setEmailData(prev => ({
      ...prev,
      [type]: prev[type].filter(e => e !== email)
    }));
  };

  const handleSend = async () => {
    if (emailData.to.length === 0) {
      toast.error("Please add at least one recipient");
      return;
    }

    if (!validateEmailAddresses([...emailData.to, ...emailData.cc, ...emailData.bcc])) {
      toast.error("Please check all email addresses are valid");
      return;
    }

    try {
      const emailPayload = createEmailData(purchaseOrder, {
        to: emailData.to,
        cc: emailData.cc,
        bcc: emailData.bcc,
        template: emailData.template,
        customMessage: emailData.customMessage,
        attachPDF: emailData.attachPDF,
      });

      await onSend(emailPayload);
      onClose();
      toast.success("Purchase order sent successfully");
    } catch (error: any) {
      toast.error(error?.message || "Failed to send email");
    }
  };

  const getTemplateDescription = (template: string) => {
    const descriptions = {
      standard: "Standard purchase order notification with all details",
      urgent: "Urgent notification requiring immediate attention",
      revision: "Notification for revised purchase order",
      cancellation: "Cancellation notification"
    };
    return descriptions[template as keyof typeof descriptions] || descriptions.standard;
  };

  const getTemplateColor = (template: string) => {
    const colors = {
      standard: "bg-blue-100 text-blue-800",
      urgent: "bg-red-100 text-red-800",
      revision: "bg-yellow-100 text-yellow-800",
      cancellation: "bg-gray-100 text-gray-800"
    };
    return colors[template as keyof typeof colors] || colors.standard;
  };

  const previewTemplate = generateEmailTemplate(
    purchaseOrder,
    emailData.template,
    emailData.customMessage
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Send Purchase Order - {purchaseOrder.po_number}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {!previewMode ? (
            <>
              {/* Email Recipients */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recipients</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Add Email Input */}
                  <div className="flex gap-2">
                    <Select value={emailType} onValueChange={(value: 'to' | 'cc' | 'bcc') => setEmailType(value)}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="to">To</SelectItem>
                        <SelectItem value="cc">CC</SelectItem>
                        <SelectItem value="bcc">BCC</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Enter email address"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addEmail()}
                      className="flex-1"
                    />
                    <Button onClick={addEmail} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Email Lists */}
                  {(['to', 'cc', 'bcc'] as const).map(type => (
                    emailData[type].length > 0 && (
                      <div key={type}>
                        <Label className="text-sm font-medium capitalize">{type}:</Label>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {emailData[type].map(email => (
                            <Badge key={email} variant="secondary" className="flex items-center gap-1">
                              {email}
                              <X 
                                className="h-3 w-3 cursor-pointer" 
                                onClick={() => removeEmail(email, type)}
                              />
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )
                  ))}
                </CardContent>
              </Card>

              {/* Email Template */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Email Template</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Template Type</Label>
                    <Select 
                      value={emailData.template} 
                      onValueChange={(value: 'standard' | 'urgent' | 'revision' | 'cancellation') => 
                        setEmailData(prev => ({ ...prev, template: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">Standard</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                        <SelectItem value="revision">Revision</SelectItem>
                        <SelectItem value="cancellation">Cancellation</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-600 mt-1">
                      {getTemplateDescription(emailData.template)}
                    </p>
                  </div>

                  <div>
                    <Label>Custom Message (Optional)</Label>
                    <Textarea
                      placeholder="Add any additional message or instructions..."
                      value={emailData.customMessage}
                      onChange={(e) => setEmailData(prev => ({ ...prev, customMessage: e.target.value }))}
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="attachPDF"
                      checked={emailData.attachPDF}
                      onCheckedChange={(checked) => 
                        setEmailData(prev => ({ ...prev, attachPDF: checked as boolean }))
                      }
                    />
                    <Label htmlFor="attachPDF" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Attach PDF copy of purchase order
                    </Label>
                  </div>
                </CardContent>
              </Card>

              {/* Email Preview Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Email Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="font-medium">Subject:</span>
                      <span className="text-sm">{previewTemplate.subject}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Template:</span>
                      <Badge className={getTemplateColor(emailData.template)}>
                        {emailData.template.charAt(0).toUpperCase() + emailData.template.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Recipients:</span>
                      <span className="text-sm">{emailData.to.length} recipient(s)</span>
                    </div>
                    {emailData.attachPDF && (
                      <div className="flex justify-between">
                        <span className="font-medium">Attachment:</span>
                        <span className="text-sm flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          PDF Purchase Order
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            /* Email Preview */
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Email Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
                  <div className="mb-4 pb-2 border-b">
                    <p><strong>Subject:</strong> {previewTemplate.subject}</p>
                    <p><strong>To:</strong> {emailData.to.join(', ')}</p>
                    {emailData.cc.length > 0 && <p><strong>CC:</strong> {emailData.cc.join(', ')}</p>}
                  </div>
                  <div 
                    dangerouslySetInnerHTML={{ __html: previewTemplate.body }}
                    className="prose prose-sm max-w-none"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setPreviewMode(!previewMode)}
            >
              {previewMode ? 'Edit' : 'Preview'}
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSend} 
              disabled={isLoading || emailData.to.length === 0}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Email
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SendEmailModal;
