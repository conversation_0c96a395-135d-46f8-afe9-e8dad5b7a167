import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface GuestChecksSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

export const GuestChecksSearch: React.FC<GuestChecksSearchProps> = ({ searchTerm, setSearchTerm }) => (
  <div className="relative mb-8">
    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
    <Input
      placeholder="Search by table number, waiter name, or check ID..."
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      className="pl-10 h-12 border-0 bg-white/80 backdrop-blur-sm shadow-md focus:shadow-lg transition-all duration-300"
    />
  </div>
);