import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ArrowLeft,
  Search,
  Bed,
  Users,
  Calendar,
  Clock,
  Phone,
  Mail,
  MapPin,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

interface Room {
  id: string;
  number: string;
  type: string;
  status: "occupied" | "available" | "maintenance" | "cleaning";
  guest?: {
    name: string;
    phone: string;
    email: string;
    checkIn: string;
    checkOut: string;
    guests: number;
  };
  floor: number;
  capacity: number;
  amenities: string[];
}

const RoomEnquiry: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);

  const mockRooms: Room[] = [
    {
      id: "1",
      number: "101",
      type: "Standard Single",
      status: "occupied",
      floor: 1,
      capacity: 1,
      amenities: ["WiFi", "TV", "AC"],
      guest: {
        name: "<PERSON> Doe",
        phone: "+1234567890",
        email: "<EMAIL>",
        checkIn: "2024-01-15",
        checkOut: "2024-01-18",
        guests: 1,
      },
    },
    {
      id: "2",
      number: "102",
      type: "Standard Double",
      status: "available",
      floor: 1,
      capacity: 2,
      amenities: ["WiFi", "TV", "AC", "Mini Bar"],
    },
    {
      id: "3",
      number: "201",
      type: "Deluxe Suite",
      status: "maintenance",
      floor: 2,
      capacity: 4,
      amenities: ["WiFi", "TV", "AC", "Mini Bar", "Balcony", "Jacuzzi"],
    },
    {
      id: "4",
      number: "202",
      type: "Standard Double",
      status: "cleaning",
      floor: 2,
      capacity: 2,
      amenities: ["WiFi", "TV", "AC"],
    },
  ];

  const filteredRooms = mockRooms.filter(
    (room) =>
      room.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room.guest?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: Room["status"]) => {
    switch (status) {
      case "occupied":
        return "bg-red-500/10 text-red-600 border-red-200";
      case "available":
        return "bg-green-500/10 text-green-600 border-green-200";
      case "maintenance":
        return "bg-orange-500/10 text-orange-600 border-orange-200";
      case "cleaning":
        return "bg-blue-500/10 text-blue-600 border-blue-200";
      default:
        return "bg-gray-500/10 text-gray-600 border-gray-200";
    }
  };

  const getStatusIcon = (status: Room["status"]) => {
    switch (status) {
      case "occupied":
        return <XCircle className="h-4 w-4" />;
      case "available":
        return <CheckCircle className="h-4 w-4" />;
      case "maintenance":
        return <AlertCircle className="h-4 w-4" />;
      case "cleaning":
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Room Enquiry
            </h1>
            <p className="text-muted-foreground">
              Search and view room status and guest information
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <Card className="shadow-lg border-border/50">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-4">
                  <Search className="h-5 w-5 text-primary" />
                  <Input
                    placeholder="Search by room number, type, or guest name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1 border-border/50"
                  />
                </div>
              </CardHeader>
            </Card>

            <Card className="shadow-lg border-border/50">
              <CardHeader>
                <CardTitle className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Rooms ({filteredRooms.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="grid gap-4">
                    {filteredRooms.map((room) => (
                      <Card
                        key={room.id}
                        className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.02] border-border/50 ${
                          selectedRoom?.id === room.id
                            ? "ring-2 ring-primary/50 bg-primary/5"
                            : "hover:border-primary/50"
                        }`}
                        onClick={() => setSelectedRoom(room)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                <Bed className="h-5 w-5 text-primary" />
                                <span className="font-bold text-lg">
                                  Room {room.number}
                                </span>
                              </div>
                              <Badge className={getStatusColor(room.status)}>
                                {getStatusIcon(room.status)}
                                {room.status.charAt(0).toUpperCase() + room.status.slice(1)}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <MapPin className="h-4 w-4" />
                              <span>Floor {room.floor}</span>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Type:</span>
                              <span className="ml-2 font-medium">{room.type}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span className="text-muted-foreground">Capacity:</span>
                              <span className="ml-1 font-medium">{room.capacity}</span>
                            </div>
                          </div>

                          {room.guest && (
                            <div className="mt-3 pt-3 border-t border-border/50">
                              <div className="flex items-center gap-2 text-sm">
                                <span className="text-muted-foreground">Guest:</span>
                                <span className="font-medium">{room.guest.name}</span>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-4">
            {selectedRoom ? (
              <Card className="shadow-lg border-border/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bed className="h-5 w-5 text-primary" />
                    Room {selectedRoom.number} Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <Badge className={getStatusColor(selectedRoom.status)}>
                        {getStatusIcon(selectedRoom.status)}
                        {selectedRoom.status.charAt(0).toUpperCase() + selectedRoom.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Type:</span>
                      <span className="font-medium">{selectedRoom.type}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Floor:</span>
                      <span className="font-medium">{selectedRoom.floor}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Capacity:</span>
                      <span className="font-medium">{selectedRoom.capacity} guests</span>
                    </div>
                  </div>

                  <div>
                    <span className="text-muted-foreground text-sm">Amenities:</span>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {selectedRoom.amenities.map((amenity) => (
                        <Badge key={amenity} variant="secondary" className="text-xs">
                          {amenity}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {selectedRoom.guest && (
                    <div className="pt-4 border-t border-border/50 space-y-3">
                      <h4 className="font-semibold text-foreground">Guest Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{selectedRoom.guest.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{selectedRoom.guest.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{selectedRoom.guest.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Check-in: {selectedRoom.guest.checkIn}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span>Check-out: {selectedRoom.guest.checkOut}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>Guests: {selectedRoom.guest.guests}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card className="shadow-lg border-border/50">
                <CardContent className="py-12">
                  <div className="text-center">
                    <Bed className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">
                      Select a Room
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      Click on a room from the list to view detailed information
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomEnquiry;
