import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Crown, 
  DollarSign, 
  Truck, 
  Building,
  Package,
  Loader2,
  AlertTriangle,
  Split,
  Plus,
  Trash2
} from "lucide-react";
import {
  useSelectSupplierMutation,
  useCreateSplitAwardMutation,
} from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface SupplierSelectionModalProps {
  open: boolean;
  onClose: () => void;
  item: any;
  bidAnalysisId: number;
}

const SupplierSelectionModal = ({ 
  open, 
  onClose, 
  item, 
  bidAnalysisId 
}: SupplierSelectionModalProps) => {
  const [selectedSupplier, setSelectedSupplier] = useState<number | null>(null);
  const [selectionReason, setSelectionReason] = useState("");
  const [isSplitAward, setIsSplitAward] = useState(false);
  const [splitAwards, setSplitAwards] = useState<any[]>([]);

  const [selectSupplier, { isLoading: selecting }] = useSelectSupplierMutation();
  const [createSplitAward, { isLoading: creatingSplit }] = useCreateSplitAwardMutation();

  useEffect(() => {
    if (open && item) {
      setSelectedSupplier(item.selected_supplier || null);
      setSelectionReason(item.selection_reason || "");
      setIsSplitAward(item.is_split_award || false);
      setSplitAwards(item.split_awards || []);
    }
  }, [open, item]);

  const resetForm = () => {
    setSelectedSupplier(null);
    setSelectionReason("");
    setIsSplitAward(false);
    setSplitAwards([]);
  };

  const addSplitAward = () => {
    setSplitAwards(prev => [...prev, {
      supplier: null,
      quantity: 0,
      unit_price: 0,
      percentage: 0,
      reason: ""
    }]);
  };

  const removeSplitAward = (index: number) => {
    setSplitAwards(prev => prev.filter((_, i) => i !== index));
  };

  const updateSplitAward = (index: number, field: string, value: any) => {
    setSplitAwards(prev => prev.map((split, i) => 
      i === index ? { ...split, [field]: value } : split
    ));
  };

  const calculateSplitPercentages = () => {
    const totalQuantity = item?.quantity || 0;
    setSplitAwards(prev => prev.map(split => ({
      ...split,
      percentage: totalQuantity > 0 ? (split.quantity / totalQuantity) * 100 : 0
    })));
  };

  const handleSubmit = async () => {
    if (!item) return;

    if (isSplitAward) {
      // Handle split award
      if (splitAwards.length === 0) {
        toast.error("Please add at least one split award");
        return;
      }

      const totalQuantity = splitAwards.reduce((sum, split) => sum + (split.quantity || 0), 0);
      if (totalQuantity !== item.quantity) {
        toast.error(`Split quantities must total ${item.quantity}`);
        return;
      }

      try {
        await createSplitAward({
          bid_analysis_id: bidAnalysisId,
          item_id: item.id,
          split_data: {
            splits: splitAwards,
            reason: selectionReason
          }
        }).unwrap();
        toast.success("Split award created successfully");
        onClose();
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to create split award");
      }
    } else {
      // Handle single supplier selection
      if (!selectedSupplier) {
        toast.error("Please select a supplier");
        return;
      }

      const selectedQuote = item.supplier_quotes?.find((q: any) => q.supplier === selectedSupplier);
      if (!selectedQuote) {
        toast.error("Selected supplier quote not found");
        return;
      }

      try {
        await selectSupplier({
          bid_analysis_id: bidAnalysisId,
          item_id: item.id,
          supplier_data: {
            supplier_id: selectedSupplier,
            unit_price: selectedQuote.unit_price,
            reason: selectionReason
          }
        }).unwrap();
        toast.success("Supplier selected successfully");
        onClose();
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to select supplier");
      }
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  if (!item) return null;

  const availableQuotes = item.supplier_quotes?.filter((q: any) => q.is_available) || [];
  const bestPrice = Math.min(...availableQuotes.map((q: any) => q.unit_price).filter((p: any) => p != null));
  const bestDelivery = Math.min(...availableQuotes.map((q: any) => q.delivery_time_days).filter((d: any) => d != null));

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5" />
            Select Winner for {item.product_name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Item Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                Item Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Product:</span>
                  <div>{item.product_name}</div>
                </div>
                <div>
                  <span className="font-medium">Code:</span>
                  <div>{item.product_code}</div>
                </div>
                <div>
                  <span className="font-medium">Quantity:</span>
                  <div>{item.quantity} {item.unit_of_measure_name}</div>
                </div>
                <div>
                  <span className="font-medium">Est. Unit Cost:</span>
                  <div>${item.estimated_unit_cost?.toLocaleString() || "N/A"}</div>
                </div>
              </div>
              {item.specifications && (
                <div className="mt-3">
                  <span className="font-medium">Specifications:</span>
                  <div className="text-gray-600">{item.specifications}</div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selection Type */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selection Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="split-award"
                  checked={isSplitAward}
                  onCheckedChange={(checked) => setIsSplitAward(checked as boolean)}
                />
                <Label htmlFor="split-award" className="flex items-center gap-2">
                  <Split className="h-4 w-4" />
                  Split Award (Multiple Suppliers)
                </Label>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {isSplitAward 
                  ? "Award different quantities to multiple suppliers"
                  : "Award the entire quantity to a single supplier"
                }
              </p>
            </CardContent>
          </Card>

          {!isSplitAward ? (
            /* Single Supplier Selection */
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Available Suppliers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {availableQuotes.map((quote: any, index: number) => (
                    <div 
                      key={index}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedSupplier === quote.supplier 
                          ? "border-blue-500 bg-blue-50" 
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setSelectedSupplier(quote.supplier)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <input
                            type="radio"
                            checked={selectedSupplier === quote.supplier}
                            onChange={() => setSelectedSupplier(quote.supplier)}
                            className="text-blue-600"
                          />
                          <div>
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-500" />
                              <span className="font-medium">{quote.supplier_name}</span>
                              {quote.unit_price === bestPrice && (
                                <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                                  <Crown className="h-3 w-3" />
                                  Best Price
                                </Badge>
                              )}
                              {quote.delivery_time_days === bestDelivery && (
                                <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
                                  <Truck className="h-3 w-3" />
                                  Fastest
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-gray-600 mt-1">
                              {quote.payment_terms && `Payment: ${quote.payment_terms}`}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-1 text-lg font-bold">
                            <DollarSign className="h-4 w-4" />
                            {quote.unit_price?.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 flex items-center gap-1">
                            <Truck className="h-3 w-3" />
                            {quote.delivery_time_days} days
                          </div>
                          <div className="text-sm font-medium text-green-600">
                            Total: ${quote.total_price?.toLocaleString()}
                          </div>
                        </div>
                      </div>
                      {quote.notes && (
                        <div className="mt-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                          {quote.notes}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            /* Split Award Configuration */
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between text-lg">
                  <span className="flex items-center gap-2">
                    <Split className="h-4 w-4" />
                    Split Award Configuration
                  </span>
                  <Button onClick={addSplitAward} size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Split
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {splitAwards.map((split, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium">Split {index + 1}</h4>
                        {splitAwards.length > 1 && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSplitAward(index)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label>Supplier</Label>
                          <Select
                            value={split.supplier?.toString() || ""}
                            onValueChange={(value) => updateSplitAward(index, "supplier", parseInt(value))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableQuotes.map((quote: any) => (
                                <SelectItem key={quote.supplier} value={quote.supplier.toString()}>
                                  {quote.supplier_name} - ${quote.unit_price}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label>Quantity</Label>
                          <Input
                            type="number"
                            min="1"
                            max={item.quantity}
                            value={split.quantity || ""}
                            onChange={(e) => {
                              updateSplitAward(index, "quantity", parseInt(e.target.value) || 0);
                              calculateSplitPercentages();
                            }}
                          />
                        </div>
                      </div>
                      
                      <div className="mt-3">
                        <Label>Reason for this split</Label>
                        <Textarea
                          placeholder="Why is this supplier getting this portion?"
                          value={split.reason || ""}
                          onChange={(e) => updateSplitAward(index, "reason", e.target.value)}
                          rows={2}
                        />
                      </div>
                      
                      {split.percentage > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          Percentage: {split.percentage.toFixed(1)}%
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {splitAwards.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Split className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <p>No splits configured. Click "Add Split" to start.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Selection Reason */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Selection Reason</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="reason">Why was this selection made?</Label>
                <Textarea
                  id="reason"
                  placeholder="Provide reasoning for the supplier selection (e.g., best price, quality, delivery time, past performance)..."
                  value={selectionReason}
                  onChange={(e) => setSelectionReason(e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={selecting || creatingSplit || (!selectedSupplier && !isSplitAward)}
          >
            {(selecting || creatingSplit) ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isSplitAward ? "Creating Split..." : "Selecting..."}
              </>
            ) : (
              <>
                <Crown className="mr-2 h-4 w-4" />
                {isSplitAward ? "Create Split Award" : "Select Winner"}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SupplierSelectionModal;
