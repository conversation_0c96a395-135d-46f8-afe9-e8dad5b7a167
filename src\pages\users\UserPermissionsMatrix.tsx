import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Search,
  Filter,
  Save,
  RefreshCw,
  Info,
  Check,
  X,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Screen } from "@/app-components/layout/screen";

// Mock data for users
const mockUsers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "<PERSON><PERSON>",
    group: "Administrators",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    role: "Telemarketer",
    group: "Telemarketers",
    status: "active",
  },
  {
    id: 3,
    name: "Mike <PERSON>",
    email: "<EMAIL>",
    role: "Marketer",
    group: "Marketers",
    status: "inactive",
  },
  {
    id: 4,
    name: "Sarah Wilson",
    email: "<EMAIL>",
    role: "Digital Specialist",
    group: "Digital",
    status: "active",
  },
];

// Mock data for permissions
const mockPermissions = [
  {
    id: 1,
    name: "create_users",
    displayName: "Create Users",
    description: "Allows creating new user accounts in the system",
    category: "User Management",
  },
  {
    id: 2,
    name: "edit_users",
    displayName: "Edit Users",
    description: "Allows editing existing user accounts and their details",
    category: "User Management",
  },
  {
    id: 3,
    name: "delete_users",
    displayName: "Delete Users",
    description: "Allows permanently deleting user accounts from the system",
    category: "User Management",
  },
  {
    id: 4,
    name: "view_prospects",
    displayName: "View Prospects",
    description: "Allows viewing prospect information and contact details",
    category: "Prospect Management",
  },
  {
    id: 5,
    name: "create_prospects",
    displayName: "Create Prospects",
    description: "Allows adding new prospects to the system",
    category: "Prospect Management",
  },
  {
    id: 6,
    name: "edit_prospects",
    displayName: "Edit Prospects",
    description: "Allows modifying existing prospect information",
    category: "Prospect Management",
  },
  {
    id: 7,
    name: "view_campaigns",
    displayName: "View Campaigns",
    description: "Allows viewing marketing campaigns and their performance",
    category: "Marketing",
  },
  {
    id: 8,
    name: "create_campaigns",
    displayName: "Create Campaigns",
    description: "Allows creating new marketing campaigns",
    category: "Marketing",
  },
];

// Mock user permissions matrix
const mockUserPermissions = {
  1: [1, 2, 3, 4, 5, 6, 7, 8], // Admin has all permissions
  2: [4, 5, 6], // Telemarketer has prospect permissions
  3: [7, 8], // Marketer has campaign permissions
  4: [4, 7], // Digital specialist has view permissions
};

export default function UserPermissionsMatrix() {
  const [searchTerm, setSearchTerm] = useState("");
  const [groupFilter, setGroupFilter] = useState("all");
  const [userPermissions, setUserPermissions] = useState(mockUserPermissions);
  const [hasChanges, setHasChanges] = useState(false);

  const filteredUsers = mockUsers.filter((user) => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesGroup = groupFilter === "all" || user.group === groupFilter;
    return matchesSearch && matchesGroup;
  });

  const groups = [...new Set(mockUsers.map(user => user.group))];

  const handlePermissionChange = (userId: number, permissionId: number, checked: boolean) => {
    setUserPermissions(prev => {
      const userPerms = prev[userId] || [];
      const newPerms = checked
        ? [...userPerms, permissionId]
        : userPerms.filter(id => id !== permissionId);
      
      setHasChanges(true);
      return { ...prev, [userId]: newPerms };
    });
  };

  const handleSelectAllPermissions = (permissionId: number, checked: boolean) => {
    setUserPermissions(prev => {
      const newPerms = { ...prev };
      filteredUsers.forEach(user => {
        const userPerms = newPerms[user.id] || [];
        newPerms[user.id] = checked
          ? [...new Set([...userPerms, permissionId])]
          : userPerms.filter(id => id !== permissionId);
      });
      setHasChanges(true);
      return newPerms;
    });
  };

  const handleSaveChanges = () => {
    // Handle save logic here
    console.log("Saving user permissions:", userPermissions);
    setHasChanges(false);
  };

  const handleReset = () => {
    setUserPermissions(mockUserPermissions);
    setHasChanges(false);
  };

  const hasPermission = (userId: number, permissionId: number) => {
    return userPermissions[userId]?.includes(permissionId) || false;
  };

  const getPermissionCount = (permissionId: number) => {
    return filteredUsers.filter(user => hasPermission(user.id, permissionId)).length;
  };

  return (
    <Screen>
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/admin/users">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Users
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">User Permissions Matrix</h1>
              <p className="text-muted-foreground">
                Manage user permissions in a matrix format
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {hasChanges && (
              <Button variant="outline" onClick={handleReset}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset
              </Button>
            )}
            <Button onClick={handleSaveChanges} disabled={!hasChanges}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={groupFilter} onValueChange={setGroupFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by group" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Groups</SelectItem>
              {groups.map((group) => (
                <SelectItem key={group} value={group}>
                  {group}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Matrix Table */}
        <Card>
          <CardHeader>
            <CardTitle>User Permissions Matrix</CardTitle>
          </CardHeader>
          <CardContent className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px] sticky left-0 bg-background">
                    User
                  </TableHead>
                  {mockPermissions.map((permission) => (
                    <TableHead key={permission.id} className="text-center min-w-[120px]">
                      <div className="flex flex-col items-center space-y-1">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help">
                              <div className="text-xs font-medium">
                                {permission.displayName}
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {permission.category}
                              </Badge>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">{permission.description}</p>
                          </TooltipContent>
                        </Tooltip>
                        <div className="flex items-center space-x-1">
                          <Checkbox
                            checked={getPermissionCount(permission.id) === filteredUsers.length}
                            onCheckedChange={(checked) => 
                              handleSelectAllPermissions(permission.id, checked as boolean)
                            }
                          />
                          <span className="text-xs text-muted-foreground">
                            {getPermissionCount(permission.id)}/{filteredUsers.length}
                          </span>
                        </div>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell className="sticky left-0 bg-background">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`/api/placeholder/40/40`} />
                          <AvatarFallback>
                            {user.name.split(" ").map(n => n[0]).join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {user.group}
                          </div>
                        </div>
                        <Badge
                          variant={user.status === "active" ? "default" : "secondary"}
                        >
                          {user.status}
                        </Badge>
                      </div>
                    </TableCell>
                    {mockPermissions.map((permission) => (
                      <TableCell key={permission.id} className="text-center">
                        <div className="flex items-center justify-center">
                          <Checkbox
                            checked={hasPermission(user.id, permission.id)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange(user.id, permission.id, checked as boolean)
                            }
                            disabled={user.status === "inactive"}
                          />
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Legend */}
        <Card>
          <CardHeader>
            <CardTitle>Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm">Permission granted</span>
              </div>
              <div className="flex items-center space-x-2">
                <X className="h-4 w-4 text-red-600" />
                <span className="text-sm">Permission denied</span>
              </div>
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Hover for permission description</span>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox />
                <span className="text-sm">Select all users for this permission</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
    </Screen>
  );
}