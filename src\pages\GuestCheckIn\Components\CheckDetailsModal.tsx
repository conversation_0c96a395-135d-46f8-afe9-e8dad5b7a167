import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { GuestCheck } from "./types/types";

interface CheckDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedCheck: GuestCheck | null;
}

export const CheckDetailsDialog: React.FC<CheckDetailsDialogProps> = ({
  open,
  onOpenChange,
  selectedCheck,
}) => {
  const formatTime = (timeString: string) =>
    new Date(timeString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[95vh] bg-white/95 backdrop-blur-2xl border-0 shadow-2xl rounded-3xl overflow-hidden p-0">
        {/* Fixed Header */}
        <DialogHeader className="sticky top-0 z-10 bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 p-6 text-white">
          <DialogTitle className="text-3xl font-bold flex items-center gap-3">
            <div className="relative">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              <div className="absolute inset-0 w-3 h-3 bg-white rounded-full animate-ping opacity-75"></div>
            </div>
            Check #{selectedCheck?.tableNumber}
          </DialogTitle>
          <div className="text-yellow-100 text-sm font-medium mt-1">
            Order Details & Summary
          </div>
        </DialogHeader>
        
        {selectedCheck && (
          <div className="overflow-y-auto max-h-[calc(95vh-200px)] custom-scrollbar">
            <div className="space-y-8 p-6">
              {/* Stats Grid */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="group relative bg-gradient-to-br from-orange-100 to-yellow-50 p-5 rounded-2xl border border-orange-200 hover:border-orange-400 transition-all duration-500 hover:shadow-lg hover:shadow-orange-200/20">
                  <Label className="text-xs font-bold text-orange-600 uppercase tracking-wider mb-2 block">Table</Label>
                  <p className="text-3xl font-black text-orange-700 relative z-10">{selectedCheck.tableNumber}</p>
                  <div className="absolute top-3 right-3 w-2 h-2 bg-orange-400 rounded-full opacity-60"></div>
                </div>
                <div className="group relative bg-gradient-to-br from-orange-100 to-yellow-50 p-5 rounded-2xl border border-orange-200 hover:border-yellow-400 transition-all duration-500 hover:shadow-lg hover:shadow-yellow-200/20">
                  <Label className="text-xs font-bold text-yellow-600 uppercase tracking-wider mb-2 block">Guests</Label>
                  <p className="text-3xl font-black text-yellow-700 relative z-10">{selectedCheck.guestCount}</p>
                  <div className="absolute top-3 right-3 w-2 h-2 bg-yellow-400 rounded-full opacity-60"></div>
                </div>
                <div className="group relative bg-gradient-to-br from-orange-100 to-yellow-50 p-5 rounded-2xl border border-orange-200 hover:border-red-400 transition-all duration-500 hover:shadow-lg hover:shadow-red-200/20">
                  <Label className="text-xs font-bold text-red-600 uppercase tracking-wider mb-2 block">Waiter</Label>
                  <p className="text-xl font-black text-red-700 relative z-10 truncate">{selectedCheck.waiterName}</p>
                  <div className="absolute top-3 right-3 w-2 h-2 bg-red-400 rounded-full opacity-60"></div>
                </div>
                <div className="group relative bg-gradient-to-br from-orange-100 to-yellow-50 p-5 rounded-2xl border border-orange-200 hover:border-orange-400 transition-all duration-500 hover:shadow-lg hover:shadow-orange-200/20">
                  <Label className="text-xs font-bold text-orange-600 uppercase tracking-wider mb-2 block">Time</Label>
                  <p className="text-xl font-black text-orange-700 relative z-10">{formatTime(selectedCheck.orderTime)}</p>
                  <div className="absolute top-3 right-3 w-2 h-2 bg-orange-400 rounded-full opacity-60"></div>
                </div>
              </div>

              {/* Items Section */}
              <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-6 rounded-3xl border border-orange-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-sm"></div>
                  </div>
                  <Label className="text-lg font-bold text-orange-700">Items Ordered</Label>
                  <div className="flex-1 h-px bg-gradient-to-r from-orange-200 to-transparent"></div>
                  <span className="text-sm text-yellow-600 font-medium">{selectedCheck.items.length} items</span>
                </div>
                <div className="space-y-3 max-h-64 overflow-y-auto pr-2 custom-scrollbar">
                  {selectedCheck.items.map((item, index) => (
                    <div key={index} className="group relative bg-orange-100/60 backdrop-blur-sm rounded-2xl border border-orange-200 hover:border-yellow-400 transition-all duration-300 hover:shadow-lg hover:shadow-yellow-200/10">
                      <div className="flex justify-between items-center p-4">
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-xl flex items-center justify-center text-white text-lg font-black shadow-lg">
                              {item.qty}
                            </div>
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-xs font-bold text-white opacity-80">
                              ×
                            </div>
                          </div>
                          <div>
                            <span className="font-semibold text-orange-700 text-lg block">{item.name}</span>
                            <span className="text-yellow-600 text-sm">${item.price.toFixed(2)} each</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-black bg-gradient-to-r from-red-400 via-yellow-400 to-orange-400 bg-clip-text text-transparent">
                            ${(item.price * item.qty).toFixed(2)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Totals Section */}
              <div className="bg-gradient-to-br from-orange-100 to-yellow-50 p-6 rounded-3xl border border-orange-200 shadow-2xl">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                  <Label className="text-lg font-bold text-orange-700">Order Summary</Label>
                  <div className="flex-1 h-px bg-gradient-to-r from-orange-200 to-transparent"></div>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-3 border-b border-orange-200">
                    <span className="text-orange-600 font-medium">Subtotal</span>
                    <span className="font-bold text-orange-700 text-lg">${selectedCheck.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center py-3 border-b border-yellow-200">
                    <span className="text-yellow-600 font-medium">Tax</span>
                    <span className="font-bold text-yellow-700 text-lg">${selectedCheck.tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center py-3 border-b border-red-200">
                    <span className="text-red-600 font-medium">Service Charge</span>
                    <span className="font-bold text-red-700 text-lg">${selectedCheck.serviceCharge.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center pt-6">
                    <span className="text-2xl font-black text-orange-700">Total</span>
                    <div className="text-right">
                      <div className="text-4xl font-black bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 bg-clip-text text-transparent">
                        ${selectedCheck.total.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* Fixed Footer */}
        <DialogFooter className="sticky bottom-0 bg-gradient-to-r from-orange-100 to-yellow-50 p-6 border-t border-orange-200 backdrop-blur-sm">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-8 py-3 bg-gradient-to-r from-red-500 to-orange-500 hover:from-yellow-400 hover:to-orange-600 border-2 border-orange-300 hover:border-yellow-400 text-white font-bold rounded-2xl transition-all duration-300 shadow-lg hover:shadow-yellow-500/25 hover:shadow-2xl transform hover:scale-105"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
      
      <style>
        {`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(39, 39, 42, 0.3);
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, rgb(139, 92, 246), rgb(168, 85, 247));
            border-radius: 10px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, rgb(124, 58, 237), rgb(147, 51, 234));
          }
        `}
      </style>
    </Dialog>
  );
};