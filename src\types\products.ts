export interface ProductMainCategory {
  id?: string;
  name: string;
  code?: string;
  description: string;
  cost_center: string; // Foreign key reference to CostCenter
}

export interface ProductCategory {
  id?: string;
  name: string;
  main_category_id: string; // Foreign key reference to ProductMainCategory
  code?: string;
  description: string;
  cost_center: string; // Foreign key reference to CostCenter
}

export interface ProductMainCategoryApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ProductMainCategory[];
  };
}

export interface ProductCategoryApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ProductCategory[];
  };
}
