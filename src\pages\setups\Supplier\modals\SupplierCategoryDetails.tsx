import { Edit } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { supplierCategoryTypes } from "@/types/suppliers";
import { Button } from "@/components/ui/button";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  supplierCategory?: supplierCategoryTypes;
}

const SupplierCategoryDetails = ({
  isOpen,
  onClose,
  supplierCategory,
}: propTypes) => {
  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Supplier Category Details"
      description="View supplier category details"
    >
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm text-gray-500">Name</h3>
            <p className="text-lg font-medium">{supplierCategory?.name}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Description</h3>
            <p className="text-lg font-medium">
              {supplierCategory?.description}
            </p>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default SupplierCategoryDetails;
