import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetStoreRequisitionQuery,
  useSubmitStoreRequisitionMutation,
  useApproveStoreRequisitionMutation,
  useRejectStoreRequisitionMutation,
  useConvertToPurchaseRequisitionMutation,
} from "@/redux/slices/procurement";
import CreateFromStoreRequisition from "../PurchaseRequisitions/modals/CreateFromStoreRequisition";
import { 
  ArrowLeft, 
  Send, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const StoreRequisitionDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [showConvertModal, setShowConvertModal] = useState(false);
  const [rejectReason, setRejectReason] = useState("");

  const { data: requisition, isLoading, error } = useGetStoreRequisitionQuery(id!);
  const [submitRequisition, { isLoading: submitting }] = useSubmitStoreRequisitionMutation();
  const [approveRequisition, { isLoading: approving }] = useApproveStoreRequisitionMutation();
  const [rejectRequisition, { isLoading: rejecting }] = useRejectStoreRequisitionMutation();
  const [convertToPurchase, { isLoading: converting }] = useConvertToPurchaseRequisitionMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { variant: "secondary" as const, color: "bg-gray-100 text-gray-800" },
      Submitted: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Approved: { variant: "default" as const, color: "bg-green-100 text-green-800" },
      Rejected: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };

  const handleSubmit = async () => {
    try {
      await submitRequisition(id!).unwrap();
      toast.success("Store requisition submitted successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to submit requisition");
    }
  };

  const handleApprove = async () => {
    try {
      await approveRequisition(id!).unwrap();
      toast.success("Store requisition approved successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to approve requisition");
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    try {
      await rejectRequisition({ id: id!, reason: rejectReason }).unwrap();
      toast.success("Store requisition rejected");
      setShowRejectDialog(false);
      setRejectReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to reject requisition");
    }
  };

  const handleConvertToPurchase = async () => {
    try {
      await convertToPurchase(id!).unwrap();
      toast.success("Successfully converted to purchase requisition");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to convert to purchase requisition");
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !requisition?.data) {
    return (
      <Screen>
        <div className="text-center py-8">
          <p className="text-red-600">Failed to load store requisition details</p>
          <Button onClick={() => navigate(-1)} className="mt-4">
            Go Back
          </Button>
        </div>
      </Screen>
    );
  }

  const req = requisition.data;

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                Store Requisition SR-{String(req.id).padStart(4, '0')}
              </h1>
              <p className="text-gray-600 mt-1">{req.purpose}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {getStatusBadge(req.status!)}
            
            {/* Action Buttons */}
            {req.status === "Draft" && (
              <Button onClick={handleSubmit} disabled={submitting}>
                {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                <Send className="mr-2 h-4 w-4" />
                Submit
              </Button>
            )}
            
            {req.status === "Submitted" && (
              <>
                <Button onClick={handleApprove} disabled={approving}>
                  {approving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Approve
                </Button>
                <Button 
                  variant="destructive" 
                  onClick={() => setShowRejectDialog(true)}
                  disabled={rejecting}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Reject
                </Button>
              </>
            )}
            
            {req.status === "Approved" && (
              <Button onClick={() => setShowConvertModal(true)}>
                <FileText className="mr-2 h-4 w-4" />
                Convert to Purchase Req.
              </Button>
            )}
          </div>
        </div>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Requested By</p>
                  <p className="font-medium">{req.requested_by_name || "N/A"}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Cost Center</p>
                  <p className="font-medium">{req.cost_center_name || "N/A"}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Package className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Store</p>
                  <p className="font-medium">{req.store_name || "N/A"}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Required By</p>
                  <p className="font-medium">
                    {req.required_by ? new Date(req.required_by).toLocaleDateString() : "N/A"}
                  </p>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div>
              <p className="text-sm text-gray-500 mb-2">Purpose</p>
              <p className="text-gray-800">{req.purpose}</p>
            </div>
          </CardContent>
        </Card>

        {/* Items */}
        <Card>
          <CardHeader>
            <CardTitle>Requested Items</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit of Measure</TableHead>
                  <TableHead>Remarks</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {req.items?.map((item: any, index: number) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">
                      {item.product_name || `Product ID: ${item.product}`}
                    </TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{item.unit_of_measure_name || "N/A"}</TableCell>
                    <TableCell>{item.remarks || "-"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Timestamps */}
        <Card>
          <CardHeader>
            <CardTitle>Timeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Created At</p>
                <p className="font-medium">
                  {req.created_at ? new Date(req.created_at).toLocaleString() : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="font-medium">
                  {req.updated_at ? new Date(req.updated_at).toLocaleString() : "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Store Requisition</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reject-reason">Reason for Rejection *</Label>
              <Textarea
                id="reject-reason"
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Please provide a reason for rejecting this requisition..."
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleReject} disabled={rejecting}>
              {rejecting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Reject Requisition
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Convert to Purchase Requisition Modal */}
      {showConvertModal && (
        <CreateFromStoreRequisition
          isOpen={showConvertModal}
          onClose={() => setShowConvertModal(false)}
          storeRequisition={req}
        />
      )}
    </Screen>
  );
};

export default StoreRequisitionDetail;
