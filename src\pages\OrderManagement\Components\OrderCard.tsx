import React, { useState } from 'react';
import OrderTrackerPipeline from './OrderPipeline';

interface OrderCardProps {
  id: number;
  orderNumber: string;
  status: 'Open' | 'In Progress' | 'Completed' | 'Cancelled' | 'Refunded';
  date: string;
  total: string;
  table_number?: string;
  guest_count?: number;
  payment_status?: string;
  order_type?: string;
}

function OrderCard({
  id,
  orderNumber,
  status,
  date,
  total,
  table_number,
  guest_count,
  payment_status,
  order_type = 'Dine In'
}: OrderCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Status color mapping
  const statusColors = {
    'Open': 'bg-blue-100 text-blue-800',
    'In Progress': 'bg-yellow-100 text-yellow-800',
    'Completed': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-red-100 text-red-800',
    'Refunded': 'bg-purple-100 text-purple-800'
  };

  const handleClick = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <div 
        onClick={handleClick}
        className="bg-white rounded-lg shadow-md hover:shadow-lg border border-gray-200 hover:border-primary w-full h-full flex flex-col cursor-pointer transition-all duration-200 hover:scale-[1.02] active:scale-95 group"
      >
        <div className="p-4 flex-1 flex flex-col">
          <div className="flex justify-between items-start mb-3">
            <div>
              <h3 className="text-lg font-semibold text-gray-800">Order #{orderNumber}</h3>
              <span className={`text-xs px-2 py-1 rounded-full ${statusColors[status]}`}>
                {status}
              </span>
            </div>
            <span className="text-sm text-gray-500">{date}</span>
          </div>

          <div className="mt-2 space-y-2 text-sm">
            {table_number && (
              <p className="flex items-center text-gray-600">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
                Table: {table_number}
              </p>
            )}
            
            {guest_count && (
              <p className="flex items-center text-gray-600">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                Guests: {guest_count}
              </p>
            )}

            {order_type && (
              <p className="flex items-center text-gray-600">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Type: {order_type}
              </p>
            )}
          </div>

          <div className="mt-auto pt-4 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <span className="font-medium text-gray-900">{total}</span>
              {payment_status && (
                <span className={`text-xs px-2 py-1 rounded-full ${
                  payment_status === 'PAID' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {payment_status}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Order Tracker Modal */}
      <OrderTrackerPipeline
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
        order={{
          id, // Now properly passed
          orderNumber,
          status,
          date,
          total,
          tableNumber: table_number,
          guestCount: guest_count?.toString(),
          order_type,
          payment_status: payment_status === 'PAID',
          // Required fields with defaults
          tax_amount: "0.00",
          service_charge: "0.00",
          catering_levy: "0.00",
          revenue_center: 1,
          workstation: 1,
          created_by: "System"
        }}
      />
    </>
  );
}

export default OrderCard;