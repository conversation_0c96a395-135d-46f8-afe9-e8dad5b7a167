import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Building2,
  MapPin,
  Clock,
  DollarSign
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Branch } from '@/types/pos';
import { useGetBranchesQuery, useDeleteBranchMutation } from '@/redux/slices/branches';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { cascadeBranchActivation } from '@/services/cascadingActivation';

const BranchManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: branches = [], isLoading, error, refetch } = useGetBranchesQuery({});
  const [deleteBranch] = useDeleteBranchMutation();

  // Force refresh data
  const handleRefresh = () => {
    refetch();
  };

  const handleDeleteBranch = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this branch?')) {
      try {
        await deleteBranch(id).unwrap();
        handleApiSuccess('Branch deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete branch');
      }
    }
  };

  const handleBranchActivation = async (branch: Branch) => {
    if (!branch.id) return;

    const newActiveState = !branch.is_active;

    try {
      const result = await cascadeBranchActivation(branch.id.toString(), newActiveState);

      if (result.success) {
        handleApiSuccess(result.message);
        refetch(); // Refresh the branches list
      } else {
        handleApiError(result.errors?.join(', ') || result.message);
      }
    } catch (error) {
      handleApiError(`Failed to ${newActiveState ? 'activate' : 'deactivate'} branch: ${error}`);
    }
  };

  const columns: ColumnDef<Branch>[] = [
    {
      accessorKey: 'branch_code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('branch_code')}</div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Branch Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue('name')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <span>{row.getValue('location')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'timezone',
      header: 'Timezone',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.getValue('timezone')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'currency',
      header: 'Currency',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <DollarSign className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.getValue('currency')}</span>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const branch = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/branches/${branch.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/branches/${branch.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Branch
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleBranchActivation(branch)}>
                {branch.is_active ? 'Deactivate' : 'Activate'} Branch
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteBranch(branch.id?.toString() || '')}
              >
                Delete Branch
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Safety check: ensure branches is an array before filtering
  const filteredBranches = Array.isArray(branches) ? branches.filter(branch =>
    branch.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.branch_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.location?.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  // Handle loading state
  if (isLoading) {
    return (
      <Screen>
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading branches...</p>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  // Handle error state
  if (error) {
    return (
      <Screen>
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-red-500 text-xl mb-4">⚠️</div>
              <p className="text-muted-foreground mb-4">
                Error loading branches: {error ? JSON.stringify(error) : 'Unknown error'}
              </p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Branch Management</h1>
          <p className="text-muted-foreground">
            Manage hotel or restaurant chain branches with localization and tax settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            🔄 Refresh
          </Button>
          <Link to="/pos/branches/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New Branch
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find branches by name, code, or location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search branches..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Branches Table */}
      <Card>
        <CardHeader>
          <CardTitle>Branches ({filteredBranches.length})</CardTitle>
          <CardDescription>
            A list of all branches in your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-muted-foreground">Loading branches...</div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-red-600">Error loading branches. Please try again.</div>
            </div>
          ) : (
            <DataTable
              data={filteredBranches}
              columns={columns}
              enablePagination={true}
              enableSorting={true}
              enableColumnFilters={true}
              enableSelectColumn={false}
            />
          )}
        </CardContent>
      </Card>
      </div>
    </Screen>
  );
};

export default BranchManagement;
