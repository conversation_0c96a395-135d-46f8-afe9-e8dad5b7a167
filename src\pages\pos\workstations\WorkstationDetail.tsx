import React, { useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Screen } from '@/app-components/layout/screen';
import { 
  ArrowLeft, 
  Edit, 
  Monitor, 
  Building2, 
  Store,
  Printer,
  Wifi,
  WifiOff,
  MoreHorizontal,
  TestTube,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Workstation, WorkstationRole } from '@/types/pos';
import { getWorkstationRoleColor } from '@/utils/pos';
import { useRetrieveWorkstationQuery } from '@/redux/slices/workstations';
import { useRetrieveBranchQuery } from '@/redux/slices/branches';
import { useRetrieveRevenueCenterQuery } from '@/redux/slices/revenueCenters';

const WorkstationDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  // API hooks to fetch workstation and related data
  const { data: workstation, isLoading, error } = useRetrieveWorkstationQuery(id || '');

  // Fetch branch data - handle both branch_code and id relationships
  const { data: branchData, isLoading: loadingBranch } = useRetrieveBranchQuery(workstation?.branch || '', {
    skip: !workstation?.branch
  });

  // Fetch revenue center data - handle both revenue_center_code and id relationships
  const { data: revenueCenterData, isLoading: loadingRevenueCenter } = useRetrieveRevenueCenterQuery(workstation?.revenue_center || '', {
    skip: !workstation?.revenue_center
  });

  // Handle loading state
  if (isLoading || loadingBranch || loadingRevenueCenter) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-muted-foreground">Loading workstation details...</div>
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !workstation) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <p className="text-muted-foreground mb-4">
              {error ? 'Error loading workstation details' : 'Workstation not found'}
            </p>
            <Button onClick={() => navigate('/pos/workstations')}>
              Back to Workstations
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/pos/workstations')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <Monitor className="h-6 w-6" />
              <h1 className="text-3xl font-bold tracking-tight">{workstation.name}</h1>
              <Badge variant={workstation.is_active ? 'default' : 'secondary'}>
                {workstation.is_active ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {workstation.hostname} • Branch {branchData?.name || workstation.branch}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <TestTube className="h-4 w-4 mr-2" />
            Test Connection
          </Button>
          <Link to={`/pos/workstations/${workstation.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Workstation
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Printer className="h-4 w-4 mr-2" />
                Manage Printers
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="h-4 w-4 mr-2" />
                Advanced Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {workstation.is_active ? 'Take Offline' : 'Bring Online'}
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Delete Workstation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="printers">Printers</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Type</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge variant="outline">
                  {workstation.role}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Branch</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">Branch {workstation.branch}</div>
                    <div className="text-sm text-muted-foreground">Branch ID</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Revenue Center</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Store className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{revenueCenterData?.name || `Revenue Center ${workstation.revenue_center}`}</div>
                    <div className="text-sm text-muted-foreground">{revenueCenterData?.revenue_center_code || workstation.revenue_center}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Connection</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  {workstation.is_active ? (
                    <Wifi className="h-4 w-4 text-green-500" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-red-500" />
                  )}
                  <span className="font-medium">
                    {workstation.is_active ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Network Information */}
          <Card>
            <CardHeader>
              <CardTitle>Network Configuration</CardTitle>
              <CardDescription>
                Network settings and connectivity information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">IP Address</h4>
                  <div className="font-mono text-sm bg-muted p-2 rounded">
                    {workstation.ip_address || 'Not configured'}
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Hostname</h4>
                  <div className="font-mono text-sm bg-muted p-2 rounded">
                    {workstation.hostname || 'Not configured'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Hardware Features */}
          <Card>
            <CardHeader>
              <CardTitle>Hardware Features</CardTitle>
              <CardDescription>
                Device capabilities and hardware features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">Touchscreen Compatible:</span>
                  <Badge variant="secondary">
                    No
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="printers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assigned Printers</CardTitle>
              <CardDescription>
                Printers connected to this workstation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {workstation.linked_printer ? '1' : '0'} printer(s) assigned
                  </span>
                  <Link to="/pos/printers/new">
                    <Button variant="outline" size="sm">
                      <Printer className="h-4 w-4 mr-2" />
                      Add Printer
                    </Button>
                  </Link>
                </div>
                
                {workstation.linked_printer ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Printer className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Linked Printer</span>
                        <Badge variant="outline">ID: {workstation.linked_printer}</Badge>
                      </div>
                      <Button variant="ghost" size="sm">
                        Configure
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Printer className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Printers Assigned</h3>
                    <p className="text-muted-foreground">
                      This workstation doesn't have any printers assigned yet.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="network" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Network Details</CardTitle>
              <CardDescription>
                Detailed network configuration and status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Connection Status</h4>
                  <div className="flex items-center space-x-2">
                    {workstation.is_active ? (
                      <Wifi className="h-4 w-4 text-green-500" />
                    ) : (
                      <WifiOff className="h-4 w-4 text-red-500" />
                    )}
                    <span className={workstation.is_active ? 'text-green-600' : 'text-red-600'}>
                      {workstation.is_active ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Last Seen</h4>
                  <span className="text-sm text-muted-foreground">
                    {workstation.is_active ? 'Currently online' : 'Unknown'}
                  </span>
                </div>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">IP Configuration</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>IP Address:</span>
                      <span className="font-mono">{workstation.ip_address || 'Not set'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Hostname:</span>
                      <span className="font-mono">{workstation.hostname || 'Not set'}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Network Tests</h4>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full">
                      <TestTube className="h-4 w-4 mr-2" />
                      Ping Test
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <Settings className="h-4 w-4 mr-2" />
                      Connection Test
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Workstation Settings</CardTitle>
              <CardDescription>
                Configuration and operational settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">General Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Name:</span>
                      <span className="font-medium">{workstation.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Type:</span>
                      <Badge variant="outline" className={workstation.role ? getWorkstationRoleColor(workstation.role as WorkstationRole) : ''}>
                        {workstation.role || 'Not set'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge variant={workstation.is_active ? 'default' : 'secondary'}>
                        {workstation.is_active ? 'Online' : 'Offline'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Code:</span>
                      <span className="font-medium">
                        {workstation.workstation_code}
                      </span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Configuration</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Touchscreen:</span>
                      <Badge variant="secondary">
                        Not supported
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Printers:</span>
                      <span className="font-medium">{workstation.linked_printer ? '1' : '0'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Language:</span>
                      <span className="font-medium">
                        {workstation.language || 'en'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </Screen>
  );
};

export default WorkstationDetail;
