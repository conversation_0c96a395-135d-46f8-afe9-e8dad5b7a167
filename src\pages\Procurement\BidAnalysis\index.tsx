import { Screen } from "@/app-components/layout/screen";
import AddBidAnalysis from "./modals/AddBidAnalysis";
import { useState } from "react";
import {
  useGetBidAnalysesQuery,
  useGetRFQsQuery,
  useDeleteBidAnalysisMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { BidAnalysis } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Trash2,
  Search,
  FileText,
  Clock,
  Package
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const BidAnalysisIndex = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [filters, setFilters] = useState({
    status: "all",
    rfq: "all",
    created_by: "all",
    date_from: "",
    date_to: "",
    search: "",
  });

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: bidAnalysesData, isLoading } = useGetBidAnalysesQuery(apiFilters);
  const { data: rfqs } = useGetRFQsQuery({});

  // Mutation hooks
  const [deleteBidAnalysis] = useDeleteBidAnalysisMutation();

  // Handler functions

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this bid analysis?")) {
      try {
        await deleteBidAnalysis(id).unwrap();
        toast.success("Bid analysis deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete bid analysis");
      }
    }
  };





  // Search handler
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<BidAnalysis>[] = [
    {
      accessorKey: "id",
      header: "Analysis ID",
      cell: ({ row }) => (
        <Link
          to={`/procurement/bid-analysis/${row.original.id}`}
          className="font-medium text-blue-600 hover:text-blue-800"
        >
          BA-{String(row.original.id).padStart(4, '0')}
        </Link>
      ),
    },
    {
      accessorKey: "rfq",
      header: "RFQ",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="font-medium">
            {row.original.rfq_number || `RFQ-${String(row.original.rfq).padStart(4, '0')}`}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "split_award",
      header: "Split Award",
      cell: ({ row }) => (
        <Badge variant={row.original.split_award ? "default" : "secondary"}>
          {row.original.split_award ? "Yes" : "No"}
        </Badge>
      ),
    },
    {
      accessorKey: "selected_responses",
      header: "Selected Responses",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Package className="h-4 w-4 text-gray-500" />
          <span className="font-medium">
            {row.original.selected_responses?.length || 0} responses
          </span>
        </div>
      ),
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => (
        <div className="text-sm">
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-gray-500" />
            {new Date(row.original.created_at || "").toLocaleDateString()}
          </div>
          <div className="text-gray-500">{row.original.created_by_name}</div>
        </div>
      ),
    },
    {
      accessorKey: "finalized_at",
      header: "Finalized",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.finalized_at ? (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              {new Date(row.original.finalized_at).toLocaleDateString()}
            </div>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const analysis = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link to={`/procurement/bid-analysis/${analysis.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Analysis
                </Link>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() => handleDelete(analysis.id!)}
                className="text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Bid Analysis</h1>
            <p className="text-gray-600 mt-1">Compare supplier responses and select winners</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Analysis
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search analyses..."
              className="w-64"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          
          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Draft">Draft</SelectItem>
              <SelectItem value="Under Review">Under Review</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
              <SelectItem value="Rejected">Rejected</SelectItem>
              <SelectItem value="Converted">Converted</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.rfq} onValueChange={(value) => setFilters(prev => ({ ...prev, rfq: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by RFQ" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All RFQs</SelectItem>
              {rfqs?.data?.results?.map((rfq: any) => (
                <SelectItem key={rfq.id} value={rfq.id.toString()}>
                  {rfq.rfq_number}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Input
            type="date"
            placeholder="From date"
            className="w-40"
            value={filters.date_from}
            onChange={(e) => setFilters(prev => ({ ...prev, date_from: e.target.value }))}
          />

          <Input
            type="date"
            placeholder="To date"
            className="w-40"
            value={filters.date_to}
            onChange={(e) => setFilters(prev => ({ ...prev, date_to: e.target.value }))}
          />
        </div>

        {/* Data Table */}
        <DataTable
          columns={columns}
          data={bidAnalysesData?.data?.results || []}
        />

        {/* Add Bid Analysis Modal */}
        <AddBidAnalysis
          open={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
      </div>
    </Screen>
  );
};

export default BidAnalysisIndex;
