import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ArrowLeft,
  CookingPot,
  Clock,
  Bell,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface OrderItem {
  id: string;
  tableNumber: string;
  orderNumber: string;
  items: {
    name: string;
    quantity: number;
    notes?: string;
    extras?: string;
    status: "pending" | "cooking" | "ready" | "cancelled";
  }[];
  timeReceived: string;
  priority: "normal" | "rush" | "vip";
}

const StationKDS: React.FC = () => {
  // Mock data - replace with actual API calls
  const [orders, setOrders] = useState<OrderItem[]>([
    {
      id: "1",
      tableNumber: "T12",
      orderNumber: "ORD-001",
      items: [
        { name: "Grilled Salmon", quantity: 2, status: "pending" },
        {
          name: "Caesar Salad",
          quantity: 1,
          notes: "No croutons",
          status: "cooking",
        },
      ],
      timeReceived: "10:30 AM",
      priority: "normal",
    },
    {
      id: "2",
      tableNumber: "T15",
      orderNumber: "ORD-002",
      items: [
        {
          name: "Ribeye Steak",
          quantity: 1,
          notes: "Medium rare",
          status: "cooking",
        },
        { name: "Mashed Potatoes", quantity: 2, status: "ready" },
        {
          name: "Fries",
          quantity: 1,
          status: "ready",
          notes: "Extra salt",
          extras: "Vinegar, Ketchup",
        },
      ],
      timeReceived: "10:35 AM",
      priority: "rush",
    },
  ]);

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Link to="/stations/home">
            <Button className="flex items-center gap-2 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg hover:scale-105 transition-all duration-200">
              <ArrowLeft className="h-4 w-4" />
              Back to Menu
            </Button>
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Kitchen Display System
            </h1>
            {/* <p className="text-muted-foreground">
              View and manage kitchen orders
            </p> */}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 text-xs">
          {orders.map((order) => (
            <Card key={order.id} className="shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-bold">
                  Table {order.tableNumber}
                </CardTitle>
                <div className="flex items-center gap-2 tex-xs">
                  <Badge
                    className="text-xs"
                    variant={
                      order.priority === "rush" ? "destructive" : "default"
                    }
                  >
                    {order.priority.toUpperCase()}
                  </Badge>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-xs text-muted-foreground">
                    {order.timeReceived}
                  </span>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="min-h-[200px] ">
                  <div className="space-y-4">
                    {order.items.map((item, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-3 space-y-2"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {item.status === "pending" && (
                              <AlertCircle className="h-5 w-5 text-yellow-500" />
                            )}
                            {item.status === "cooking" && (
                              <CookingPot className="h-5 w-5 text-blue-500" />
                            )}
                            {item.status === "ready" && (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                            {item.status === "cancelled" && (
                              <XCircle className="h-5 w-5 text-red-500" />
                            )}
                            <span className="font-medium">{item.name}</span>
                          </div>
                          <span className="text-xs font-semibold">
                            x{item.quantity}
                          </span>
                        </div>
                        {item.notes && (
                          <p className="text-xs text-muted-foreground">
                            Note: {item.notes}
                          </p>
                        )}
                        {item.extras && (
                          <p className="text-xs text-muted-foreground">
                            Extras: {item.extras}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                <div className="flex justify-between mt-4">
                  <Button variant="outline" className="w-full">
                    Mark as Ready
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StationKDS;
