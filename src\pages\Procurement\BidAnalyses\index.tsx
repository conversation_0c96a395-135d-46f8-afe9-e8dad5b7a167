import React, { useState } from "react";
import { useGetBidAnalysesQuery } from "@/redux/slices/procurement";
import EditBidAnalysisSingle from "./modals/EditBidAnalysisSingle";
import EditBidAnalysisMultiple from "./modals/EditBidAnalysisMultiple";
import ViewBidAnalysis from "./modals/ViewBidAnalysis";
import { Screen } from "@/app-components/layout/screen";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { 
  MoreH<PERSON>zontal,
  Eye,
  Edit,
  BarChart,
  Search,
  Filter,
  CheckCircle,
  Clock,
  XCircle
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

interface BidAnalysis {
  id: number;
  code: string;
  rfq: string;
  created_by: string;
  split_award: boolean;
  recommendation_notes: string;
  selected_responses: string | null;
  finalized_at: string | null;
  bid_lines?: any[];
}

const BidAnalyses: React.FC = () => {
  const [filters, setFilters] = useState({
    search: "",
    split_award: "all",
    finalized: "all",
  });

  // Modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditSingleModal, setShowEditSingleModal] = useState(false);
  const [showEditMultipleModal, setShowEditMultipleModal] = useState(false);
  const [selectedBidAnalysis, setSelectedBidAnalysis] = useState<BidAnalysis | null>(null);

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: bidAnalysesData, isLoading } = useGetBidAnalysesQuery(apiFilters);

  // Handler functions
  const handleView = (bidAnalysis: BidAnalysis) => {
    setSelectedBidAnalysis(bidAnalysis);
    setShowViewModal(true);
  };

  const handleEdit = (bidAnalysis: BidAnalysis) => {
    setSelectedBidAnalysis(bidAnalysis);
    if (!bidAnalysis.split_award) {
      // Single award - use single supplier modal
      setShowEditSingleModal(true);
    } else {
      // Split award - use multiple supplier modal
      setShowEditMultipleModal(true);
    }
  };

  // Table columns
  const columns: ColumnDef<BidAnalysis>[] = [
    {
      accessorKey: "code",
      header: "Code",
      cell: ({ row }) => {
        const bidAnalysis = row.original;
        return (
          <div>
            <div className="font-medium">{bidAnalysis.code}</div>
            <div className="text-sm text-gray-500">ID: {bidAnalysis.id}</div>
          </div>
        );
      },
    },
    {
      accessorKey: "rfq",
      header: "RFQ",
      cell: ({ row }) => (
        <div className="font-medium text-blue-600">{row.getValue("rfq")}</div>
      ),
    },
    {
      accessorKey: "created_by",
      header: "Created By",
    },
    {
      accessorKey: "split_award",
      header: "Award Type",
      cell: ({ row }) => {
        const splitAward = row.getValue("split_award") as boolean;
        return (
          <Badge variant={splitAward ? "default" : "secondary"}>
            {splitAward ? "Split Award" : "Single Award"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "selected_responses",
      header: "Selected Response",
      cell: ({ row }) => {
        const response = row.getValue("selected_responses") as string | null;
        return response ? (
          <Badge variant="outline">{response}</Badge>
        ) : (
          <span className="text-gray-400">Not selected</span>
        );
      },
    },
    {
      accessorKey: "finalized_at",
      header: "Status",
      cell: ({ row }) => {
        const finalizedAt = row.getValue("finalized_at") as string | null;
        return finalizedAt ? (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Finalized
          </Badge>
        ) : (
          <Badge variant="outline" className="text-orange-600">
            <Clock className="mr-1 h-3 w-3" />
            Draft
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const bidAnalysis = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(bidAnalysis)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              
              {!bidAnalysis.finalized_at && (
                <DropdownMenuItem onClick={() => handleEdit(bidAnalysis)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Analysis
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Bid Analyses</h1>
          <p className="text-gray-600 mt-1">Evaluate and analyze RFQ responses</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Analyses</p>
              <p className="text-2xl font-bold text-blue-600">
                {bidAnalysesData?.total_data || 0}
              </p>
            </div>
            <BarChart className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Finalized</p>
              <p className="text-2xl font-bold text-green-600">
                {bidAnalysesData?.results?.filter((ba: any) => ba.finalized_at).length || 0}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Draft</p>
              <p className="text-2xl font-bold text-orange-600">
                {bidAnalysesData?.results?.filter((ba: any) => !ba.finalized_at).length || 0}
              </p>
            </div>
            <Clock className="h-8 w-8 text-orange-600" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Split Awards</p>
              <p className="text-2xl font-bold text-purple-600">
                {bidAnalysesData?.results?.filter((ba: any) => ba.split_award).length || 0}
              </p>
            </div>
            <Filter className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search bid analyses..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10"
            />
          </div>

          <Select
            value={filters.split_award}
            onValueChange={(value) => setFilters(prev => ({ ...prev, split_award: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Award Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Award Types</SelectItem>
              <SelectItem value="true">Split Award</SelectItem>
              <SelectItem value="false">Single Award</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.finalized}
            onValueChange={(value) => setFilters(prev => ({ ...prev, finalized: value }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="true">Finalized</SelectItem>
              <SelectItem value="false">Draft</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={() => setFilters({ search: "", split_award: "all", finalized: "all" })}
          >
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white rounded-lg shadow border">
        <DataTable
          columns={columns}
          data={bidAnalysesData?.results || []}
        />
      </div>

      {/* Modals */}
      {selectedBidAnalysis && (
        <>
          <ViewBidAnalysis
            open={showViewModal}
            onClose={() => {
              setShowViewModal(false);
              setSelectedBidAnalysis(null);
            }}
            bidAnalysis={selectedBidAnalysis}
          />

          <EditBidAnalysisSingle
            open={showEditSingleModal}
            onClose={() => {
              setShowEditSingleModal(false);
              setSelectedBidAnalysis(null);
            }}
            bidAnalysis={selectedBidAnalysis}
            onSuccess={() => {
              // Refresh data or handle success
            }}
          />

          <EditBidAnalysisMultiple
            open={showEditMultipleModal}
            onClose={() => {
              setShowEditMultipleModal(false);
              setSelectedBidAnalysis(null);
            }}
            bidAnalysis={selectedBidAnalysis}
            onSuccess={() => {
              // Refresh data or handle success
            }}
          />
        </>
      )}
    </div>
    </Screen>
  );
};

export default BidAnalyses;
