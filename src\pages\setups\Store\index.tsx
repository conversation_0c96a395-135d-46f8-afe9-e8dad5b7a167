import { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { storeTypes } from "@/types/store";
import storeTestData from "./storeTestData";
import { searchDebouncer } from "@/utils/debouncers";
import AddStore from "./modals/AddStore";
import StoreDetails from "./modals/StoreDetails";
import { useGetStoresQuery } from "@/redux/slices/store";
import { Link } from "react-router-dom";

const index = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<storeTypes | null>(null);

  const {
    data: stores,
    isLoading,
    isFetching,
    isError,
    refetch,
  } = useGetStoresQuery({
    page: currentPage,
    page_size: itemsPerPage,
    search: searchValue,
  });

  const columns: ColumnDef<storeTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      enableColumnFilter: false,
    },
    {
      accessorKey: "code",
      header: "Code",
      enableColumnFilter: false,
    },
    {
      accessorKey: "branch",
      header: "Branch",
      cell: ({ row }) => (
        <Link
          to={`/pos/branches/${row.original.id}`}
          className="text-blue-500 hover:underline"
        >
          {row.original.branch}
        </Link>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "location",
      header: "Location",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(row.original)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
          {/* <Button
            variant="ghost"
            size="icon"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button> */}
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  const handleViewDetails = (store: storeTypes) => {
    setSelectedStore(store);
    setIsDetailModalOpen(true);
  };

  const handleEdit = (store: storeTypes) => {
    setSelectedStore(store);
    setIsEditModalOpen(true);
  };

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">Stores</h1>
        <div className="flex items-center gap-2">
          <Button variant="default" onClick={() => setIsAddModalOpen(true)}>
            Add Store
          </Button>
        </div>
      </div>

      <DataTable<storeTypes>
        data={stores?.data?.results || []}
        columns={columns}
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enableColumnFilters={true}
        enableSorting={true}
        enablePrintPdf={true}
        tableClassName="border-collapse"
        tHeadClassName="bg-gray-50"
        tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
        tBodyTrClassName="hover:bg-gray-50"
        tBodyCellsClassName="border-t"
        searchInput={
          <input
            value={searchInput}
            name="searchInput"
            type="search"
            onChange={(e) =>
              searchDebouncer(e.target.value, setSearchInput, setSearchValue)
            }
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search stores..."
          />
        }
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={stores?.data?.total_data || 0}
      />

      {/* Modal Components */}
      {isAddModalOpen && (
        <AddStore
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          refetch={refetch}
        />
      )}

      {isEditModalOpen && (
        <AddStore
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedStore!}
          refetch={refetch}
        />
      )}

      {isDetailModalOpen && (
        <StoreDetails
          isOpen={isDetailModalOpen}
          onClose={() => setIsDetailModalOpen(false)}
          store={selectedStore!}
        />
      )}
    </Screen>
  );
};

export default index;
