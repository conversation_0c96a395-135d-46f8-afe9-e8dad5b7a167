// src/components/guest-checks/ActionButtons.tsx
import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Printer, QrCode, Percent, CreditCard } from 'lucide-react';

interface ActionButtonsProps {
  checkId: string;
  status: string;
  onCloseCheck?: (checkId: string) => void;
  onSplitCheck?: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ checkId, status, onCloseCheck, onSplitCheck }) => (
  <div className="flex flex-wrap gap-2 pt-3 border-t">
    <Button size="sm" variant="outline" className="flex-1">
      <Plus className="h-4 w-4 mr-1" />
      Add Item
    </Button>
    <Button size="sm" variant="outline" className="flex-1">
      <Printer className="h-4 w-4 mr-1" />
      Print
    </Button>
    <Button size="sm" variant="outline" onClick={onSplitCheck}>
      <QrCode className="h-4 w-4 mr-1" />
      QR
    </Button>
    <Button size="sm" variant="outline">
      <Percent className="h-4 w-4 mr-1" />
      Discount
    </Button>
    {status === 'active' && (
      <Button
        size="sm"
        className="flex-1"
        onClick={() => onCloseCheck?.(checkId)}
      >
        <CreditCard className="h-4 w-4 mr-1" />
        Close Check
      </Button>
    )}
  </div>
);

export default ActionButtons;