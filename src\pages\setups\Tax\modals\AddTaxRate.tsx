import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import CustomSelectField from "@/components/CustomSelectField";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  useAddTaxRateMutation,
  usePatchTaxRateMutation,
} from "@/redux/slices/taxRates";
import { taxRateTypes } from "@/types/tax";
import { Loader2, Percent, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: taxRateTypes;
}

const AddTaxRate = ({ isOpen, onClose, updateData }: propTypes) => {
  const [createTaxRate, { isLoading: loading }] = useAddTaxRateMutation();
  const [updateTaxRate, { isLoading: loadingUpdate }] =
    usePatchTaxRateMutation();

  const [formData, setFormData] = useState({
    name: updateData ? updateData.name : "",
    percentage: updateData ? updateData.percentage : "",
    branch: updateData ? updateData.branch : "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddTaxRate = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (updateData) {
        await updateTaxRate({ id: updateData.id, data: formData }).unwrap();
        toast.success("Tax rate updated successfully");
      } else {
        await createTaxRate(formData).unwrap();
        toast.success("Tax rate added successfully");
      }
      onClose();
    } catch (error) {
      toast.error("Error occurred while saving tax rate");
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Tax Rate" : "Add Tax Rate"}
      description="Enter tax rate details"
    >
      <form onSubmit={handleAddTaxRate}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter tax rate name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="percentage">Percentage*</Label>
            <div className="relative">
              <Input
                id="percentage"
                name="percentage"
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={formData.percentage}
                onChange={handleInputChange}
                placeholder="Enter tax percentage"
                required
              />
              <Percent className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Branch</Label>
            <Input
              id="branch"
              name="branch"
              value={formData.branch}
              onChange={handleInputChange}
              placeholder="Enter branch"
            />
            {/* <CustomSelectField
              setValue={(e: any) =>
                handleInputChange({
                  target: { name: "branch", value: e },
                } as any)
              }
              useSearchField
              valueField="id"
              labelField="name"
              data={[]}
              queryFunc={() => {}}
              loader={false}
              isMultiple={false}
            /> */}
          </div>
        </div>

        <div className="flex justify-end gap-4 py-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {loading || loadingUpdate ? (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding...
            </Button>
          ) : (
            <Button type="submit">
              {updateData ? "Update Store" : "Add Store"}
              <Send className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddTaxRate;
