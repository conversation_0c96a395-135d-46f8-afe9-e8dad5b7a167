import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  useAddProductCategoryMutation,
  usePatchProductCategoryMutation,
} from "@/redux/slices/productCategories";
import { ProductCategory } from "@/types/products";
import { Loader2, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  main_category_code: string;
  isOpen: boolean;
  onClose: () => void;
  updateData?: ProductCategory;
}

const AddProductCategory = ({
  isOpen,
  onClose,
  updateData,
  main_category_code,
}: propTypes) => {
  const [createCategory, { isLoading: loading }] =
    useAddProductCategoryMutation();
  const [updateCategory, { isLoading: loadingUpdate }] =
    usePatchProductCategoryMutation();

  console.log("main_category_code", main_category_code);

  const [formData, setFormData] = useState({
    main_category: main_category_code,
    name: updateData ? updateData.name : "",
    description: updateData ? updateData.description : "",
    cost_center: updateData ? updateData.cost_center : "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddCategory = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      if (updateData) {
        await updateCategory({
          id: updateData.id,
          ...formData,
        }).unwrap();
        toast.success("Category updated successfully");
      } else {
        await createCategory(formData).unwrap();
        toast.success("Category added successfully");
      }
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Something went wrong");
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Product Category" : "Add Product Category"}
      description="Enter product category details"
    >
      <form onSubmit={handleAddCategory}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter category name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter category description"
            />
          </div>
        </div>

        <div className="flex justify-end gap-4 py-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading || loadingUpdate}
          >
            Cancel
          </Button>
          <ActionButton type="submit" disabled={loading || loadingUpdate}>
            {loading || loadingUpdate ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Please wait
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                {updateData ? "Update Category" : "Add Category"}
              </>
            )}
          </ActionButton>
        </div>
      </form>
    </BaseModal>
  );
};

export default AddProductCategory;
