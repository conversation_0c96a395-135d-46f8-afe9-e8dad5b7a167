
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { CheckCard } from './CheckCard';


interface Check {
  id: string;
  tableNumber: string;
  guestCount: number;
  waiterName: string;
  orderTime: string;
  status: string;
  subtotal: number;
  tax: number;
  serviceCharge: number;
  total: number;
  items: { name: string; price: number; qty: number }[];
  discounts: { type: string; amount: number }[];
  voids: any[];
}

interface SplitCheckDialogProps {
  check: Check | null;
  onClose: () => void;
}

const SplitCheckDialog: React.FC<SplitCheckDialogProps> = ({ check, onClose }) => (
  <Card className="fixed inset-4 z-50 bg-white shadow-xl">
    <CardHeader>
      <div className="flex justify-between items-center">
        <CardTitle>Split Check - {check?.id}</CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h3 className="font-semibold mb-3">Original Check</h3>
          {check && <CheckCard check={check} showActions={false} />}
        </div>
        <div>
          <h3 className="font-semibold mb-3">New Check</h3>
          <Card>
            <CardContent className="p-4">
              <div className="text-center text-gray-500 py-8">
                Drag items here to split
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button>Create Split Check</Button>
      </div>
    </CardContent>
  </Card>
);

export default SplitCheckDialog;