import React from 'react';
import { useForm } from 'react-hook-form';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2, Monitor, X } from 'lucide-react';
import { WorkstationFormData, WorkstationRole } from '@/types/pos';
import { getWorkstationRoleLabel } from '@/utils/pos';
import {useAddWorkstationMutation } from '@/redux/slices/workstations';
import { useGetRevenueCentersQuery } from '@/redux/slices/revenueCenters';

import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';

interface AddWorkstationModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  branchId: string;
  revenueCenterId?: string;
  onSuccess?: () => void;
}

const AddWorkstationModal: React.FC<AddWorkstationModalProps> = ({
  isOpen,
  onOpenChange,
  branchId,
  revenueCenterId,
  onSuccess,
}) => {
  const [createWorkstation, { isLoading }] =useAddWorkstationMutation();
  const { data: revenueCenters = [], isLoading: loadingRevenueCenters } = useGetRevenueCentersQuery({
    branch: branchId,
  }, {
    skip: !branchId
  });

  const form = useForm<WorkstationFormData>({
    defaultValues: {
      name: '',
      branch: branchId,
      revenue_center: revenueCenterId || '',
      role: WorkstationRole.POS,
      ip_address: '',
      hostname: '',
      supports_magnetic_card: true,
      supports_employee_login: true,
      language: 'en',
      is_active: true,
    },
  });



  const onSubmit = async (data: WorkstationFormData) => {
    try {
      await createWorkstation(data).unwrap();
      handleApiSuccess('Workstation created successfully');
      form.reset();
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      handleApiError(error, 'Failed to create workstation');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  // Filter revenue centers for the current branch
  const filteredRevenueCenters = revenueCenters.filter(
    (rc) => rc.branch === branchId
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Add Workstation
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              rules={{ required: 'Workstation name is required' }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Workstation Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Main Counter POS, Waiter Tablet 1"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Workstation Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select workstation role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(WorkstationRole).map((role) => (
                          <SelectItem key={role} value={role}>
                            {getWorkstationRoleLabel(role)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="revenue_center"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Revenue Center</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select revenue center (optional)" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingRevenueCenters ? (
                          <SelectItem value="loading" disabled>
                            Loading revenue centers...
                          </SelectItem>
                        ) : filteredRevenueCenters.length === 0 ? (
                          <SelectItem value="no-revenue-centers" disabled>
                            No revenue centers available
                          </SelectItem>
                        ) : (
                          filteredRevenueCenters.map((rc) => (
                            <SelectItem key={rc.id} value={rc.revenue_center_code || ''}>
                              {rc.name} ({rc.revenue_center_code})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="ip_address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>IP Address</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., *************" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hostname"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Hostname</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., pos-terminal-01" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-3">
              <FormField
                control={form.control}
                name="supports_magnetic_card"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Magnetic Card Support</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Enable magnetic card reader functionality
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="supports_employee_login"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Employee Login Support</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Allow employees to log in to this workstation
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Active Status</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Enable this workstation for operations
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Create Workstation
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddWorkstationModal;
