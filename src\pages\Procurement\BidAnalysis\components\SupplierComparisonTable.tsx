import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Crown, 
  DollarSign, 
  Truck, 
  CheckCircle, 
  Building,
  Package,
  Clock,
  MessageSquare,
  Users,
  TrendingDown,
  TrendingUp,
  AlertTriangle
} from "lucide-react";
import { BidAnalysis } from "@/types/procurement";
import {
  useSelectSupplierMutation,
  useBulkSelectSupplierMutation,
} from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";
import SupplierSelectionModal from "../modals/SupplierSelectionModal";
import BulkSelectionModal from "../modals/BulkSelectionModal";

interface SupplierComparisonTableProps {
  bidAnalysis: BidAnalysis;
}

const SupplierComparisonTable = ({ bidAnalysis }: SupplierComparisonTableProps) => {
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [showSelectionModal, setShowSelectionModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [selectedItemForModal, setSelectedItemForModal] = useState<any>(null);

  const [selectSupplier] = useSelectSupplierMutation();
  const [bulkSelectSupplier] = useBulkSelectSupplierMutation();

  const handleItemSelection = (itemId: number, checked: boolean) => {
    setSelectedItems(prev => 
      checked 
        ? [...prev, itemId]
        : prev.filter(id => id !== itemId)
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(bidAnalysis.items?.map(item => item.id!) || []);
    } else {
      setSelectedItems([]);
    }
  };

  const handleQuickSelect = async (itemId: number, supplierId: number, unitPrice: number) => {
    try {
      await selectSupplier({
        bid_analysis_id: bidAnalysis.id!,
        item_id: itemId,
        supplier_data: {
          supplier_id: supplierId,
          unit_price: unitPrice,
          reason: "Quick selection based on best price"
        }
      }).unwrap();
      toast.success("Supplier selected successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to select supplier");
    }
  };

  const handleBulkSelection = async (selectionData: any) => {
    try {
      await bulkSelectSupplier({
        bid_analysis_id: bidAnalysis.id!,
        selection_data: selectionData
      }).unwrap();
      toast.success("Bulk selection completed successfully");
      setSelectedItems([]);
      setShowBulkModal(false);
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to perform bulk selection");
    }
  };

  const getBestPrice = (item: any) => {
    const prices = item.supplier_quotes?.map((q: any) => q.unit_price).filter((p: any) => p != null) || [];
    return prices.length > 0 ? Math.min(...prices) : null;
  };

  const getBestDelivery = (item: any) => {
    const deliveryTimes = item.supplier_quotes?.map((q: any) => q.delivery_time_days).filter((d: any) => d != null) || [];
    return deliveryTimes.length > 0 ? Math.min(...deliveryTimes) : null;
  };

  const getSupplierNames = () => {
    const suppliers = new Set<string>();
    bidAnalysis.items?.forEach(item => {
      item.supplier_quotes?.forEach(quote => {
        if (quote.supplier_name) suppliers.add(quote.supplier_name);
      });
    });
    return Array.from(suppliers);
  };

  const supplierNames = getSupplierNames();
  const selectedCount = selectedItems.length;
  const totalItems = bidAnalysis.items?.length || 0;
  const selectedItemsWithWinners = bidAnalysis.items?.filter(item => item.selected_supplier).length || 0;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{supplierNames.length}</div>
            <div className="text-sm text-gray-600">Suppliers</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{selectedItemsWithWinners}</div>
            <div className="text-sm text-gray-600">Winners Selected</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{totalItems - selectedItemsWithWinners}</div>
            <div className="text-sm text-gray-600">Pending Selection</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {totalItems > 0 ? Math.round((selectedItemsWithWinners / totalItems) * 100) : 0}%
            </div>
            <div className="text-sm text-gray-600">Completion</div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {bidAnalysis.status === "Draft" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Bulk Actions
              </span>
              <div className="text-sm font-normal text-gray-600">
                {selectedCount} of {totalItems} items selected
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all"
                  checked={selectedCount === totalItems && totalItems > 0}
                  onCheckedChange={handleSelectAll}
                />
                <Label htmlFor="select-all">Select All Items</Label>
              </div>
              
              <Button
                onClick={() => setShowBulkModal(true)}
                disabled={selectedCount === 0}
                variant="outline"
              >
                <Users className="mr-2 h-4 w-4" />
                Bulk Select Winner ({selectedCount})
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comparison Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Item-by-Item Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {bidAnalysis.items?.map((item) => {
              const bestPrice = getBestPrice(item);
              const bestDelivery = getBestDelivery(item);
              const isSelected = selectedItems.includes(item.id!);
              
              return (
                <div key={item.id} className="border rounded-lg p-6">
                  {/* Item Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      {bidAnalysis.status === "Draft" && (
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={(checked) => handleItemSelection(item.id!, checked as boolean)}
                        />
                      )}
                      <div>
                        <h3 className="font-semibold text-lg flex items-center gap-2">
                          <Package className="h-5 w-5" />
                          {item.product_name}
                        </h3>
                        <div className="text-sm text-gray-600">
                          Quantity: {item.quantity} {item.unit_of_measure_name} • 
                          Code: {item.product_code}
                        </div>
                        {item.specifications && (
                          <div className="text-sm text-gray-500 mt-1">
                            Specs: {item.specifications}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Winner Badge */}
                    {item.selected_supplier && (
                      <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                        <Crown className="h-3 w-3" />
                        Winner: {item.selected_supplier_name}
                      </Badge>
                    )}
                  </div>

                  {/* Supplier Quotes Table */}
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Unit Price</TableHead>
                          <TableHead>Total Price</TableHead>
                          <TableHead>Delivery Time</TableHead>
                          <TableHead>Payment Terms</TableHead>
                          <TableHead>Notes</TableHead>
                          {bidAnalysis.status === "Draft" && <TableHead>Actions</TableHead>}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {item.supplier_quotes?.map((quote, index) => (
                          <TableRow key={index} className={!quote.is_available ? "opacity-50" : ""}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Building className="h-4 w-4 text-gray-500" />
                                <span className="font-medium">{quote.supplier_name}</span>
                                {!quote.is_available && (
                                  <Badge variant="secondary" className="text-xs">No Quote</Badge>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {quote.unit_price ? (
                                <div className="flex items-center gap-1">
                                  {quote.unit_price === bestPrice && bestPrice !== null && (
                                    <Crown className="h-4 w-4 text-yellow-500" />
                                  )}
                                  <span className={quote.unit_price === bestPrice ? "font-bold text-green-600" : ""}>
                                    ${quote.unit_price?.toLocaleString()}
                                  </span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {quote.total_price ? (
                                <span className="font-medium">
                                  ${quote.total_price.toLocaleString()}
                                </span>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              {quote.delivery_time_days ? (
                                <div className="flex items-center gap-1">
                                  {quote.delivery_time_days === bestDelivery && bestDelivery !== null && (
                                    <Crown className="h-4 w-4 text-yellow-500" />
                                  )}
                                  <Truck className="h-3 w-3 text-gray-500" />
                                  <span className={quote.delivery_time_days === bestDelivery ? "font-bold text-green-600" : ""}>
                                    {quote.delivery_time_days} days
                                  </span>
                                </div>
                              ) : (
                                <span className="text-gray-400">-</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <span className="text-sm">{quote.payment_terms || "-"}</span>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm text-gray-600 max-w-32 truncate">
                                {quote.notes || "-"}
                              </div>
                            </TableCell>
                            {bidAnalysis.status === "Draft" && (
                              <TableCell>
                                {quote.is_available && quote.unit_price && (
                                  <div className="flex gap-1">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleQuickSelect(item.id!, quote.supplier!, quote.unit_price!)}
                                      disabled={item.selected_supplier === quote.supplier}
                                    >
                                      {item.selected_supplier === quote.supplier ? (
                                        <CheckCircle className="h-3 w-3" />
                                      ) : (
                                        "Select"
                                      )}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => {
                                        setSelectedItemForModal(item);
                                        setShowSelectionModal(true);
                                      }}
                                    >
                                      <MessageSquare className="h-3 w-3" />
                                    </Button>
                                  </div>
                                )}
                              </TableCell>
                            )}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Selection Reason */}
                  {item.selection_reason && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                      <div className="flex items-center gap-2 text-blue-800 text-sm font-medium mb-1">
                        <MessageSquare className="h-3 w-3" />
                        Selection Reason
                      </div>
                      <p className="text-blue-700 text-sm">{item.selection_reason}</p>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <SupplierSelectionModal
        open={showSelectionModal}
        onClose={() => {
          setShowSelectionModal(false);
          setSelectedItemForModal(null);
        }}
        item={selectedItemForModal}
        bidAnalysisId={bidAnalysis.id!}
      />

      <BulkSelectionModal
        open={showBulkModal}
        onClose={() => setShowBulkModal(false)}
        selectedItems={selectedItems}
        bidAnalysis={bidAnalysis}
        onBulkSelect={handleBulkSelection}
      />
    </div>
  );
};

export default SupplierComparisonTable;
