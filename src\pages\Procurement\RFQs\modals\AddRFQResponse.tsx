import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Building, FileText, Upload, DollarSign } from "lucide-react";
import {
  useCreateRFQResponseForRFQMutation,
  useGetRFQQuery,
  useGetSuppliersQuery,
  useGetTaxRatesQuery,
} from "@/redux/slices/procurement";
import { RFQResponseCreateFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddRFQResponseProps {
  open: boolean;
  onClose: () => void;
  rfqId: number;
}

const AddRFQResponse = ({ open, onClose, rfqId }: AddRFQResponseProps) => {
  const [createRFQResponse, { isLoading: creating }] = useCreateRFQResponseForRFQMutation();

  // Fetch supporting data
  const { data: rfq } = useGetRFQQuery(rfqId);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: taxRates } = useGetTaxRatesQuery({});

  // Default currencies - only KES for now
  const currencies = {
    results: [
      { id: 1, code: "KES", name: "Kenya Shillings" }
    ]
  };

  const [formData, setFormData] = useState<RFQResponseCreateFormData>({
    supplier_id: 0,
    notes: "",
    items: [],
  });

  const handleInputChange = (field: keyof RFQResponseCreateFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };



  const calculateTotalValue = () => {
    return formData.items.reduce((total, item, index) => {
      const quantity = parseFloat(rfq?.rfq_items?.[index]?.quantity || "0");
      const unitPrice = parseFloat(item.unit_price || "0");
      return total + (quantity * unitPrice);
    }, 0);
  };

  const resetForm = () => {
    setFormData({
      supplier_id: 0,
      notes: "",
      items: rfq?.rfq_items?.map(() => ({
        unit_price: "",
        delivery_time_days: undefined,
        currency: "KES",
        tax_rate: undefined,
      })) || [],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation - Check all required fields per API specification
    if (!formData.supplier_id) {
      toast.error("Please select a supplier");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.unit_price && item.currency
    );

    if (validItems.length === 0) {
      toast.error("Please provide unit price and currency for at least one item");
      return;
    }

    try {
      // Find the supplier code from the selected supplier ID
      const selectedSupplier = suppliers?.results?.find((s: any) => s.id === formData.supplier_id);

      if (!selectedSupplier) {
        toast.error("Selected supplier not found");
        return;
      }

      const payload = {
        supplier_id: selectedSupplier.code, // Send supplier code instead of ID
        notes: formData.notes || "",
        items: validItems,
      };

      const result = await createRFQResponse({ id: rfqId, ...payload }).unwrap();

      toast.success(`RFQ response created successfully. Response Code: ${result.code}`);
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create RFQ response");
    }
  };

  useEffect(() => {
    if (open && rfq) {
      setFormData(prev => ({
        ...prev,
        items: rfq.rfq_items?.map(() => ({
          unit_price: "",
          delivery_time_days: undefined,
          currency: "KES",
          tax_rate: undefined,
        })) || [],
      }));
    }
  }, [open, rfq]);

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Add RFQ Response - {rfq?.rfq_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Building className="h-4 w-4" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="supplier">Supplier *</Label>
                  <Select
                    value={formData.supplier_id ? formData.supplier_id.toString() : ""}
                    onValueChange={(value) => handleInputChange("supplier_id", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers?.results?.map((supplier: any) => (
                        <SelectItem key={supplier.id} value={supplier.id.toString()}>
                          {supplier.name} ({supplier.code || supplier.id})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or comments about this response..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Optional notes about this RFQ response
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Items Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <DollarSign className="h-4 w-4" />
                Item Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {rfq?.rfq_items?.map((rfqItem, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">Product: {rfqItem.product}</h4>
                      <p className="text-sm text-gray-600">
                        Quantity: {rfqItem.quantity} (Unit: {rfqItem.unit_of_measure})
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label>Unit Price *</Label>
                      <Input
                        placeholder="Enter unit price"
                        value={formData.items[index]?.unit_price || ""}
                        onChange={(e) => handleItemChange(index, "unit_price", e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Unit price (required)
                      </p>
                    </div>

                    <div>
                      <Label>Currency *</Label>
                      <Select
                        value={formData.items[index]?.currency || "KES"}
                        onValueChange={(value) => handleItemChange(index, "currency", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          {currencies?.results?.map((currency: any) => (
                            <SelectItem key={currency.id} value={currency.code}>
                              {currency.code} - {currency.name}
                            </SelectItem>
                          )) || (
                            <>
                              <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                              <SelectItem value="USD">USD - US Dollar</SelectItem>
                              <SelectItem value="EUR">EUR - Euro</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Delivery Time (Days)</Label>
                      <Input
                        type="number"
                        min="1"
                        placeholder="Days"
                        value={formData.items[index]?.delivery_time_days || ""}
                        onChange={(e) => handleItemChange(index, "delivery_time_days", parseInt(e.target.value) || undefined)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label>Tax Rate</Label>
                      <Select
                        value={formData.items[index]?.tax_rate?.toString() || ""}
                        onValueChange={(value) => handleItemChange(index, "tax_rate", value ? parseFloat(value) : undefined)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select tax rate" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="0">No Tax</SelectItem>
                          {taxRates?.results?.map((taxRate: any) => (
                            <SelectItem key={taxRate.id} value={taxRate.percentage}>
                              {taxRate.name} ({taxRate.percentage}%)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              ))}

              {/* Total Summary */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Estimated Total Value:</span>
                  <span>KES {calculateTotalValue().toLocaleString()}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Calculated from unit prices and RFQ quantities (excluding taxes)
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Response...
                </>
              ) : (
                "Submit Response"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRFQResponse;
