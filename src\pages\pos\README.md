# POS Configuration Module

A comprehensive Point of Sale (POS) configuration system built with React, TypeScript, and modern UI components. This module provides complete management of POS infrastructure including branches, revenue centers, workstations, tax configuration, and printer management.

## Features

### 🏢 Branch Management
- **Add New Branch**: Create branches with auto-generated codes, location details, timezone, and currency settings
- **Branch Configuration**: Manage tax settings, localization, and operational status
- **Branch Details**: Comprehensive view with revenue centers, workstations, and tax configuration
- **Status Management**: Toggle branch active/inactive status

### 🏪 Revenue Center Management
- **Revenue Center Setup**: Configure business units within branches that generate income
- **Service Charge Configuration**: Set up service charges with inclusive/exclusive options
- **Sales Categories**: Assign product categories (food, beverages, etc.)
- **Tax Class Assignment**: Link revenue centers to appropriate tax classes
- **Workstation Integration**: View and manage assigned workstations

### 💻 Workstation Management
- **Device Configuration**: Set up POS devices with different types:
  - Full POS (Orders + Payments)
  - Order Only
  - Kitchen Display
  - Self-Service Kiosk
  - Waiter Tablet
- **Network Configuration**: IP address and hostname management
- **Printer Assignment**: Link workstations to multiple printers
- **Hardware Features**: Touchscreen compatibility settings
- **Status Monitoring**: Online/offline status tracking

### 🧾 Tax Configuration
- **Tax Classes**: Create categories for different types of taxes
- **Tax Rates**: Set up percentage-based tax rates with inclusive/exclusive options
- **Revenue Center Assignment**: Apply tax classes to specific revenue centers
- **Tax Calculation Preview**: Real-time calculation examples
- **Usage Tracking**: Monitor which items and centers use each tax class

### 🖨️ Printer Management
- **Printer Setup**: Configure thermal and receipt printers
- **Interface Types**: Support for LAN, Wi-Fi, and Bluetooth connections
- **Printer Types**: Kitchen, Receipt, Bill, and Backup printers
- **Print Routing**: Route specific menu items and categories to appropriate printers
- **Workstation Assignment**: Link printers to multiple workstations
- **Test Functionality**: Test printer connections and print samples

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **UI Components**: Shadcn/ui with Tailwind CSS
- **State Management**: Zustand for global state
- **Forms**: React Hook Form with validation
- **Routing**: React Router v6
- **Icons**: Lucide React
- **Data Tables**: TanStack Table
- **Date Handling**: Built-in Date APIs

## Project Structure

```
src/pages/pos/
├── index.tsx                 # Main POS module wrapper
├── Dashboard.tsx             # POS configuration dashboard
├── branches/                 # Branch management
│   ├── index.tsx            # Branch list and management
│   ├── BranchForm.tsx       # Add/edit branch form
│   ├── BranchDetail.tsx     # Branch detail view
│   ├── NewBranch.tsx        # New branch wrapper
│   └── EditBranch.tsx       # Edit branch wrapper
├── revenue-centers/          # Revenue center management
│   ├── index.tsx            # Revenue center list
│   ├── RevenueCenterForm.tsx # Add/edit form
│   ├── RevenueCenterDetail.tsx # Detail view
│   ├── NewRevenueCenter.tsx # New wrapper
│   └── EditRevenueCenter.tsx # Edit wrapper
├── workstations/            # Workstation management
│   ├── index.tsx            # Workstation list
│   ├── WorkstationForm.tsx  # Add/edit form
│   ├── WorkstationDetail.tsx # Detail view
│   ├── NewWorkstation.tsx   # New wrapper
│   └── EditWorkstation.tsx  # Edit wrapper
├── tax-configuration/       # Tax management
│   ├── index.tsx            # Tax configuration dashboard
│   ├── TaxClassForm.tsx     # Tax class form
│   ├── TaxRateForm.tsx      # Tax rate form
│   ├── NewTaxClass.tsx      # New tax class wrapper
│   ├── NewTaxRate.tsx       # New tax rate wrapper
│   └── README.md            # This file
└── printers/                # Printer management
    ├── index.tsx            # Printer list
    ├── PrinterForm.tsx      # Add/edit form
    ├── NewPrinter.tsx       # New wrapper
    └── EditPrinter.tsx      # Edit wrapper
```

## Supporting Files

```
src/
├── types/pos.ts             # TypeScript type definitions
├── utils/pos.ts             # Utility functions
├── hooks/usePOSData.ts      # Data fetching hooks
├── zustand/usePOSStore.tsx  # Global state management
└── components/pos/          # Reusable POS components
    ├── StatusBadge.tsx      # Status indicator component
    ├── POSCard.tsx          # Dashboard card component
    └── SearchAndFilter.tsx  # Search and filter component
```

## Key Features

### 🎨 Modern UI/UX
- Clean, responsive design with Tailwind CSS
- Consistent component library using Shadcn/ui
- Intuitive navigation with breadcrumbs and clear actions
- Status indicators and visual feedback
- Mobile-responsive layouts

### 🔧 Form Management
- Comprehensive form validation with React Hook Form
- Auto-generation of codes and IDs
- Real-time validation feedback
- Conditional field rendering
- Multi-step form support

### 📊 Data Management
- Zustand store for global state management
- Custom hooks for data fetching and CRUD operations
- Optimistic updates for better UX
- Error handling and loading states
- Local storage persistence for filters and selections

### 🔍 Search & Filtering
- Real-time search across all entities
- Advanced filtering by status, type, branch, etc.
- Sortable data tables with pagination
- Export capabilities
- Bulk operations support

### 🔗 Integration Ready
- RESTful API integration patterns
- Mock data for development and testing
- Type-safe API calls with TypeScript
- Error boundary implementation
- Loading state management

## Usage Examples

### Creating a New Branch
1. Navigate to POS Configuration → Branch Management
2. Click "Add New Branch"
3. Fill in branch details (name, location, timezone, currency)
4. Configure tax settings
5. Set branch status (active/inactive)
6. Save to create the branch

### Setting Up a Workstation
1. Go to POS Configuration → Workstations
2. Click "Add Workstation"
3. Select branch and revenue center
4. Choose workstation type (Full POS, Tablet, etc.)
5. Configure network settings (IP, hostname)
6. Assign printers
7. Save configuration

### Configuring Tax Rates
1. Navigate to POS Configuration → Tax Configuration
2. Create tax classes first (e.g., "Food Tax", "Beverage Tax")
3. Add tax rates to each class with percentages
4. Set inclusive/exclusive tax calculation
5. Assign to revenue centers
6. Preview calculations

## API Integration

The module is designed to work with RESTful APIs. Key endpoints expected:

- `GET/POST/PUT/DELETE /api/branches` - Branch management
- `GET/POST/PUT/DELETE /api/revenue-centers` - Revenue center management
- `GET/POST/PUT/DELETE /api/workstations` - Workstation management
- `GET/POST/PUT/DELETE /api/tax-classes` - Tax class management
- `GET/POST/PUT/DELETE /api/tax-rates` - Tax rate management
- `GET/POST/PUT/DELETE /api/printers` - Printer management

## Development

### Adding New Features
1. Create new components in the appropriate directory
2. Add TypeScript types to `src/types/pos.ts`
3. Update the Zustand store if needed
4. Add routes to `App.tsx`
5. Update navigation in the sidebar

### Testing
- Unit tests for utility functions
- Component testing with React Testing Library
- Integration tests for form submissions
- E2E tests for complete workflows

## Contributing

1. Follow the existing code structure and patterns
2. Use TypeScript for all new code
3. Implement proper error handling
4. Add loading states for async operations
5. Ensure responsive design
6. Write comprehensive tests

## License

This POS Configuration module is part of the GMC Client application.
