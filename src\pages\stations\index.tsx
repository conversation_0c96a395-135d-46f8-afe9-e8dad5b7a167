import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditCard, KeyRound } from "lucide-react";
import Logo from "@/assets/logo.png";

const StationWelcome: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-lg shadow-2xl border-border/50 backdrop-blur-sm">
        <CardHeader className="text-center space-y-6 pb-8">
          <div className="flex justify-center">
            <div className="relative">
              <img
                src={Logo}
                alt="Logo"
                className="h-24 w-auto drop-shadow-lg"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/10 to-transparent rounded-full blur-xl" />
            </div>
          </div>
          <div className="space-y-3">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Welcome to GMC POS
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              Please select your preferred login method
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-4 px-8 pb-8">
          <Button
            variant="outline"
            size="lg"
            className="w-full h-16 flex items-center justify-center gap-3 text-lg font-semibold hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-primary/50 group"
            asChild
          >
            <Link to="/stations/pin-login">
              <KeyRound className="h-6 w-6 group-hover:text-primary transition-colors" />
              Login with PIN
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            className="w-full h-16 flex items-center justify-center gap-3 text-lg font-semibold hover:scale-105 transition-all duration-200 hover:shadow-lg border-border/50 hover:border-primary/50 group"
            asChild
          >
            <Link to="/stations/card-login">
              <CreditCard className="h-6 w-6 group-hover:text-primary transition-colors" />
              Login with Card
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default StationWelcome;
