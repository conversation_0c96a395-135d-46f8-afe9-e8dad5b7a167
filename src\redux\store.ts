import { configureStore } from "@reduxjs/toolkit";
import { apiSlice } from "./apiSlice";
import { persistedAuthReducer } from "./authSlice";
import { FLUSH ,REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER, persistStore } from "redux-persist";

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    auth: persistedAuthReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(apiSlice.middleware),
  devTools: true,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;