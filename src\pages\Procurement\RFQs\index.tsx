import { Screen } from "@/app-components/layout/screen";
import AddRFQ from "./modals/AddRFQ";
import AddRFQResponse from "./modals/AddRFQResponse";
import CancelRFQ from "./modals/CancelRFQ";
import ViewRFQ from "./modals/ViewRFQ";
import { useState } from "react";
import {
  useGetRFQsQuery,
  useGetSuppliersQuery,
  useGetProcurementStoresQuery,
  useCloseRFQMutation,
  useCancelRFQMutation,
  useDeleteRFQMutation,
  useCreateBidAnalysisFromRFQMutation,
} from "@/redux/slices/procurement";
import { ColumnDef } from "@tanstack/react-table";
import { RFQ } from "@/types/procurement";
import { Link } from "react-router-dom";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Plus,
  MoreHorizontal,
  Eye,
  Send,
  CheckCircle,
  XCircle,
  Trash2,
  Download,
  Search,
  FileText,
  Clock,
  Users,
  BarChart
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";

const RFQsIndex = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAddResponseModal, setShowAddResponseModal] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedRFQ, setSelectedRFQ] = useState<any>(null);
  const [filters, setFilters] = useState({
    status: "all",
    supplier: "all",
    delivery_location: "all",
    category: "all",
    search: "",
  });

  // Transform filters for API (remove "all" values)
  const apiFilters = Object.fromEntries(
    Object.entries(filters).filter(([key, value]) => value !== "all" && value !== "")
  );

  // API queries
  const { data: rfqsData, isLoading } = useGetRFQsQuery(apiFilters);
  const { data: suppliers } = useGetSuppliersQuery({});
  const { data: stores } = useGetProcurementStoresQuery({});

  // Mutation hooks
  const [closeRFQ] = useCloseRFQMutation();
  const [cancelRFQ] = useCancelRFQMutation();
  const [deleteRFQ] = useDeleteRFQMutation();
  const [createBidAnalysisFromRFQ] = useCreateBidAnalysisFromRFQMutation();

  // Handler functions
  const handleClose = async (id: number) => {
    try {
      await closeRFQ(id).unwrap();
      toast.success("RFQ closed successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to close RFQ");
    }
  };

  const handleCancel = (rfq: any) => {
    setSelectedRFQ(rfq);
    setIsCancelModalOpen(true);
  };

  const handleView = (rfq: any) => {
    setSelectedRFQ(rfq);
    setIsViewModalOpen(true);
  };

  const handleCreateResponse = (rfq: any) => {
    setSelectedRFQ(rfq);
    setShowAddResponseModal(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this RFQ?")) {
      try {
        await deleteRFQ(id).unwrap();
        toast.success("RFQ deleted successfully");
      } catch (error: any) {
        toast.error(error?.data?.message || "Failed to delete RFQ");
      }
    }
  };

  const handleCreateBidAnalysis = async (rfq: any) => {
    if (window.confirm("Create Bid Analysis? This will close the RFQ and create a bid analysis for evaluation.")) {
      try {
        const payload = {
          code: `BA-${String(Date.now()).slice(-4)}`, // Generate a simple code
          rfq: rfq.code,
          created_by: "EMP-001", // You might want to get this from user context
          split_award: false, // Default to single supplier
          recommendation_notes: "",
          selected_responses: null,
          finalized_at: null,
        };

        await createBidAnalysisFromRFQ({
          rfq_id: rfq.id,
          ...payload
        }).unwrap();

        toast.success("Bid Analysis created successfully and RFQ closed");
      } catch (error: any) {
        console.error("Error creating bid analysis:", error);
        toast.error(error?.data?.message || "Failed to create bid analysis");
      }
    }
  };

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: {
        variant: "secondary" as const,
        color: "bg-gray-100 text-gray-800",
        icon: "📝",
        description: "In preparation"
      },
      Open: {
        variant: "default" as const,
        color: "bg-green-100 text-green-800",
        icon: "📂",
        description: "Accepting responses"
      },
      Closed: {
        variant: "default" as const,
        color: "bg-blue-100 text-blue-800",
        icon: "🔒",
        description: "Response period ended"
      },
      Cancelled: {
        variant: "destructive" as const,
        color: "bg-red-100 text-red-800",
        icon: "❌",
        description: "Cancelled"
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <div className="flex flex-col">
        <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
          <span>{config.icon}</span>
          {status}
        </Badge>
        <span className="text-xs text-gray-500 mt-1">
          {config.description}
        </span>
      </div>
    );
  };

  // Search handler
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search: value }));
  };

  // Table columns
  const columns: ColumnDef<RFQ>[] = [
    {
      accessorKey: "rfq_number",
      header: "RFQ Number",
      cell: ({ row }) => {
        const rfqNumber = row.getValue("rfq_number") as string;
        const id = row.original.id;
        return (
          <div className="flex flex-col">
            <button
              onClick={() => handleView(row.original)}
              className="font-semibold text-blue-600 hover:text-blue-800 hover:underline text-left"
              title="Click to view details"
            >
              {rfqNumber || `RFQ-${String(id).padStart(5, '0')}`}
            </button>
            <span className="text-xs text-gray-500">
              ID: {id}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "created_by",
      header: "Created By",
      cell: ({ row }) => {
        const value = row.getValue("created_by") as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {value || "Not Assigned"}
            </span>
            {value && (
              <span className="text-xs text-gray-500">
                Employee: {value}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "rfq_items",
      header: "Items",
      cell: ({ row }) => {
        const items = row.getValue("rfq_items") as any[] || [];
        const itemCount = items.length;
        const totalQuantity = items.reduce((sum: number, item: any) =>
          sum + parseFloat(item.quantity || 0), 0
        );

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {itemCount} {itemCount === 1 ? 'item' : 'items'}
            </span>
            <span className="text-xs text-gray-500">
              Total qty: {totalQuantity.toFixed(2)}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "response_deadline",
      header: "Response Deadline",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4 text-gray-500" />
          <span className="text-sm">
            {row.original.response_deadline 
              ? new Date(row.original.response_deadline).toLocaleDateString()
              : "-"
            }
          </span>
        </div>
      ),
    },
    {
      accessorKey: "supplier_category",
      header: "Supplier Category",
      cell: ({ row }) => {
        const category = row.getValue("supplier_category") as string;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {category || "Not Specified"}
            </span>
            {!category && (
              <span className="text-xs text-gray-500">
                All categories
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.original.status || "Open"),
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        if (!date) return <span className="text-gray-400">N/A</span>;

        const createdDate = new Date(date);
        const today = new Date();
        const diffTime = today.getTime() - createdDate.getTime();
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor(diffTime / (1000 * 60 * 60));

        let timeAgo = "";
        if (diffDays === 0) {
          if (diffHours === 0) {
            timeAgo = "Just now";
          } else {
            timeAgo = `${diffHours}h ago`;
          }
        } else if (diffDays === 1) {
          timeAgo = "Yesterday";
        } else {
          timeAgo = `${diffDays} days ago`;
        }

        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {createdDate.toLocaleDateString()}
            </span>
            <span className="text-xs text-gray-500">
              {timeAgo}
            </span>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const rfq = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleView(rfq)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>

              {rfq.status === "Open" && (
                <>
                  <DropdownMenuItem onClick={() => handleCreateResponse(rfq)}>
                    <FileText className="mr-2 h-4 w-4" />
                    Create RFQ Response
                  </DropdownMenuItem>

                  <DropdownMenuItem onClick={() => handleCreateBidAnalysis(rfq)}>
                    <BarChart className="mr-2 h-4 w-4" />
                    Create Bid Analysis
                  </DropdownMenuItem>

                  <DropdownMenuItem onClick={() => handleClose(rfq.id!)}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Close RFQ
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleCancel(rfq)}
                    className="text-red-600"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Cancel
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => handleDelete(rfq.id!)}
                    className="text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
              

            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Request for Quotations (RFQ)</h1>
            <p className="text-gray-600 mt-1">Manage RFQs and track supplier responses</p>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create RFQ
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        {rfqsData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {(() => {
              const results = rfqsData?.data?.results || rfqsData?.results || [];
              const stats = results.reduce((acc: any, rfq: any) => {
                acc.total++;
                acc[rfq.status] = (acc[rfq.status] || 0) + 1;
                acc.totalItems += rfq.rfq_items?.length || 0;
                return acc;
              }, { total: 0, totalItems: 0, Draft: 0, Open: 0, Closed: 0, Cancelled: 0 }) || {};

              return (
                <>
                  <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <FileText className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total RFQs</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Open RFQs</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.Open || 0}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <XCircle className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Closed RFQs</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.Closed || 0}</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white p-6 rounded-lg shadow-sm border">
                    <div className="flex items-center">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <FileText className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Total Items</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalItems}</p>
                      </div>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search RFQs..."
              className="w-64"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
          
          <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Open">Open</SelectItem>
              <SelectItem value="Closed">Closed</SelectItem>
              <SelectItem value="Cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filters.supplier} onValueChange={(value) => setFilters(prev => ({ ...prev, supplier: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by supplier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers?.results?.map((supplier: any) => (
                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.delivery_location} onValueChange={(value) => setFilters(prev => ({ ...prev, delivery_location: value }))}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              {stores?.results?.map((store: any) => (
                <SelectItem key={store.id} value={store.id.toString()}>
                  {store.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Data Table */}
        <DataTable<RFQ>
          data={rfqsData?.data?.results || rfqsData?.results || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={filters.search}
              name="searchInput"
              type="search"
              onChange={(e) => handleSearch(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search RFQs..."
            />
          }
        />

        {/* Add RFQ Modal */}
        <AddRFQ
          open={showAddModal}
          onClose={() => setShowAddModal(false)}
        />

        {isCancelModalOpen && selectedRFQ && (
          <CancelRFQ
            isOpen={isCancelModalOpen}
            onClose={() => {
              setIsCancelModalOpen(false);
              setSelectedRFQ(null);
            }}
            rfqId={selectedRFQ.id}
            rfqNumber={selectedRFQ.rfq_number}
          />
        )}

        {isViewModalOpen && selectedRFQ && (
          <ViewRFQ
            isOpen={isViewModalOpen}
            onClose={() => {
              setIsViewModalOpen(false);
              setSelectedRFQ(null);
            }}
            rfq={selectedRFQ}
          />
        )}

        {showAddResponseModal && selectedRFQ && (
          <AddRFQResponse
            open={showAddResponseModal}
            onClose={() => {
              setShowAddResponseModal(false);
              setSelectedRFQ(null);
            }}
            rfqId={selectedRFQ.id}
          />
        )}
      </div>
    </Screen>
  );
};

export default RFQsIndex;
