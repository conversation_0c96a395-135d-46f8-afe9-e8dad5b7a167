import React, { useState, useEffect } from "react";
import { X, Utensils, Clock, Star, ChefHat, Building2, Users, Tag, Loader2, Check } from "lucide-react";
import { useAddMenuMutation } from "@/redux/slices/menuMake";
import { useGetBranchesQuery } from "@/redux/slices/branches";
import { useGetPrintersQuery } from "@/redux/slices/printers";
import { useGetTaxClassesQuery } from "@/redux/slices/taxClasses";
import { useGetMenuGroupsQuery } from "@/redux/slices/menuGroup";
import { useGetMenuSubGroupsQuery } from "@/redux/slices/subGroup";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

interface CreateMenuItemModalProps {
  onClose: () => void;
  onSave: (newItem: {
    title: string;
    description: string;
    imageUrl: string;
    price: string;
    category: string;
    rating: number;
    prepTime: string;
  }) => void;
}

export function CreateMenuItemModal({ onClose, onSave }: CreateMenuItemModalProps) {
  
  // API Queries
  const { data: MenuGroup, isLoading: isLoadingGroup } = useGetMenuGroupsQuery({});
  const { data: MenuSubGroup, isLoading: isLoadingMenuSubGroup, error: MenuSubGroupError } = useGetMenuSubGroupsQuery({});
  const { data: branchData, isLoading: isLoadingBranch, error: branchError } = useGetBranchesQuery({});
  const { data: Printer, isLoading: isLoadingPrinter, error: PrinterError } = useGetPrintersQuery({});
  const { data: Tax, isLoading: isLoadingTaxClass, error: TaxError } = useGetTaxClassesQuery({});
  const [addFoodOrder, { isLoading: isSubmitting }] = useAddMenuMutation();
  
  // Console log all API responses
  useEffect(() => {
    console.log("=== API DATA STRUCTURES ===");
    
    console.log("MenuGroup Data:", {
      data: MenuGroup,
      isLoading: isLoadingGroup,
      structure: MenuGroup ? Object.keys(MenuGroup) : "No data"
    });
    
    console.log("MenuSubGroup Data:", {
      data: MenuSubGroup,
      isLoading: isLoadingMenuSubGroup,
      error: MenuSubGroupError,
      structure: MenuSubGroup ? Object.keys(MenuSubGroup) : "No data"
    });
    
    console.log("Branch Data:", {
      data: branchData,
      isLoading: isLoadingBranch,
      error: branchError,
      structure: branchData ? Object.keys(branchData) : "No data",
      firstItem: branchData && branchData.length > 0 ? branchData[0] : "No items"
    });
    
    console.log("Printer Data:", {
      data: Printer,
      isLoading: isLoadingPrinter,
      error: PrinterError,
      structure: Printer ? Object.keys(Printer) : "No data"
    });
    
    console.log("Tax Data:", {
      data: Tax,
      isLoading: isLoadingTaxClass,
      error: TaxError,
      structure: Tax ? Object.keys(Tax) : "No data"
    });
    
    console.log("=== END API DATA STRUCTURES ===");
  }, [MenuGroup, MenuSubGroup, branchData, Printer, Tax, isLoadingGroup, isLoadingMenuSubGroup, isLoadingBranch, isLoadingPrinter, isLoadingTaxClass]);

  // Log individual API responses when they change
  useEffect(() => {
    if (MenuGroup) {
      console.log("MenuGroup Response:", MenuGroup);
      if (MenuGroup?.data?.results && Array.isArray(MenuGroup.data.results)) {
        console.log("MenuGroup Array Length:", MenuGroup.data.results.length);
        if (MenuGroup.data.results.length > 0) {
          console.log("MenuGroup First Item:", MenuGroup.data.results[0]);
        }
      }
    }
  }, [MenuGroup]);

  useEffect(() => {
    if (MenuSubGroup) {
      console.log("MenuSubGroup Response:", MenuSubGroup);
      if (MenuSubGroup?.data?.results && Array.isArray(MenuSubGroup.data.results)) {
        console.log("MenuSubGroup Array Length:", MenuSubGroup.data.results.length);
        if (MenuSubGroup.data.results.length > 0) {
          console.log("MenuSubGroup First Item:", MenuSubGroup.data.results[0]);
        }
      }
    }
  }, [MenuSubGroup]);

  useEffect(() => {
    if (branchData) {
      console.log("Branch Response:", branchData);
      if (Array.isArray(branchData)) {
        console.log("Branch Array Length:", branchData.length);
        if (branchData.length > 0) {
          console.log("Branch First Item:", branchData[0]);
          console.log("Branch First Item Keys:", Object.keys(branchData[0]));
        }
      }
    }
  }, [branchData]);

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    availability_type: "ALWAYS",
    available_days: {},
    available_times: {},
    ordering_channels: {},
    is_active: true,
    branch: "",
    menu_group: "",
    menu_sub_group: "",
  });

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  // Set default branch when branches are loaded
  useEffect(() => {
    if (branchData && branchData.length > 0 && !formData.branch) {
      const branchId = branchData[0].id || branchData[0]._id || "";
      console.log("Setting default branch:", {
        branchId,
        branchItem: branchData[0]
      });
      setFormData(prev => ({
        ...prev,
        branch: branchId
      }));
    }
  }, [branchData, formData.branch]);

  const handleInputChange = (name: string, value: string | boolean) => {
    console.log("Form field changed:", { name, value });
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const payload = {
      name: formData.name,
      description: formData.description,
      availability_type: formData.availability_type,
      available_days: formData.available_days,
      available_times: formData.available_times,
      ordering_channels: formData.ordering_channels,
      is_active: formData.is_active,
      branch: formData.branch,
      menu_group: formData.menu_group,
      menu_sub_group: formData.menu_sub_group,
    };
    
    console.log("Submitting form with payload:", payload);
    
    try {
      const result = await addFoodOrder(payload).unwrap();
      console.log("Menu item created successfully:", result);
      
      const savedItem = {
        title: formData.name,
        description: formData.description,
        imageUrl: "",
        price: "",
        category: "",
        rating: 0,
        prepTime: "",
      };
      
      console.log("Calling onSave with:", savedItem);
      onSave(savedItem);
      onClose();
    } catch (error) {
      console.error("Failed to add menu item:", error);
      console.error("Error details:", { error, payload, formData });
    }
  };

  // Log form data changes
  useEffect(() => {
    console.log("Form data updated:", formData);
  }, [formData]);

  const isFormValid = formData.name && formData.description && formData.branch && formData.menu_group && formData.menu_sub_group;

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return "Basic Information";
      case 2: return "Organization";
      case 3: return "Settings & Review";
      default: return "";
    }
  };

  const getStepDescription = (step: number) => {
    switch (step) {
      case 1: return "Add the basic details of your menu item";
      case 2: return "Choose the category and location";
      case 3: return "Configure availability and finalize";
      default: return "";
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getFilteredSubGroups = () => {
    if (!MenuSubGroup?.data?.results || !formData.menu_group) return [];
    return MenuSubGroup.data.results.filter((subGroup: any) => 
      subGroup.group.toString() === formData.menu_group
    );
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm animate-in fade-in duration-300"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden animate-in zoom-in-95 slide-in-from-bottom-4 duration-300">
        
        {/* Header with progress */}
        <div className="relative bg-gradient-to-br from-orange-500 via-red-500 to-pink-600 p-6 text-white">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                    <ChefHat className="h-6 w-6" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">Create Menu Item</h2>
                    <p className="text-white/90 text-sm">
                      {getStepDescription(currentStep)}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 rounded-full"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Progress Steps */}
              <div className="flex items-center gap-4">
                {[1, 2, 3].map((step) => (
                  <div key={step} className="flex items-center gap-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                      step < currentStep 
                        ? 'bg-white text-orange-500' 
                        : step === currentStep 
                          ? 'bg-white/20 text-white ring-2 ring-white' 
                          : 'bg-white/10 text-white/60'
                    }`}>
                      {step < currentStep ? <Check className="h-4 w-4" /> : step}
                    </div>
                    <span className={`text-sm font-medium ${step === currentStep ? 'text-white' : 'text-white/60'}`}>
                      {getStepTitle(step)}
                    </span>
                    {step < 3 && <div className="w-8 h-0.5 bg-white/20 ml-2" />}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <ScrollArea className="flex-1 p-6 max-h-[60vh]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <Card className="border-0 shadow-none">
                <CardContent className="p-0 space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-semibold flex items-center gap-2">
                      <Utensils className="h-4 w-4 text-orange-500" />
                      Menu Item Name *
                    </Label>
                    <Input
                      id="name"
                      placeholder="e.g., Grilled Salmon with Herbs"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      className="h-11 text-base"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-semibold">
                      Description *
                    </Label>
                    <Textarea
                      id="description"
                      placeholder="Describe your menu item in detail - ingredients, preparation method, taste profile..."
                      value={formData.description}
                      onChange={(e) => handleInputChange("description", e.target.value)}
                      className="min-h-[100px] text-base resize-none"
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      {formData.description.length}/500 characters
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Organization */}
            {currentStep === 2 && (
              <Card className="border-0 shadow-none">
                <CardContent className="p-0 space-y-6">
                  <div className="space-y-2">
                    <Label className="text-sm font-semibold flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-blue-500" />
                      Branch *
                    </Label>
                    {isLoadingBranch ? (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Loading branches...</span>
                      </div>
                    ) : branchError ? (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                        Error loading branches
                      </div>
                    ) : (
                      <Select value={formData.branch} onValueChange={(value) => handleInputChange("branch", value)}>
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select a branch" />
                        </SelectTrigger>
                        <SelectContent>
                          {branchData?.map((branch: any) => (
                            <SelectItem key={branch.id || branch._id} value={branch.id || branch._id}>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {branch.branch_code}
                                </Badge>
                                {branch.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-semibold flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-500" />
                      Menu Group *
                    </Label>
                    {isLoadingGroup ? (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Loading menu groups...</span>
                      </div>
                    ) : (
                      <Select value={formData.menu_group} onValueChange={(value) => {
                        handleInputChange("menu_group", value);
                        handleInputChange("menu_sub_group", ""); // Reset sub-group when group changes
                      }}>
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select a menu group" />
                        </SelectTrigger>
                        <SelectContent>
                          {MenuGroup?.data?.results?.map((group: any) => (
                            <SelectItem key={group.id} value={group.id.toString()}>
                              {group.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-semibold flex items-center gap-2">
                      <Tag className="h-4 w-4 text-purple-500" />
                      Menu Sub Group *
                    </Label>
                    {isLoadingMenuSubGroup ? (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Loading sub groups...</span>
                      </div>
                    ) : !formData.menu_group ? (
                      <div className="p-3 bg-muted rounded-lg text-muted-foreground text-sm">
                        Please select a menu group first
                      </div>
                    ) : (
                      <Select value={formData.menu_sub_group} onValueChange={(value) => handleInputChange("menu_sub_group", value)}>
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select a sub group" />
                        </SelectTrigger>
                        <SelectContent>
                          {getFilteredSubGroups().map((subGroup: any) => (
                            <SelectItem key={subGroup.id} value={subGroup.id.toString()}>
                              <div className="space-y-1">
                                <div className="font-medium">{subGroup.name}</div>
                                <div className="text-xs text-muted-foreground">{subGroup.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 3: Settings & Review */}
            {currentStep === 3 && (
              <Card className="border-0 shadow-none">
                <CardContent className="p-0 space-y-6">
                  <div className="space-y-4">
                    <Label className="text-sm font-semibold flex items-center gap-2">
                      <Clock className="h-4 w-4 text-indigo-500" />
                      Availability Settings
                    </Label>
                    
                    <div className="space-y-2">
                      <Label className="text-sm">Availability Type</Label>
                      <Select value={formData.availability_type} onValueChange={(value) => handleInputChange("availability_type", value)}>
                        <SelectTrigger className="h-11">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ALWAYS">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                              Always Available
                            </div>
                          </SelectItem>
                          <SelectItem value="SCHEDULED">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                              Scheduled Availability
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                      <div className="space-y-1">
                        <Label className="text-sm font-medium">Active Status</Label>
                        <p className="text-xs text-muted-foreground">
                          Enable this item for customer ordering
                        </p>
                      </div>
                      <Switch
                        checked={formData.is_active}
                        onCheckedChange={(checked) => handleInputChange("is_active", checked)}
                      />
                    </div>
                  </div>

                  <Separator />

                  {/* Review Section */}
                  <div className="space-y-4">
                    <Label className="text-sm font-semibold flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      Review Your Menu Item
                    </Label>
                    
                    <div className="p-4 bg-muted/30 rounded-lg space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Name</p>
                        <p className="font-semibold">{formData.name || "Not specified"}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Description</p>
                        <p className="text-sm">{formData.description || "Not specified"}</p>
                      </div>
                      <div className="flex gap-4">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Branch</p>
                          <p className="text-sm">{branchData?.find((b: any) => (b.id || b._id) === formData.branch)?.name || "Not selected"}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Group</p>
                          <p className="text-sm">{MenuGroup?.data?.results?.find((g: any) => g.id.toString() === formData.menu_group)?.name || "Not selected"}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </form>
        </ScrollArea>

        {/* Footer Actions */}
        <div className="border-t bg-muted/30 p-6">
          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="min-w-[100px]"
            >
              Previous
            </Button>
            
            <div className="flex gap-3">
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              {currentStep < totalSteps ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  className="min-w-[100px] bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                  disabled={
                    (currentStep === 1 && (!formData.name || !formData.description)) ||
                    (currentStep === 2 && (!formData.branch || !formData.menu_group || !formData.menu_sub_group))
                  }
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !isFormValid}
                  className="min-w-[100px] bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Create Item
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}