import { apiSlice } from "../apiSlice";
import { 
  <PERSON>r, 
  GETUser, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UsersQueryParams, 
  PaginatedUsersResponse 
} from "../../types/user";

export const userApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all users with pagination and search
    getUsers: builder.query<PaginatedUsersResponse, UsersQueryParams>({
      query: (params = {}) => ({
        url: "/users/users",
        method: "GET",
        params: params,
      }),
      transformResponse: (response: any) => {
        console.log('Raw API response for getUsers:', response);

        // Handle different response structures
        if (response && response.results && Array.isArray(response.results)) {
          // Standard paginated response
          return {
            count: response.count || response.results.length,
            next: response.next || null,
            previous: response.previous || null,
            results: response.results
          };
        } else if (Array.isArray(response)) {
          // Direct array response
          return {
            count: response.length,
            next: null,
            previous: null,
            results: response
          };
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return {
              count: response.data.length,
              next: null,
              previous: null,
              results: response.data
            };
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return {
              count: response.data.count || response.data.results.length,
              next: response.data.next || null,
              previous: response.data.previous || null,
              results: response.data.results
            };
          }
        }

        // Fallback
        console.warn('Unexpected API response structure:', response);
        return {
          count: 0,
          next: null,
          previous: null,
          results: []
        };
      },
      providesTags: ["Users"],
    }),

    retrieveUser: builder.query<GETUser, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Users", id }],
    }),

    getUserDetails: builder.query<GETUser, string>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Users", id }],
    }),

    addUser: builder.mutation<User, CreateUserRequest>({
      query: (payload) => ({
        url: "/users/registration",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Users"],
    }),

    patchUser: builder.mutation<User, { id: number; data: UpdateUserRequest }>({
      query: ({ id, data }) => ({
        url: `/users/users/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Users", id }, "Users"],
    }),

    // Delete user
    deleteUser: builder.mutation<void, number>({
      query: (id) => ({
        url: `/users/users/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Users"],
    }),

    // Search user by email to get employee number
    searchUserByEmail: builder.query<GETUser[], string>({
      query: (email) => ({
        url: "/users/users",
        method: "GET",
        params: { search: email, page_size: 1 },
      }),
      transformResponse: (response: any) => {
        // Handle different response structures
        if (Array.isArray(response)) {
          return response;
        } else if (response && response.results && Array.isArray(response.results)) {
          return response.results;
        } else if (response && response.data) {
          if (Array.isArray(response.data)) {
            return response.data;
          } else if (response.data.results && Array.isArray(response.data.results)) {
            return response.data.results;
          }
        }
        return [];
      },
    }),

    // Health check endpoint
    healthCheck: builder.query<any, void>({
      query: () => ({
        url: "/sys/healthz",
        method: "GET",
      }),
    }),
  }),
});

export const {
  useGetUsersQuery,
  useRetrieveUserQuery,
  useGetUserDetailsQuery,
  useSearchUserByEmailQuery,
  useAddUserMutation,
  usePatchUserMutation,
  useDeleteUserMutation,
  useHealthCheckQuery,

  useLazyGetUsersQuery,
  useLazyRetrieveUserQuery,
  useLazyGetUserDetailsQuery,
  useLazySearchUserByEmailQuery,
  useLazyHealthCheckQuery,
} = userApiSlice;
