import { apiSlice } from "../apiSlice";

// Types for Tax Rate API based on API specification
export interface TaxRate {
  id?: number;
  name: string;
  percentage: string; // decimal field
  is_active?: boolean;
  branch?: string;
}

export const taxRateApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tax rates
    getTaxRates: builder.query<TaxRate[], any>({
      query: (params) => ({
        url: "/setup/tax-rates",
        method: "GET",
        params: params,
      }),
      providesTags: ["TaxRates"],
    }),

    retrieveTaxRate: builder.query<TaxRate, string>({
      query: (id) => ({
        url: `/setup/tax-rates/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "TaxRates", id }],
    }),

    addTaxRate: builder.mutation<TaxRate, Partial<TaxRate>>({
      query: (payload) => ({
        url: "/setup/tax-rates",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["TaxRates"],
    }),

    patchTaxRate: builder.mutation<TaxRate, { id: string; data: Partial<TaxRate> }>({
      query: ({ id, data }) => ({
        url: `/setup/tax-rates/${id}`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "TaxRates", id }, "TaxRates"],
    }),

    deleteTaxRate: builder.mutation<void, string>({
      query: (id) => ({
        url: `/setup/tax-rates/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["TaxRates"],
    }),
  }),
});

export const {
  useGetTaxRatesQuery,
  useRetrieveTaxRateQuery,
  useAddTaxRateMutation,
  usePatchTaxRateMutation,
  useDeleteTaxRateMutation,

  useLazyGetTaxRatesQuery,
  useLazyRetrieveTaxRateQuery,
} = taxRateApiSlice;
