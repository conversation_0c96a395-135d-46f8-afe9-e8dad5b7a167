import { store } from '@/redux/store';
import { branchApiSlice } from '@/redux/slices/branches';
import { revenueCenterApiSlice } from '@/redux/slices/revenueCenters';
import { workstationApiSlice } from '@/redux/slices/workstations';

/**
 * Service for handling cascading activation/deactivation of branches, revenue centers, and workstations
 */

export interface CascadingActivationResult {
  success: boolean;
  message: string;
  affectedEntities: {
    branches: number;
    revenueCenters: number;
    workstations: number;
  };
  errors?: string[];
}

/**
 * Activates or deactivates a branch and all its related entities
 */
export const cascadeBranchActivation = async (
  branchId: string,
  isActive: boolean
): Promise<CascadingActivationResult> => {
  const result: CascadingActivationResult = {
    success: false,
    message: '',
    affectedEntities: {
      branches: 0,
      revenueCenters: 0,
      workstations: 0
    },
    errors: []
  };

  try {
    // First, update the branch
    const branchResponse = await store.dispatch(
      branchApiSlice.endpoints.patchBranch.initiate({
        id: branchId,
        data: { is_active: isActive }
      })
    );

    if (branchResponse.error) {
      result.errors?.push(`Failed to update branch: ${branchResponse.error}`);
      return result;
    }

    result.affectedEntities.branches = 1;

    // Get all revenue centers for this branch
    const revenueCentersResponse = await store.dispatch(
      revenueCenterApiSlice.endpoints.getRevenueCenters.initiate({})
    );

    if (revenueCentersResponse.data) {
      const branchRevenueCenters = revenueCentersResponse.data.filter(
        rc => rc.branch === branchId || rc.branch === branchResponse.data?.branch_code
      );

      // Update all revenue centers for this branch
      for (const rc of branchRevenueCenters) {
        if (rc.id) {
          try {
            await store.dispatch(
              revenueCenterApiSlice.endpoints.patchRevenueCenter.initiate({
                id: rc.id.toString(),
                data: { is_active: isActive }
              })
            );
            result.affectedEntities.revenueCenters++;
          } catch (error) {
            result.errors?.push(`Failed to update revenue center ${rc.name}: ${error}`);
          }
        }
      }
    }

    // Get all workstations for this branch
    const workstationsResponse = await store.dispatch(
      workstationApiSlice.endpoints.getWorkstations.initiate({})
    );

    if (workstationsResponse.data) {
      const branchWorkstations = workstationsResponse.data.filter(
        ws => ws.branch === branchId || ws.branch === branchResponse.data?.branch_code
      );

      // Update all workstations for this branch
      for (const ws of branchWorkstations) {
        if (ws.id) {
          try {
            await store.dispatch(
              workstationApiSlice.endpoints.patchWorkstation.initiate({
                id: ws.id.toString(),
                data: { is_active: isActive }
              })
            );
            result.affectedEntities.workstations++;
          } catch (error) {
            result.errors?.push(`Failed to update workstation ${ws.name}: ${error}`);
          }
        }
      }
    }

    result.success = result.errors?.length === 0;
    result.message = isActive 
      ? `Successfully activated branch and ${result.affectedEntities.revenueCenters} revenue centers, ${result.affectedEntities.workstations} workstations`
      : `Successfully deactivated branch and ${result.affectedEntities.revenueCenters} revenue centers, ${result.affectedEntities.workstations} workstations`;

    return result;

  } catch (error) {
    result.errors?.push(`Unexpected error: ${error}`);
    result.message = `Failed to ${isActive ? 'activate' : 'deactivate'} branch`;
    return result;
  }
};

/**
 * Activates or deactivates a revenue center and all its workstations
 */
export const cascadeRevenueCenterActivation = async (
  revenueCenterId: string,
  isActive: boolean
): Promise<CascadingActivationResult> => {
  const result: CascadingActivationResult = {
    success: false,
    message: '',
    affectedEntities: {
      branches: 0,
      revenueCenters: 0,
      workstations: 0
    },
    errors: []
  };

  try {
    // First, update the revenue center
    const rcResponse = await store.dispatch(
      revenueCenterApiSlice.endpoints.patchRevenueCenter.initiate({
        id: revenueCenterId,
        data: { is_active: isActive }
      })
    );

    if (rcResponse.error) {
      result.errors?.push(`Failed to update revenue center: ${rcResponse.error}`);
      return result;
    }

    result.affectedEntities.revenueCenters = 1;

    // Get all workstations for this revenue center
    const workstationsResponse = await store.dispatch(
      workstationApiSlice.endpoints.getWorkstations.initiate({})
    );

    if (workstationsResponse.data) {
      const rcWorkstations = workstationsResponse.data.filter(
        ws => ws.revenue_center === revenueCenterId
      );

      // Update all workstations for this revenue center
      for (const ws of rcWorkstations) {
        if (ws.id) {
          try {
            await store.dispatch(
              workstationApiSlice.endpoints.patchWorkstation.initiate({
                id: ws.id.toString(),
                data: { is_active: isActive }
              })
            );
            result.affectedEntities.workstations++;
          } catch (error) {
            result.errors?.push(`Failed to update workstation ${ws.name}: ${error}`);
          }
        }
      }
    }

    result.success = result.errors?.length === 0;
    result.message = isActive 
      ? `Successfully activated revenue center and ${result.affectedEntities.workstations} workstations`
      : `Successfully deactivated revenue center and ${result.affectedEntities.workstations} workstations`;

    return result;

  } catch (error) {
    result.errors?.push(`Unexpected error: ${error}`);
    result.message = `Failed to ${isActive ? 'activate' : 'deactivate'} revenue center`;
    return result;
  }
};
