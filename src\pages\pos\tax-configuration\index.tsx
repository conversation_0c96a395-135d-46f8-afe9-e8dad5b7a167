import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Calculator,
  Percent,
  Archive,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { TaxClass, TaxRate } from '@/types/pos';
import {
  useGetTaxClassesQuery,
  useDeleteTaxClassMutation
} from '@/redux/slices/taxClasses';
import {
  useGetTaxRatesQuery,
  useDeleteTaxRateMutation
} from '@/redux/slices/taxRates';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

const TaxConfiguration: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // API hooks
  const { data: taxClasses = [], isLoading: loadingTaxClasses, error: taxClassesError, refetch: refetchTaxClasses } = useGetTaxClassesQuery({});
  const { data: taxRates = [], isLoading: loadingTaxRates, error: taxRatesError, refetch: refetchTaxRates } = useGetTaxRatesQuery({});
  const [deleteTaxClass] = useDeleteTaxClassMutation();
  const [deleteTaxRate] = useDeleteTaxRateMutation();

  // Handle API errors
  useEffect(() => {
    if (taxClassesError) {
      handleApiError(taxClassesError, 'load tax classes');
    }
    if (taxRatesError) {
      handleApiError(taxRatesError, 'load tax rates');
    }
  }, [taxClassesError, taxRatesError]);

  const handleDeleteTaxClass = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this tax class?')) {
      try {
        await deleteTaxClass(id).unwrap();
        handleApiSuccess('Tax class deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete tax class');
      }
    }
  };

  const handleDeleteTaxRate = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this tax rate?')) {
      try {
        await deleteTaxRate(id).unwrap();
        handleApiSuccess('Tax rate deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete tax rate');
      }
    }
  };

  const handleRefresh = () => {
    console.log('Manually refreshing tax configuration data...');
    refetchTaxClasses();
    refetchTaxRates();
  };

  const taxClassColumns: ColumnDef<TaxClass>[] = [
    {
      accessorKey: 'name',
      header: 'Tax Class Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Calculator className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.description}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'rates',
      header: 'Tax Rates',
      cell: ({ row }) => {
        const rateIds = row.original.rates || [];
        const associatedRates = Array.isArray(taxRates) ? taxRates.filter(rate =>
          rateIds.includes(rate.id)
        ) : [];
        return (
          <div className="flex flex-wrap gap-1">
            {associatedRates.length > 0 ? (
              associatedRates.map((rate) => (
                <Badge key={rate.id} variant="outline" className="flex items-center space-x-1">
                  <Percent className="h-3 w-3" />
                  <span>{rate.rate || 0}%</span>
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground text-sm">No rates assigned</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'Branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branch = row.getValue('Branch') as string;
        return branch ? (
          <Badge variant="outline">
            {branch}
          </Badge>
        ) : (
          <span className="text-muted-foreground text-sm">All branches</span>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <Badge variant="default">Active</Badge>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const taxClass = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/tax-configuration/classes/${taxClass.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/tax-configuration/classes/${taxClass.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Tax Class
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Plus className="h-4 w-4 mr-2" />
                Add Tax Rate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                Preview Usage
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Archive className="h-4 w-4 mr-2" />
                Archive
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteTaxClass(taxClass.id?.toString() || '')}
              >
                Delete Tax Class
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  // Process tax rates with their associated tax class names
  const allTaxRates = Array.isArray(taxRates) ? taxRates.map(rate => {
    const associatedTaxClass = Array.isArray(taxClasses) ? taxClasses.find(tc =>
      tc.rates?.includes(rate.id)
    ) : null;
    return {
      ...rate,
      taxClassName: associatedTaxClass?.name || 'Unassigned'
    };
  }) : [];

  const taxRateColumns: ColumnDef<TaxRate & { taxClassName: string }>[] = [
    {
      accessorKey: 'name',
      header: 'Rate Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Percent className="h-4 w-4 text-muted-foreground" />
          <div className="font-medium">{row.getValue('name')}</div>
        </div>
      ),
    },
    {
      accessorKey: 'taxClassName',
      header: 'Tax Class',
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue('taxClassName')}</Badge>
      ),
    },
    {
      accessorKey: 'rate',
      header: 'Rate',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <span className="font-medium">{row.getValue('rate') || 0}%</span>
        </div>
      ),
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.getValue('is_active') as boolean;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const rate = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>
                <Edit className="h-4 w-4 mr-2" />
                Edit Rate
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Eye className="h-4 w-4 mr-2" />
                View Usage
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteTaxRate(rate.id?.toString() || '')}
              >
                <AlertTriangle className="h-4 w-4 mr-2" />
                Delete Rate
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredTaxClasses = Array.isArray(taxClasses) ? taxClasses.filter(tc =>
    tc.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tc.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  const filteredTaxRates = Array.isArray(allTaxRates) ? allTaxRates.filter(rate =>
    rate.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rate.taxClassName?.toLowerCase().includes(searchTerm.toLowerCase())
  ) : [];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Tax Configuration</h1>
          <p className="text-muted-foreground">
            Manage tax classes and rates for products and services
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loadingTaxClasses || loadingTaxRates}>
            🔄 Refresh
          </Button>
          <Link to="/pos/tax-configuration/classes/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Class
            </Button>
          </Link>
          <Link to="/pos/tax-configuration/rates/new">
            <Button variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Rate
            </Button>
          </Link>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find tax classes and rates by name or description
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tax classes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tax Configuration Tabs */}
      <Tabs defaultValue="classes" className="space-y-6">
        <TabsList>
          <TabsTrigger value="classes">Tax Classes</TabsTrigger>
          <TabsTrigger value="rates">Tax Rates</TabsTrigger>
          <TabsTrigger value="preview">Usage Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="classes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Classes ({filteredTaxClasses.length})</CardTitle>
              <CardDescription>
                Categories of tax that apply to different types of products or services
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingTaxClasses ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-muted-foreground">Loading tax classes...</div>
                </div>
              ) : taxClassesError ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-red-600">Error loading tax classes. Please try again.</div>
                </div>
              ) : (
                <DataTable
                  data={filteredTaxClasses}
                  columns={taxClassColumns}
                  enablePagination={true}
                  enableSorting={true}
                  enableColumnFilters={true}
                  enableSelectColumn={false}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Rates ({allTaxRates.length})</CardTitle>
              <CardDescription>
                Specific percentage rates assigned to tax classes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingTaxRates ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-muted-foreground">Loading tax rates...</div>
                </div>
              ) : taxRatesError ? (
                <div className="flex justify-center items-center h-32">
                  <div className="text-red-600">Error loading tax rates. Please try again.</div>
                </div>
              ) : (
                <DataTable
                  data={filteredTaxRates}
                  columns={taxRateColumns}
                  enablePagination={true}
                  enableSorting={true}
                  enableColumnFilters={true}
                  enableSelectColumn={false}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tax Usage Preview</CardTitle>
              <CardDescription>
                See which menu items, products, or revenue centers use each tax class
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTaxClasses.map((taxClass) => (
                  <div key={taxClass.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{taxClass.name}</h4>
                      <Badge variant="default">Active</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{taxClass.description}</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Default Rate:</span>
                        <div className="mt-1">
                          {taxClass.default_rate}
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Menu Items:</span>
                        <div className="mt-1">0 items (Not implemented)</div>
                      </div>
                      <div>
                        <span className="font-medium">Products:</span>
                        <div className="mt-1">0 products (Not implemented)</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Tax Classes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Array.isArray(taxClasses) ? taxClasses.length : 0}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Classes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Array.isArray(taxClasses) ? taxClasses.filter(tc => tc.isActive).length : 0}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Tax Rates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allTaxRates.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Average Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {allTaxRates.length > 0 
                ? (allTaxRates.reduce((sum, rate) => sum + rate.percentage, 0) / allTaxRates.length).toFixed(1)
                : 0}%
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default TaxConfiguration;
