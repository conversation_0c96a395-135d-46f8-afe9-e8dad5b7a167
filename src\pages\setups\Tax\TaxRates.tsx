import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { Screen } from "@/app-components/layout/screen";
import { Plus, Filter, Percent } from "lucide-react";
import { taxRateTypes } from "@/types/tax";
import { taxRatesTestData } from "./taxTestData";
import { searchDebouncer } from "@/utils/debouncers";
import AddTaxRate from "./modals/AddTaxRate";

const TaxRates = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedUoM, setSelectedUoM] = useState<taxRateTypes | null>(null);

  const handleViewDetails = (UoM: taxRateTypes) => {
    setSelectedUoM(UoM);
    setIsDetailModalOpen(true);
  };

  const handleEdit = (UoM: taxRateTypes) => {
    setSelectedUoM(UoM);
    setIsEditModalOpen(true);
  };

  const columns: ColumnDef<taxRateTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "percentage",
      header: "Percentage",
      cell: ({ row }) => {
        return (
          <Badge variant="outline" className="flex items-center space-x-1">
            <Percent className="h-3 w-3" />
            <span>{row.getValue("percentage")}%</span>
          </Badge>
        );
      },
    },
    {
      accessorKey: "branch",
      header: "Branch",
      cell: ({ row }) => {
        return row?.original?.branch ? (
          <span>{row?.original?.branch}</span>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
    },
    {
      accessorKey: "is_active",
      header: "Status",
      cell: ({ row }) => {
        const isActive = row.getValue("is_active");
        return (
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? "Active" : "Inactive"}
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewDetails(row.original)}
          >
            View
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
          >
            Edit
          </Button>
        </div>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Tax Rates</h1>
            <p className="text-muted-foreground">
              Manage tax rates for your business
            </p>
          </div>
          <Link to="/setups/tax/rates/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Tax Rate
            </Button>
          </Link>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center space-x-2">
          <div className="flex-1 max-w-sm">
            <Input
              placeholder="Search tax rates..."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              className="h-9"
            />
          </div>
          <Button variant="outline" size="sm" className="h-9">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>

        {/* Tax Rates Table */}
        <DataTable<taxRateTypes>
          // data={UoMs?.data?.results || []}
          data={taxRatesTestData || []}
          columns={columns}
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enableColumnFilters={true}
          enableSorting={true}
          enablePrintPdf={true}
          tableClassName="border-collapse"
          tHeadClassName="bg-gray-50"
          tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
          tBodyTrClassName="hover:bg-gray-50"
          tBodyCellsClassName="border-t"
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search suppliers..."
            />
          }
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          // totalItems={suppliers?.data?.total_data || 0}
          totalItems={taxRatesTestData.length || 0}
        />
      </div>

      {/* Modal Components */}
      {isAddModalOpen && (
        <AddTaxRate
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}

      {isEditModalOpen && (
        <AddTaxRate
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          updateData={selectedUoM!}
        />
      )}
    </Screen>
  );
};

export default TaxRates;
