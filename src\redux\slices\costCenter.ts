import { apiSlice } from "../apiSlice";

export const costCenterApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCostCenters: builder.query({
      query: (params) => ({
        url: "/setup/cost-centers",
        method: "GET",
        params: params,
      }),
      providesTags: ["CostCenters"],
    }),

    retrieveCostCenter: builder.query({
      query: (id) => ({
        url: `/setup/cost-centers/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "CostCenters", id }],
    }),

    addCostCenters: builder.mutation({
      query: (payload) => ({
        url: "/setup/cost-centers",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["CostCenters"],
    }),

    patchCostCenters: builder.mutation({
      query: (payload) => ({
        url: `/setup/cost-centers/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "CostCenters", id },
        "CostCenters",
      ],
    }),
  }),
});

export const {
  useGetCostCentersQuery,
  useRetrieveCostCenterQuery,
  useAddCostCentersMutation,
  usePatchCostCentersMutation,

  useLazyGetCostCentersQuery,
  useLazyRetrieveCostCenterQuery,
} = costCenterApiSlice;
