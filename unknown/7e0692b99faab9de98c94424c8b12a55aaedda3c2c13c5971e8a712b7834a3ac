import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Monitor,
  Building2,
  Store,
  Printer,
  Wifi,
  WifiOff
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Workstation, WorkstationRole } from '@/types/pos';
import { Screen } from '@/app-components/layout/screen';
import {
  useGetWorkstationsQuery,
  useDeleteWorkstationMutation
} from '@/redux/slices/workstations';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetRevenueCentersQuery } from '@/redux/slices/revenueCenters';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

const WorkstationManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');
  const [selectedType, setSelectedType] = useState('all');

  // API hooks
  const { data: workstations = [], isLoading: loadingWorkstations, error: workstationsError, refetch: refetchWorkstations } = useGetWorkstationsQuery({});
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: revenueCenters = [], isLoading: loadingRevenueCenters } = useGetRevenueCentersQuery({});
  const [deleteWorkstation] = useDeleteWorkstationMutation();

  // Handle API errors
  useEffect(() => {
    if (workstationsError) {
      handleApiError(workstationsError, 'load workstations');
    }
  }, [workstationsError]);

  const handleDeleteWorkstation = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this workstation?')) {
      try {
        await deleteWorkstation(id).unwrap();
        handleApiSuccess('Workstation deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete workstation');
      }
    }
  };

  const handleRefresh = () => {
    console.log('Manually refreshing workstations data...');
    refetchWorkstations();
  };

  const columns: ColumnDef<Workstation>[] = [
    {
      accessorKey: 'name',
      header: 'Workstation Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Monitor className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">{row.original.Workstation_code}</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => {
        const role = row.getValue('role') as string;
        const getRoleColor = (role: string) => {
          switch (role) {
            case 'POS': return 'bg-blue-100 text-blue-800';
            case 'Order Only': return 'bg-purple-100 text-purple-800';
            case 'self Service': return 'bg-pink-100 text-pink-800';
            case 'Kitchen dislay': return 'bg-orange-100 text-orange-800';
            case 'Bar': return 'bg-green-100 text-green-800';
            case 'Tablet': return 'bg-cyan-100 text-cyan-800';
            case 'Mobile': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };

        return (
          <Badge variant="outline" className={getRoleColor(role)}>
            {role || 'Unknown'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branchRef = row.original.branch;
        // Find branch by both branch_code and id for compatibility
        const branch = branches.find(b =>
          b.id?.toString() === branchRef?.toString() ||
          b.branch_code === branchRef
        );
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name || 'Unknown Branch'}</div>
              <div className="text-sm text-muted-foreground">{branch?.branch_code || branchRef}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'revenue_center',
      header: 'Revenue Center',
      cell: ({ row }) => {
        const rcRef = row.original.revenue_center;
        // Find revenue center by both revenue_center_code and id for compatibility
        const rc = revenueCenters.find(r =>
          r.id?.toString() === rcRef?.toString() ||
          r.revenue_center_code === rcRef
        );
        return rc ? (
          <div className="flex items-center space-x-2">
            <Store className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{rc.name}</div>
              <div className="text-sm text-muted-foreground">{rc.revenue_center_code || rc.id}</div>
            </div>
          </div>
        ) : rcRef ? (
          <span className="text-muted-foreground">Revenue Center {rcRef}</span>
        ) : (
          <span className="text-muted-foreground">Not assigned</span>
        );
      },
    },
    {
      accessorKey: 'ip_address',
      header: 'IP Address',
      cell: ({ row }) => {
        const ipAddress = row.getValue('ip_address') as string;
        const isOnline = row.original.is_online;
        return (
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="font-mono text-sm">{ipAddress}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'linked_printer',
      header: 'Printer',
      cell: ({ row }) => {
        const printerId = row.getValue('linked_printer') as string;
        return (
          <div className="flex items-center space-x-2">
            <Printer className="h-4 w-4 text-muted-foreground" />
            <span>{printerId ? '1' : '0'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'is_active',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.original.is_active ?? true;
        return (
          <Badge variant={isActive ? 'default' : 'secondary'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const workstation = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Configuration
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/workstations/${workstation.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Workstation
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Printer className="h-4 w-4 mr-2" />
                Manage Printers
              </DropdownMenuItem>
              <DropdownMenuItem>
                Reassign to Branch/RVC
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {workstation.is_active ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteWorkstation(workstation.id?.toString() || '')}
              >
                Delete Workstation
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredWorkstations = Array.isArray(workstations) ? workstations.filter(ws => {
    const matchesSearch = ws.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.hostname?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.ip_address?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ws.workstation_code?.toLowerCase().includes(searchTerm.toLowerCase());
    // Support filtering by both branch_code and id
    const matchesBranch = selectedBranch === 'all' ||
                         ws.branch?.toString() === selectedBranch ||
                         ws.branch === selectedBranch;
    const matchesType = selectedType === 'all' || ws.role === selectedType;
    return matchesSearch && matchesBranch && matchesType;
  }) : [];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workstation Management</h1>
          <p className="text-muted-foreground">
            Manage POS devices, terminals, and kitchen displays
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loadingWorkstations}>
            🔄 Refresh
          </Button>
          <Link to="/pos/workstations/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Workstation
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find workstations by name, IP address, or configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search workstations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {Array.isArray(branches) && branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id?.toString() || `branch-${branch.id}`}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="POS">POS</SelectItem>
                <SelectItem value="Order Only">Order Only</SelectItem>
                <SelectItem value="self Service">Self Service</SelectItem>
                <SelectItem value="Kitchen dislay">Kitchen Display</SelectItem>
                <SelectItem value="Bar">Bar</SelectItem>
                <SelectItem value="Tablet">Tablet</SelectItem>
                <SelectItem value="Mobile">Mobile</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Workstations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Workstations ({filteredWorkstations.length})</CardTitle>
          <CardDescription>
            POS devices and terminals across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingWorkstations ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-muted-foreground">Loading workstations...</div>
            </div>
          ) : workstationsError ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-red-600">Error loading workstations. Please try again.</div>
            </div>
          ) : (
            <DataTable
              data={filteredWorkstations}
              columns={columns}
              enablePagination={true}
              enableSorting={true}
              enableColumnFilters={true}
              enableSelectColumn={false}
            />
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Workstations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Array.isArray(workstations) ? workstations.length : 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Array.isArray(workstations) ? workstations.filter(ws => ws.is_active).length : 0}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">POS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(workstations) ? workstations.filter(ws => ws.role === 'POS').length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Tablets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(workstations) ? workstations.filter(ws => ws.role === 'Tablet').length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Kitchen Displays</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(workstations) ? workstations.filter(ws => ws.role === 'Kitchen dislay').length : 0}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default WorkstationManagement;
