import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Search,
  Save,
  RefreshCw,
  Info,
  Users,
  Shield,
  Plus,
  Edit,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Screen } from "@/app-components/layout/screen";

// Mock data for groups
const mockGroups = [
  {
    id: 1,
    name: "Administrators",
    description: "Full system access and administrative privileges",
    memberCount: 3,
  },
  {
    id: 2,
    name: "Telemarketers",
    description: "Telemarketing staff with prospect management access",
    memberCount: 15,
  },
  {
    id: 3,
    name: "Marketers",
    description: "Marketing team with campaign and content management",
    memberCount: 8,
  },
  {
    id: 4,
    name: "Digital",
    description: "Digital marketing specialists and content creators",
    memberCount: 5,
  },
];

// Mock data for permissions
const mockPermissions = [
  {
    id: 1,
    name: "create_users",
    displayName: "Create Users",
    description: "Allows creating new user accounts in the system",
    category: "User Management",
  },
  {
    id: 2,
    name: "edit_users",
    displayName: "Edit Users",
    description: "Allows editing existing user accounts and their details",
    category: "User Management",
  },
  {
    id: 3,
    name: "delete_users",
    displayName: "Delete Users",
    description: "Allows permanently deleting user accounts from the system",
    category: "User Management",
  },
  {
    id: 4,
    name: "view_prospects",
    displayName: "View Prospects",
    description: "Allows viewing prospect information and contact details",
    category: "Prospect Management",
  },
  {
    id: 5,
    name: "create_prospects",
    displayName: "Create Prospects",
    description: "Allows adding new prospects to the system",
    category: "Prospect Management",
  },
  {
    id: 6,
    name: "edit_prospects",
    displayName: "Edit Prospects",
    description: "Allows modifying existing prospect information",
    category: "Prospect Management",
  },
  {
    id: 7,
    name: "view_campaigns",
    displayName: "View Campaigns",
    description: "Allows viewing marketing campaigns and their performance",
    category: "Marketing",
  },
  {
    id: 8,
    name: "create_campaigns",
    displayName: "Create Campaigns",
    description: "Allows creating new marketing campaigns",
    category: "Marketing",
  },
  {
    id: 9,
    name: "view_analytics",
    displayName: "View Analytics",
    description: "Allows accessing analytics and reporting dashboards",
    category: "Analytics",
  },
  {
    id: 10,
    name: "create_content",
    displayName: "Create Content",
    description: "Allows creating and publishing marketing content",
    category: "Content Management",
  },
];

// Mock group permissions
const mockGroupPermissions = {
  1: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], // Administrators have all permissions
  2: [4, 5, 6], // Telemarketers have prospect permissions
  3: [7, 8, 9], // Marketers have campaign and analytics permissions
  4: [4, 7, 9, 10], // Digital specialists have view and content permissions
};

export default function GroupsPermissions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [groupPermissions, setGroupPermissions] = useState(mockGroupPermissions);
  const [hasChanges, setHasChanges] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<typeof mockPermissions[0] | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState({
    displayName: "",
    description: "",
    category: "",
  });

  const filteredGroups = mockGroups.filter((group) =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const categories = [...new Set(mockPermissions.map(p => p.category))];

  const handlePermissionChange = (groupId: number, permissionId: number, checked: boolean) => {
    setGroupPermissions(prev => {
      const groupPerms = prev[groupId] || [];
      const newPerms = checked
        ? [...groupPerms, permissionId]
        : groupPerms.filter(id => id !== permissionId);
      
      setHasChanges(true);
      return { ...prev, [groupId]: newPerms };
    });
  };

  const handleSelectAllGroups = (permissionId: number, checked: boolean) => {
    setGroupPermissions(prev => {
      const newPerms = { ...prev };
      filteredGroups.forEach(group => {
        const groupPerms = newPerms[group.id] || [];
        newPerms[group.id] = checked
          ? [...new Set([...groupPerms, permissionId])]
          : groupPerms.filter(id => id !== permissionId);
      });
      setHasChanges(true);
      return newPerms;
    });
  };

  const handleSaveChanges = () => {
    // Handle save logic here
    console.log("Saving group permissions:", groupPermissions);
    setHasChanges(false);
  };

  const handleReset = () => {
    setGroupPermissions(mockGroupPermissions);
    setHasChanges(false);
  };

  const hasPermission = (groupId: number, permissionId: number) => {
    return groupPermissions[groupId]?.includes(permissionId) || false;
  };

  const getPermissionCount = (permissionId: number) => {
    return filteredGroups.filter(group => hasPermission(group.id, permissionId)).length;
  };

  const handleEditPermission = (permission: typeof mockPermissions[0]) => {
    setEditingPermission({
      displayName: permission.displayName,
      description: permission.description,
      category: permission.category,
    });
    setSelectedPermission(permission);
    setIsEditModalOpen(true);
  };

  const handleUpdatePermission = () => {
    // Handle permission update logic here
    console.log("Updating permission:", selectedPermission?.id, editingPermission);
    setIsEditModalOpen(false);
    setSelectedPermission(null);
  };

  return (
    <Screen>
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/admin/users">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Users
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Groups Permissions</h1>
              <p className="text-muted-foreground">
                Manage permissions for user groups
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {hasChanges && (
              <Button variant="outline" onClick={handleReset}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Reset
              </Button>
            )}
            <Button onClick={handleSaveChanges} disabled={!hasChanges}>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockGroups.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockPermissions.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockGroups.reduce((sum, group) => sum + group.memberCount, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search groups..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Permissions by Category */}
        <div className="space-y-6">
          {categories.map((category) => {
            const categoryPermissions = mockPermissions.filter(p => p.category === category);
            
            return (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{category}</span>
                    <Badge variant="outline">{categoryPermissions.length} permissions</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {categoryPermissions.map((permission) => (
                      <div key={permission.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-help">
                                  <h4 className="font-medium">{permission.displayName}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {permission.description}
                                  </p>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">{permission.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditPermission(permission)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <div className="flex items-center space-x-1">
                              <Checkbox
                                checked={getPermissionCount(permission.id) === filteredGroups.length}
                                onCheckedChange={(checked) => 
                                  handleSelectAllGroups(permission.id, checked as boolean)
                                }
                              />
                              <span className="text-sm text-muted-foreground">
                                Select All ({getPermissionCount(permission.id)}/{filteredGroups.length})
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {filteredGroups.map((group) => (
                            <div key={group.id} className="flex items-center space-x-2">
                              <Checkbox
                                checked={hasPermission(group.id, permission.id)}
                                onCheckedChange={(checked) =>
                                  handlePermissionChange(group.id, permission.id, checked as boolean)
                                }
                              />
                              <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium truncate">{group.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {group.memberCount} members
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Edit Permission Modal */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Permission</DialogTitle>
              <DialogDescription>
                Update the permission details and description.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="edit-display-name">Display Name</Label>
                <Input
                  id="edit-display-name"
                  value={editingPermission.displayName}
                  onChange={(e) => setEditingPermission(prev => ({ ...prev, displayName: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">Category</Label>
                <Select
                  value={editingPermission.category}
                  onValueChange={(value) => setEditingPermission(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={editingPermission.description}
                  onChange={(e) => setEditingPermission(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdatePermission}>Update Permission</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
    </Screen>
  );
}