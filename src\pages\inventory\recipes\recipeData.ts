import { Recipe,RecipeCategory } from './recipe.type';

export const mockRecipeCategories: RecipeCategory[] = [
  { id: '1', name: 'Dressings', description: 'Salad dressings and vinaigrettes', type: 'Prep' },
  { id: '2', name: 'Sauces', description: 'Hot and cold sauces', type: 'Prep' },
  { id: '3', name: 'Marina<PERSON>', description: 'Meat and vegetable marinades', type: 'Prep' },
  { id: '4', name: 'Stocks & Broths', description: 'Base stocks and broths', type: 'Prep' },
  { id: '5', name: 'Appetizers', description: 'Starter dishes', type: 'Final' },
  { id: '6', name: 'Entrees', description: 'Main course dishes', type: 'Final' },
  { id: '7', name: 'Sides', description: 'Side dishes and accompaniments', type: 'Final' },
  { id: '8', name: 'Desserts', description: 'Sweet dishes and desserts', type: 'Final' },
  { id: '9', name: 'Bever<PERSON>', description: 'Prepared drinks and cocktails', type: 'Both' },
  { id: '10', name: 'Breakfast', description: 'Morning dishes', type: 'Final' }
];

export const mockRecipes: Recipe[] = [
  {
    id: '1',
    name: 'House Caesar Dressing',
    type: 'Prep',
    category: 'Dressings',
    portionSize: 2,
    portionUnit: 'oz',
    costPerPortion: 85,
    active: true,
    description: 'Classic Caesar dressing with anchovies',
    
    instructions: 'Blend all ingredients until smooth. Refrigerate for 2 hours before serving.',
    prepTime: 15,
    servings: 16,
    linkedMenuItems: ['Caesar Salad', 'Grilled Chicken Caesar'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '8',
        productName: 'Whole Milk',
        quantity: 0.5,
        unit: 'cup',
        cost: 0.54,
        notes: 'For creaminess'
      },
      {
        id: '2',
        productId: '3',
        productName: 'Organic Sugar',
        quantity: 1,
        unit: 'tbsp',
        cost: 0.08,
        notes: 'Balance acidity'
      }
    ]
  },
  {
    id: '2',
    name: 'Grilled Salmon with Herbs',
    type: 'Final',
    category: 'Entrees',
    portionSize: 6,
    portionUnit: 'oz',
    costPerPortion: 1875,
    active: true,
    description: 'Fresh Atlantic salmon grilled with herb butter',
    instructions: 'Season salmon, grill 4-5 minutes per side, finish with herb butter.',
    prepTime: 10,
    cookTime: 10,
    servings: 1,
    linkedMenuItems: ['Dinner Menu - Salmon Special'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '5',
        productName: 'Fresh Salmon Fillet',
        quantity: 6,
        unit: 'oz',
        cost: 15.99,
        notes: 'Main protein'
      },
      {
        id: '2',
        productId: '6',
        productName: 'Olive Oil Extra Virgin',
        quantity: 1,
        unit: 'tbsp',
        cost: 0.58,
        notes: 'For grilling'
      }
    ]
  },
  {
    id: '3',
    name: 'Spicy Buffalo Sauce',
    type: 'Prep',
    category: 'Sauces',
    portionSize: 1,
    portionUnit: 'oz',
    costPerPortion: 320,
    active: true,
    description: 'Classic buffalo wing sauce with cayenne and butter',
    instructions: 'Melt butter, whisk in hot sauce and seasonings. Keep warm.',
    prepTime: 5,
    cookTime: 5,
    servings: 32,
    linkedMenuItems: ['Buffalo Wings', 'Buffalo Chicken Sandwich'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '8',
        productName: 'Whole Milk',
        quantity: 0.25,
        unit: 'cup',
        cost: 0.27,
        notes: 'For butter substitute'
      }
    ]
  },
  {
    id: '4',
    name: 'Chocolate Brownie',
    type: 'Final',
    category: 'Desserts',
    portionSize: 1,
    portionUnit: 'piece',
    costPerPortion: 215,
    active: true,
    description: 'Rich chocolate brownie with walnuts',
    instructions: 'Mix dry ingredients, add wet ingredients, bake at 350°F for 25 minutes.',
    prepTime: 20,
    cookTime: 25,
    servings: 12,
    linkedMenuItems: ['Dessert Menu - Brownie'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '3',
        productName: 'Organic Sugar',
        quantity: 2,
        unit: 'cups',
        cost: 1.24,
        notes: 'Main sweetener'
      },
      {
        id: '2',
        productId: '8',
        productName: 'Whole Milk',
        quantity: 0.5,
        unit: 'cup',
        cost: 0.54,
        notes: 'For moisture'
      }
    ]
  },
  {
    id: '5',
    name: 'Vegetable Stock',
    type: 'Prep',
    category: 'Stocks & Broths',
    portionSize: 8,
    portionUnit: 'oz',
    costPerPortion: 450,
    active: true,
    description: 'Light vegetable stock for soups and sauces',
    instructions: 'Simmer vegetables for 2 hours, strain and cool.',
    prepTime: 30,
    cookTime: 120,
    servings: 16,
    linkedMenuItems: ['Vegetable Soup', 'Risotto'],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '1',
        productName: 'Bottled Water',
        quantity: 12,
        unit: 'cups',
        cost: 3.75,
        notes: 'Base liquid'
      }
    ]
  },
  {
    id: '6',
    name: 'Garlic Aioli',
    type: 'Prep',
    category: 'Sauces',
    portionSize: 1,
    portionUnit: 'tbsp',
    costPerPortion: 180,
    active: false,
    description: 'Creamy garlic mayonnaise',
    instructions: 'Blend garlic, egg yolk, slowly add oil while blending.',
    prepTime: 10,
    servings: 24,
    linkedMenuItems: [],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    ingredients: [
      {
        id: '1',
        productId: '6',
        productName: 'Olive Oil Extra Virgin',
        quantity: 0.75,
        unit: 'cup',
        cost: 3.47,
        notes: 'Main ingredient'
      }
    ]
  }
];