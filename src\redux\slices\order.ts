import { apiSlice } from "../apiSlice";

export const orderApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getOrders: builder.query({
      query: (params) => ({
        url: "/orders",
        method: "GET",
        params: params,
      }),
      providesTags: ["Orders"],
    }),

    retrieveOrder: builder.query({
      query: (id) => ({
        url: `/orders/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Orders", id }],
    }),

    addOrder: builder.mutation({
      query: (payload) => ({
        url: "/orders",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["Orders"],
    }),

    postOrder: builder.mutation({
      query: (payload) => ({
        url: "/order-line-items",
        method: "POST",
        body: payload,
      }),
    }),

    patchOrder: builder.mutation({
      query: ({ id, status }) => ({
        url: `/orders/${id}`, // Updated endpoint (adjust if API docs specify otherwise)
        method: "PATCH",
        body: { status }, // Send status in the body
      }),
      invalidatesTags: ["Orders"],
    }),

    deleteOrder: builder.mutation({
      query: (id) => ({
        url: `/orders/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Orders"],
    }),
  }),
});

export const {
  useGetOrdersQuery,
  useRetrieveOrderQuery,
  useAddOrderMutation,
  usePostOrderMutation,
  usePatchOrderMutation,
  useDeleteOrderMutation,

  useLazyGetOrdersQuery,
  useLazyRetrieveOrderQuery,
} = orderApiSlice;
