import { useState } from "react";
import { useParams, useNavi<PERSON>, Link } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetInvoiceQuery,
  useSubmitInvoiceMutation,
  useReviewInvoiceMutation,
  useApproveInvoiceMutation,
  useDeleteInvoiceMutation,
  useCreatePaymentVoucherMutation,
  useGetInvoiceAuditLogQuery,
} from "@/redux/slices/procurement";
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Download,
  Printer,
  RefreshCw,
  Send,
  DollarSign,
  Receipt,
  Eye,
  Clock,
  History
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { paymentVoucherIntegrationHooks } from "@/utils/paymentVoucherIntegration";

const InvoiceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [workflowDialogOpen, setWorkflowDialogOpen] = useState(false);
  const [workflowAction, setWorkflowAction] = useState<"submit" | "approve" | "reject">("submit");
  const [workflowNotes, setWorkflowNotes] = useState("");

  // Mock data
  const mockInvoice = {
    id: parseInt(id || "1"),
    invoice_number: "INV-2025-001",
    invoice_date: "2025-01-15",
    supplier: 1,
    supplier_name: "ABC Suppliers Ltd",
    grn: 1,
    grn_number: "GRN-2025-001",
    status: "Draft" as const,
    total_amount: "1500.00",
    currency: "USD",
    gl_account: 1,
    gl_account_name: "Food Supplies",
    created_by_name: "John Doe",
    created_at: "2025-01-15T10:00:00Z",
    notes: "Initial invoice for office supplies delivery",
    items: [
      {
        id: 1,
        invoice: 1,
        product: 1,
        product_name: "Office Paper",
        product_code: "OFF-001",
        quantity_invoiced: "100",
        unit_price: "15.00",
        total_price: "1500.00",
        variance_quantity: "0.00",
        variance_amount: "0.00",
        grn_quantity: "100",
        grn_unit_price: "15.00",
      },
    ]
  };

  const mockAuditLog = [
    {
      id: 1,
      action: "created",
      user: "John Doe",
      timestamp: "2025-01-15T10:00:00Z",
      changes: { status: "Draft", total_amount: "1500.00" },
      notes: "Invoice created from GRN-2025-001",
    },
  ];

  const invoice = mockInvoice;
  const auditLog = mockAuditLog;
  const isLoading = false;
  const error = null;
  const refetch = () => console.log("Refetch called");

  // Mock mutation states
  const [submitting, setSubmitting] = useState(false);
  const [reviewing, setReviewing] = useState(false);
  const [approving, setApproving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [creatingVoucher, setCreatingVoucher] = useState(false);

  const submitInvoice = async (id: number) => {
    setSubmitting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSubmitting(false);
    return { unwrap: () => Promise.resolve() };
  };

  const reviewInvoice = async (payload: any) => {
    setReviewing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setReviewing(false);
    return { unwrap: () => Promise.resolve() };
  };

  const approveInvoice = async (id: number) => {
    setApproving(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setApproving(false);
    return { unwrap: () => Promise.resolve() };
  };

  const deleteInvoice = async (id: number) => {
    setDeleting(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDeleting(false);
    return { unwrap: () => Promise.resolve() };
  };

  const createPaymentVoucher = async (id: number) => {
    setCreatingVoucher(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setCreatingVoucher(false);
    return { unwrap: () => Promise.resolve({ voucher_number: "PV-2025-001" }) };
  };

  // Handle workflow actions
  const handleWorkflowAction = async () => {
    if (!invoice) return;

    try {
      switch (workflowAction) {
        case "submit":
          await submitInvoice(invoice.id!);
          toast.success("Invoice submitted for review");
          break;
        case "approve":
          await approveInvoice(invoice.id!);
          toast.success("Invoice approved successfully");
          break;
        case "reject":
          await reviewInvoice({
            id: invoice.id!,
            action: "reject",
            notes: workflowNotes.trim() || undefined,
          });
          toast.success("Invoice rejected");
          break;
      }

      setWorkflowDialogOpen(false);
      setWorkflowNotes("");
      refetch();
    } catch (error: any) {
      toast.error("Failed to process workflow action");
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!invoice) return;

    if (window.confirm(`Are you sure you want to delete invoice ${invoice.invoice_number}?`)) {
      try {
        await deleteInvoice(invoice.id!);
        toast.success("Invoice deleted successfully");
        navigate("/procurement/invoices");
      } catch (error: any) {
        toast.error("Failed to delete invoice");
      }
    }
  };

  // Handle create payment voucher
  const handleCreateVoucher = async () => {
    if (!invoice) return;

    try {
      // Use the integration hook for validation and creation
      const result = await paymentVoucherIntegrationHooks.onCreatePaymentVoucher(invoice);

      // Also call the mock API endpoint
      const apiResult = await createPaymentVoucher(invoice.id!);
      const voucherResult = await apiResult.unwrap();

      toast.success(`Payment voucher ${voucherResult.voucher_number} created successfully`);
      refetch();
    } catch (error: any) {
      toast.error(error?.message || "Failed to create payment voucher");
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusConfig = (status: string) => {
      switch (status) {
        case "Approved":
          return {
            color: "bg-green-100 text-green-800 border-green-200",
            icon: CheckCircle,
          };
        case "Reviewed":
          return {
            color: "bg-blue-100 text-blue-800 border-blue-200",
            icon: Clock,
          };
        case "Draft":
          return {
            color: "bg-yellow-100 text-yellow-800 border-yellow-200",
            icon: Edit,
          };
        default:
          return {
            color: "bg-gray-100 text-gray-800 border-gray-200",
            icon: FileText,
          };
      }
    };

    const config = getStatusConfig(status);
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} border flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status}
      </Badge>
    );
  };

  // Calculate variance for items
  const calculateVariance = (invoiceQty: string, invoicePrice: string, grnQty?: string, grnPrice?: string) => {
    const invQty = parseFloat(invoiceQty);
    const invPrice = parseFloat(invoicePrice);
    const grnQuantity = parseFloat(grnQty || "0");
    const grnUnitPrice = parseFloat(grnPrice || "0");

    const qtyVariance = invQty - grnQuantity;
    const priceVariance = invPrice - grnUnitPrice;
    const amountVariance = (invQty * invPrice) - (grnQuantity * grnUnitPrice);

    return {
      qtyVariance,
      priceVariance,
      amountVariance,
      hasVariance: Math.abs(qtyVariance) > 0.01 || Math.abs(priceVariance) > 0.01
    };
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      </Screen>
    );
  }

  if (error || !invoice) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Failed to load invoice details</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/invoices")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Invoices
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <FileText className="h-8 w-8 text-blue-600" />
                {invoice.invoice_number}
              </h1>
              <p className="text-gray-600 mt-1">
                Purchase Invoice Details
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            
            {/* Workflow Actions */}
            {invoice.status === "Draft" && (
              <>
                <Button 
                  onClick={() => {
                    setWorkflowAction("submit");
                    setWorkflowDialogOpen(true);
                  }}
                  disabled={submitting}
                >
                  {submitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4 mr-2" />
                  )}
                  Submit for Review
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4 mr-2" />
                  )}
                  Delete
                </Button>
              </>
            )}

            {invoice.status === "Reviewed" && (
              <>
                <Button 
                  onClick={() => {
                    setWorkflowAction("approve");
                    setWorkflowDialogOpen(true);
                  }}
                  disabled={approving}
                >
                  {approving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Approve
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => {
                    setWorkflowAction("reject");
                    setWorkflowDialogOpen(true);
                  }}
                  disabled={reviewing}
                >
                  {reviewing ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2" />
                  )}
                  Reject
                </Button>
              </>
            )}

            {invoice.status === "Approved" && !invoice.payment_voucher && (
              <Button 
                onClick={handleCreateVoucher}
                disabled={creatingVoucher}
                className="bg-green-600 hover:bg-green-700"
              >
                {creatingVoucher ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <DollarSign className="h-4 w-4 mr-2" />
                )}
                Create Payment Voucher
              </Button>
            )}
          </div>
        </div>

        {/* Invoice Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Invoice Number</Label>
                  <p className="font-semibold">{invoice.invoice_number}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Status</Label>
                  <div className="mt-1">
                    <StatusBadge status={invoice.status} />
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Invoice Date</Label>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-600" />
                    {new Date(invoice.invoice_date).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Total Amount</Label>
                  <p className="flex items-center gap-2 font-semibold text-lg">
                    <DollarSign className="h-4 w-4 text-green-600" />
                    {invoice.currency} {parseFloat(invoice.total_amount).toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                GRN & Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">GRN Number</Label>
                  <Link 
                    to={`/procurement/grns/${invoice.grn}`}
                    className="font-semibold text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-2"
                  >
                    <Receipt className="h-4 w-4" />
                    {invoice.grn_number || `GRN-${invoice.grn}`}
                  </Link>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">Supplier</Label>
                  <p className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-gray-600" />
                    {invoice.supplier_name || "N/A"}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-600">GL Account</Label>
                  <p className="font-medium">{invoice.gl_account_name || "N/A"}</p>
                </div>
                {invoice.payment_voucher && (
                  <div>
                    <Label className="text-sm font-medium text-gray-600">Payment Voucher</Label>
                    <p className="font-semibold text-green-600">
                      {invoice.payment_voucher_number || `PV-${invoice.payment_voucher}`}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Invoice Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Invoice Items
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => refetch()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {invoice.items && invoice.items.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Invoiced Qty</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Total Price</TableHead>
                    <TableHead>GRN Qty</TableHead>
                    <TableHead>Variance</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoice.items.map((item, index) => {
                    const variance = calculateVariance(
                      item.quantity_invoiced,
                      item.unit_price,
                      item.grn_quantity,
                      item.grn_unit_price
                    );
                    
                    return (
                      <TableRow key={item.id || index}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.product_name || `Product ${item.product}`}</p>
                            {item.product_code && (
                              <p className="text-sm text-gray-600">{item.product_code}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {parseFloat(item.quantity_invoiced).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          {invoice.currency} {parseFloat(item.unit_price).toFixed(2)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {invoice.currency} {parseFloat(item.total_price).toFixed(2)}
                        </TableCell>
                        <TableCell>
                          {item.grn_quantity ? parseFloat(item.grn_quantity).toLocaleString() : "N/A"}
                        </TableCell>
                        <TableCell>
                          {variance.hasVariance ? (
                            <div className="flex items-center gap-1">
                              <AlertTriangle className="h-4 w-4 text-yellow-600" />
                              <span className="text-sm text-yellow-700">
                                Qty: {variance.qtyVariance > 0 ? "+" : ""}{variance.qtyVariance.toFixed(2)}
                              </span>
                            </div>
                          ) : (
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Match
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                            Invoiced
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No items found for this invoice
              </div>
            )}
          </CardContent>
        </Card>

        {/* Audit Trail */}
        {auditLog && auditLog.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Audit Trail
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {auditLog.map((log, index) => (
                  <div key={log.id || index} className="flex items-start gap-3 p-3 border rounded-lg">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium">{log.user}</p>
                        <p className="text-sm text-gray-600">
                          {new Date(log.timestamp).toLocaleString()}
                        </p>
                      </div>
                      <p className="text-sm text-gray-700 capitalize">{log.action}</p>
                      {log.notes && (
                        <p className="text-sm text-gray-600 mt-1">{log.notes}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Notes Section */}
        {invoice.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">{invoice.notes}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Workflow Action Dialog */}
      <Dialog open={workflowDialogOpen} onOpenChange={setWorkflowDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {workflowAction === "submit" && "Submit Invoice for Review"}
              {workflowAction === "approve" && "Approve Invoice"}
              {workflowAction === "reject" && "Reject Invoice"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="notes">
                {workflowAction === "reject" ? "Rejection Reason *" : "Notes (Optional)"}
              </Label>
              <Textarea
                id="notes"
                placeholder={
                  workflowAction === "reject" 
                    ? "Please provide a reason for rejection..." 
                    : "Add any notes about this action..."
                }
                value={workflowNotes}
                onChange={(e) => setWorkflowNotes(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setWorkflowDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleWorkflowAction} 
              disabled={submitting || reviewing || approving || (workflowAction === "reject" && !workflowNotes.trim())}
            >
              {(submitting || reviewing || approving) ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  {workflowAction === "submit" && <Send className="mr-2 h-4 w-4" />}
                  {workflowAction === "approve" && <CheckCircle className="mr-2 h-4 w-4" />}
                  {workflowAction === "reject" && <XCircle className="mr-2 h-4 w-4" />}
                  {workflowAction === "submit" && "Submit"}
                  {workflowAction === "approve" && "Approve"}
                  {workflowAction === "reject" && "Reject"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Screen>
  );
};

export default InvoiceDetail;
