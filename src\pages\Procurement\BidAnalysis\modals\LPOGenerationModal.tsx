import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Package, 
  Building,
  DollarSign,
  Loader2,
  FileText,
  Users,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { BidAnalysis, LPOGenerationData } from "@/types/procurement";
import { useGenerateLPOFromBidAnalysisMutation } from "@/redux/slices/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface LPOGenerationModalProps {
  open: boolean;
  onClose: () => void;
  bidAnalysis: BidAnalysis;
}

const LPOGenerationModal = ({ 
  open, 
  onClose, 
  bidAnalysis 
}: LPOGenerationModalProps) => {
  const [formData, setFormData] = useState<LPOGenerationData>({
    bid_analysis_id: bidAnalysis.id!,
    group_by_supplier: true,
    delivery_terms: "",
    payment_terms: "",
    notes: "",
    generate_separate_pos: true,
  });

  const [generateLPO, { isLoading: generating }] = useGenerateLPOFromBidAnalysisMutation();

  const resetForm = () => {
    setFormData({
      bid_analysis_id: bidAnalysis.id!,
      group_by_supplier: true,
      delivery_terms: "",
      payment_terms: "",
      notes: "",
      generate_separate_pos: true,
    });
  };

  const handleInputChange = (field: keyof LPOGenerationData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const getSupplierSummary = () => {
    const supplierMap = new Map();
    
    bidAnalysis.items?.forEach(item => {
      if (item.selected_supplier) {
        if (!supplierMap.has(item.selected_supplier)) {
          supplierMap.set(item.selected_supplier, {
            id: item.selected_supplier,
            name: item.selected_supplier_name,
            items: [],
            totalValue: 0,
            itemCount: 0
          });
        }
        
        const supplier = supplierMap.get(item.selected_supplier);
        supplier.items.push(item);
        supplier.totalValue += item.selected_total_price || 0;
        supplier.itemCount++;
      }
    });

    return Array.from(supplierMap.values());
  };

  const handleSubmit = async () => {
    const selectedItemsCount = bidAnalysis.items?.filter(item => item.selected_supplier).length || 0;
    const totalItemsCount = bidAnalysis.items?.length || 0;

    if (selectedItemsCount === 0) {
      toast.error("No suppliers have been selected for any items");
      return;
    }

    if (selectedItemsCount < totalItemsCount) {
      const proceed = window.confirm(
        `Only ${selectedItemsCount} out of ${totalItemsCount} items have selected suppliers. Do you want to proceed with generating LPOs for the selected items only?`
      );
      if (!proceed) return;
    }

    try {
      const result = await generateLPO({
        id: bidAnalysis.id!,
        lpo_data: formData
      }).unwrap();

      toast.success(`${result.purchase_orders_created?.length || 1} Purchase Order(s) generated successfully`);
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to generate Purchase Orders");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const supplierSummary = getSupplierSummary();
  const totalValue = supplierSummary.reduce((sum, supplier) => sum + supplier.totalValue, 0);
  const selectedItemsCount = bidAnalysis.items?.filter(item => item.selected_supplier).length || 0;
  const totalItemsCount = bidAnalysis.items?.length || 0;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Generate Purchase Orders (LPO)
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Generation Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{supplierSummary.length}</div>
                  <div className="text-sm text-gray-600">Suppliers</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{selectedItemsCount}</div>
                  <div className="text-sm text-gray-600">Items Selected</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    ${totalValue.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Total Value</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {formData.generate_separate_pos ? supplierSummary.length : 1}
                  </div>
                  <div className="text-sm text-gray-600">POs to Generate</div>
                </div>
              </div>

              {selectedItemsCount < totalItemsCount && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="text-yellow-800 text-sm">
                    {totalItemsCount - selectedItemsCount} item(s) don't have selected suppliers and will be skipped
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Generation Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Generation Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="group-by-supplier"
                  checked={formData.group_by_supplier}
                  onCheckedChange={(checked) => handleInputChange("group_by_supplier", checked)}
                />
                <Label htmlFor="group-by-supplier">
                  Group items by supplier
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="separate-pos"
                  checked={formData.generate_separate_pos}
                  onCheckedChange={(checked) => handleInputChange("generate_separate_pos", checked)}
                />
                <Label htmlFor="separate-pos">
                  Generate separate Purchase Orders for each supplier
                </Label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="delivery-terms">Delivery Terms</Label>
                  <Select
                    value={formData.delivery_terms}
                    onValueChange={(value) => handleInputChange("delivery_terms", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select delivery terms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="FOB Origin">FOB Origin</SelectItem>
                      <SelectItem value="FOB Destination">FOB Destination</SelectItem>
                      <SelectItem value="CIF">CIF (Cost, Insurance, Freight)</SelectItem>
                      <SelectItem value="DDP">DDP (Delivered Duty Paid)</SelectItem>
                      <SelectItem value="EXW">EXW (Ex Works)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="payment-terms">Payment Terms</Label>
                  <Select
                    value={formData.payment_terms}
                    onValueChange={(value) => handleInputChange("payment_terms", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment terms" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Net 30">Net 30 days</SelectItem>
                      <SelectItem value="Net 60">Net 60 days</SelectItem>
                      <SelectItem value="Net 90">Net 90 days</SelectItem>
                      <SelectItem value="2/10 Net 30">2/10 Net 30</SelectItem>
                      <SelectItem value="COD">Cash on Delivery</SelectItem>
                      <SelectItem value="Advance Payment">Advance Payment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supplier Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Supplier Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total Value</TableHead>
                    <TableHead>PO Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {supplierSummary.map((supplier) => (
                    <TableRow key={supplier.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{supplier.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {supplier.itemCount} item(s)
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 font-medium">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                          {supplier.totalValue.toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 flex items-center gap-1 w-fit">
                          <CheckCircle className="h-3 w-3" />
                          Ready for PO
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">Notes for Purchase Orders</Label>
                <Textarea
                  id="notes"
                  placeholder="Add any additional notes or instructions for the generated Purchase Orders..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Preview Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">What will be generated?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.generate_separate_pos ? (
                  <div className="flex items-center gap-2 text-green-700">
                    <CheckCircle className="h-4 w-4" />
                    <span>{supplierSummary.length} separate Purchase Orders will be created (one per supplier)</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-blue-700">
                    <FileText className="h-4 w-4" />
                    <span>1 consolidated Purchase Order will be created with all suppliers</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2 text-gray-700">
                  <Users className="h-4 w-4" />
                  <span>All selected suppliers will be notified automatically</span>
                </div>
                
                <div className="flex items-center gap-2 text-gray-700">
                  <Package className="h-4 w-4" />
                  <span>Purchase Orders will include all selected items with agreed prices</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={generating || supplierSummary.length === 0}
          >
            {generating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating LPOs...
              </>
            ) : (
              <>
                <Package className="mr-2 h-4 w-4" />
                Generate {formData.generate_separate_pos ? supplierSummary.length : 1} Purchase Order(s)
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LPOGenerationModal;
