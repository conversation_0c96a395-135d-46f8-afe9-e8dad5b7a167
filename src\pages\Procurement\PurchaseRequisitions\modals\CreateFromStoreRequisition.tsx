import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, AlertTriangle } from "lucide-react";
import {
  useCreatePurchaseRequisitionMutation,
  useGetProcurementOfficersQuery,
  useGetSupplierCategoriesQuery,
} from "@/redux/slices/procurement";
import { PurchaseRequisitionFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface CreateFromStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  storeRequisition: any; // Store requisition data
}

const CreateFromStoreRequisition = ({ 
  isOpen, 
  onClose, 
  storeRequisition 
}: CreateFromStoreRequisitionProps) => {
  const [createPurchaseRequisition, { isLoading: creating }] = useCreatePurchaseRequisitionMutation();
  
  // Fetch supporting data
  const { data: procurementOfficers } = useGetProcurementOfficersQuery({});
  const { data: supplierCategories } = useGetSupplierCategoriesQuery({});

  const [formData, setFormData] = useState<PurchaseRequisitionFormData>({
    store_requisition: "",
    assigned_to: "",
    supplier_category: "",
    budget_code: "",
    required_date: "",
    priority: "",
    items: [],
  });

  // Initialize form data when store requisition is provided
  useEffect(() => {
    if (storeRequisition) {
      setFormData({
        store_requisition: storeRequisition.id,
        assigned_to: "",
        supplier_category: "",
        budget_code: "",
        required_date: storeRequisition.required_by || "",
        priority: "Medium",
        items: storeRequisition.items?.map((item: any) => ({
          product: item.product,
          quantity: item.quantity,
          unit_of_measure: item.unit_of_measure,
          estimated_unit_cost: "",
          specifications: item.remarks || "",
          preferred_supplier: "",
          justification: "",
        })) || [],
      });
    }
  }, [storeRequisition]);

  const handleInputChange = (field: keyof PurchaseRequisitionFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.supplier_category || !formData.budget_code || !formData.required_date || !formData.priority) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.items.some(item => !item.estimated_unit_cost)) {
      toast.error("Please provide estimated unit cost for all items");
      return;
    }

    try {
      const payload = {
        ...formData,
        items: formData.items.map(item => ({
          ...item,
          estimated_unit_cost: Number(item.estimated_unit_cost),
        })),
      };

      await createPurchaseRequisition(payload).unwrap();
      toast.success("Purchase requisition created successfully");
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create purchase requisition");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Purchase Requisition</DialogTitle>
          <p className="text-sm text-gray-600">
            Converting Store Requisition: {storeRequisition?.purpose}
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Purchase Requisition Details</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="assigned_to">Assign to Procurement Officer</Label>
                <Select
                  value={formData.assigned_to.toString()}
                  onValueChange={(value) => handleInputChange("assigned_to", Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select procurement officer" />
                  </SelectTrigger>
                  <SelectContent>
                    {procurementOfficers?.data?.results?.map((officer: any) => (
                      <SelectItem key={officer.id} value={officer.id.toString()}>
                        {officer.name} - {officer.department}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplier_category">Supplier Category *</Label>
                <Select
                  value={formData.supplier_category}
                  onValueChange={(value) => handleInputChange("supplier_category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier category" />
                  </SelectTrigger>
                  <SelectContent>
                    {supplierCategories?.data?.results?.map((category: any) => (
                      <SelectItem key={category.id} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="budget_code">Budget Code *</Label>
                <Input
                  id="budget_code"
                  value={formData.budget_code}
                  onChange={(e) => handleInputChange("budget_code", e.target.value)}
                  placeholder="Enter budget code"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="required_date">Required Date *</Label>
                <Input
                  id="required_date"
                  type="date"
                  value={formData.required_date}
                  onChange={(e) => handleInputChange("required_date", e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority *</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => handleInputChange("priority", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                        High
                      </div>
                    </SelectItem>
                    <SelectItem value="Urgent">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        Urgent
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Items from Store Requisition</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <h4 className="font-medium">Item {index + 1}</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Estimated Unit Cost *</Label>
                      <Input
                        type="number"
                        value={item.estimated_unit_cost}
                        onChange={(e) => handleItemChange(index, "estimated_unit_cost", e.target.value)}
                        placeholder="Enter estimated cost per unit"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Preferred Supplier</Label>
                      <Input
                        value={item.preferred_supplier}
                        onChange={(e) => handleItemChange(index, "preferred_supplier", e.target.value)}
                        placeholder="Enter preferred supplier (optional)"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Additional Specifications</Label>
                    <Textarea
                      value={item.specifications}
                      onChange={(e) => handleItemChange(index, "specifications", e.target.value)}
                      placeholder="Enter detailed specifications..."
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Justification</Label>
                    <Textarea
                      value={item.justification}
                      onChange={(e) => handleItemChange(index, "justification", e.target.value)}
                      placeholder="Justify why this item is needed..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Purchase Requisition
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateFromStoreRequisition;
