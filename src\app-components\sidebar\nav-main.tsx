import React from "react";
import { ChevronRight, type LucideIcon } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@/components/ui/sidebar";
import { Link } from "react-router-dom";

export type SubSubMenuItem = {
  title: string;
  url: string;
};

export type SubMenuItem = {
  title: string;
  url: string;
  isActive?: boolean;
  items?: SubSubMenuItem[];
};

export type MenuItem = {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: SubMenuItem[];
};

export function NavMain({
  label,
  items,
  open,
  onToggle,
}: {
  label: string;
  items: MenuItem[];
  open: boolean;
  onToggle: () => void;
}) {
  return (
    <SidebarGroup>
      <Collapsible open={open} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <SidebarGroupLabel className="flex items-center justify-between text-xs uppercase text-sidebar-foreground/70 px-4 py-3 cursor-pointer">
            {label}
            <ChevronRight
              className={`w-4 h-4 transition-transform duration-200 ${
                open ? "rotate-90" : ""
              }`}
            />
          </SidebarGroupLabel>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <SidebarMenu className="space-y-1 text-sm">
            {items.map((item) =>
              item.items && item.items.length > 0 ? (
                <TopLevelWithChildren key={item.title} item={item} />
              ) : (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    data-active={item.isActive}
                    className="flex items-center py-3 px-4 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:bg-sidebar-accent focus:text-sidebar-accent-foreground data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground"
                  >
                    <Link to={item.url} className="flex items-center">
                      {item.icon && (
                        <item.icon className="w-5 h-5 text-sidebar-foreground/70 shrink-0" />
                      )}
                      <span className="ml-3 truncate">{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )
            )}
          </SidebarMenu>
        </CollapsibleContent>
      </Collapsible>
    </SidebarGroup>
  );
}

function TopLevelWithChildren({ item }: { item: MenuItem }) {
  const [openMenu, setOpenMenu] = React.useState<string | null>(
    item.isActive ? item.title : null
  );
  const [openSub, setOpenSub] = React.useState<string | null>(
    item.items?.find((si) => si.isActive)?.title || null
  );

  return (
    <Collapsible
      asChild
      open={openMenu === item.title}
      onOpenChange={() =>
        setOpenMenu((prev) => (prev === item.title ? null : item.title))
      }
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton className="flex items-center py-3 px-4 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus:bg-sidebar-accent focus:text-sidebar-accent-foreground">
            {item.icon && (
              <item.icon className="w-5 h-5 text-sidebar-foreground/70 shrink-0" />
            )}
            <span className="ml-3 truncate">{item.title}</span>
            <ChevronRight className="ml-auto text-sidebar-foreground/70 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <SidebarMenuSub className="space-y-1 pl-8 py-1">
            {item.items!.map((sub) =>
              sub.items && sub.items.length > 0 ? (
                <SecondLevelWithChildren
                  key={sub.title}
                  subItem={sub}
                  isOpen={openSub === sub.title}
                  onOpenChange={(isOpen) =>
                    setOpenSub(isOpen ? sub.title : null)
                  }
                />
              ) : (
                <SidebarMenuSubItem key={sub.title}>
                  <SidebarMenuSubButton
                    asChild
                    className="block text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate"
                  >
                    <Link to={sub.url} className="block truncate">
                      {sub.title}
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              )
            )}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}

function SecondLevelWithChildren({
  subItem,
  isOpen,
  onOpenChange,
}: {
  subItem: SubMenuItem;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  return (
    <SidebarMenuSubItem>
      <Collapsible
        open={isOpen}
        onOpenChange={onOpenChange}
        className="w-full"
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate">
          <span>{subItem.title}</span>
          <ChevronRight
            className={`w-4 h-4 text-sidebar-foreground/70 transition-transform duration-200 ${
              isOpen ? "rotate-90" : ""
            }`}
          />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="space-y-1 pl-4 py-1">
            {subItem.items!.map((subSubItem) => (
              <Link
                key={subSubItem.title}
                to={subSubItem.url}
                className="block text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate"
              >
                {subSubItem.title}
              </Link>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </SidebarMenuSubItem>
  );
}
