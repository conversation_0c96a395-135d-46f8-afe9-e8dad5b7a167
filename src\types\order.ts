export interface orderTableTypes {
  id: number;
  table_number: string;
  capacity: number;
  is_active: boolean;
  created_at: string;
  modified_at: string;
  revenue_center: string;
}

export interface orderLineTypes {
  id: number;
  order: string;
  menu_item: string;
  quantity: string;
  unit_price: string;
  modifiers: string;
  special_instructions: string;
  line_total: string;
}

interface orderTypes {}
