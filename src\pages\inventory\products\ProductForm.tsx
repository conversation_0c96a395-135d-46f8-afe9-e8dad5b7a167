import React, { useState, useEffect } from 'react';
import { X, Save, RefreshCw } from 'lucide-react';
import { Product, ProductFormData, FormErrors, Category, UnitOfMeasure, Supplier } from './product.type';

interface ProductFormProps {
  product?: Product;
  categories: Category[];
  unitsOfMeasure: UnitOfMeasure[];
  suppliers: Supplier[];
  onSave: (productData: ProductFormData) => void;
  onCancel: () => void;
  isEditing?: boolean;
}

export const ProductForm: React.FC<ProductFormProps> = ({
  product,
  categories,
  unitsOfMeasure,
  suppliers,
  onSave,
  onCancel,
  isEditing = false
}) => {
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    code: '',
    category: '',
    unitOfMeasure: '',
    cost: '',
    taxCode: 'VAT',
    perishable: false,
    shelfLifeDays: '',
    expiryDate: '',
    defaultSupplier: '',
    active: true
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        code: product.code,
        category: product.category,
        unitOfMeasure: product.unitOfMeasure,
        cost: product.cost.toString(),
        taxCode: product.taxCode,
        perishable: product.perishable,
        shelfLifeDays: product.shelfLifeDays?.toString() || '',
        expiryDate: product.expiryDate || '',
        defaultSupplier: product.defaultSupplier || '',
        active: product.active
      });
    }
  }, [product]);

  const generateSKU = () => {
    setIsGeneratingCode(true);
    
    // Simulate API call delay
    setTimeout(() => {
      const categoryCode = categories.find(c => c.name === formData.category)?.name.substring(0, 3).toUpperCase() || 'GEN';
      const timestamp = Date.now().toString().slice(-4);
      const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
      const generatedCode = `${categoryCode}${timestamp}${randomNum}`;
      
      setFormData(prev => ({ ...prev, code: generatedCode }));
      setIsGeneratingCode(false);
    }, 1000);
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.unitOfMeasure) {
      newErrors.unitOfMeasure = 'Unit of measure is required';
    }

    if (!formData.cost || isNaN(parseFloat(formData.cost)) || parseFloat(formData.cost) < 0) {
      newErrors.cost = 'Valid cost is required';
    }

    if (formData.perishable && formData.shelfLifeDays && (isNaN(parseInt(formData.shelfLifeDays)) || parseInt(formData.shelfLifeDays) <= 0)) {
      newErrors.shelfLifeDays = 'Valid shelf life days required';
    }

    if (formData.perishable && formData.expiryDate) {
      const expiryDate = new Date(formData.expiryDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (expiryDate <= today) {
        newErrors.expiryDate = 'Expiry date must be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">
              {isEditing ? 'Edit Product' : 'Add New Product'}
            </h3>
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Product Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter product name"
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
          </div>

          {/* Product Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product Code (SKU)
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value)}
                className={`flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.code ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter or generate product code"
              />
              <button
                type="button"
                onClick={generateSKU}
                disabled={isGeneratingCode || !formData.category}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                <RefreshCw className={`w-4 h-4 ${isGeneratingCode ? 'animate-spin' : ''}`} />
                Generate
              </button>
            </div>
            {errors.code && <p className="mt-1 text-sm text-red-600">{errors.code}</p>}
            <p className="mt-1 text-xs text-gray-500">Select a category first to generate SKU</p>
          </div>

          {/* Category and Unit of Measure */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category *
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.category ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Unit of Measure *
              </label>
              <select
                value={formData.unitOfMeasure}
                onChange={(e) => handleInputChange('unitOfMeasure', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.unitOfMeasure ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select unit</option>
                {unitsOfMeasure.map(unit => (
                  <option key={unit.id} value={unit.name}>
                    {unit.name} ({unit.abbreviation})
                  </option>
                ))}
              </select>
              {errors.unitOfMeasure && <p className="mt-1 text-sm text-red-600">{errors.unitOfMeasure}</p>}
            </div>
          </div>

          {/* Cost and Tax Code */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cost per Unit * (Exclusive of Tax)
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.cost}
                  onChange={(e) => handleInputChange('cost', e.target.value)}
                  className={`w-full pl-8 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.cost ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="0.00"
                />
              </div>
              {errors.cost && <p className="mt-1 text-sm text-red-600">{errors.cost}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tax Code *
              </label>
              <select
                value={formData.taxCode}
                onChange={(e) => handleInputChange('taxCode', e.target.value as 'VAT' | 'No VAT')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="VAT">VAT</option>
                <option value="No VAT">No VAT</option>
              </select>
            </div>
          </div>

          {/* Perishable Toggle */}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="perishable"
                checked={formData.perishable}
                onChange={(e) => handleInputChange('perishable', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="perishable" className="ml-2 block text-sm text-gray-700">
                Perishable item
              </label>
            </div>
          </div>

          {/* Perishable Fields */}
          {formData.perishable && (
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="text-sm font-medium text-yellow-800 mb-3">Perishable Item Settings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Shelf Life (Days)
                  </label>
                  <input
                    type="number"
                    min="1"
                    value={formData.shelfLifeDays}
                    onChange={(e) => handleInputChange('shelfLifeDays', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.shelfLifeDays ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="e.g., 7"
                  />
                  {errors.shelfLifeDays && <p className="mt-1 text-sm text-red-600">{errors.shelfLifeDays}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expiry Date (Optional)
                  </label>
                  <input
                    type="date"
                    value={formData.expiryDate}
                    onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.expiryDate ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.expiryDate && <p className="mt-1 text-sm text-red-600">{errors.expiryDate}</p>}
                </div>
              </div>
            </div>
          )}

          {/* Default Supplier */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Default Supplier (Optional)
            </label>
            <select
              value={formData.defaultSupplier}
              onChange={(e) => handleInputChange('defaultSupplier', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select supplier</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.name}>
                  {supplier.name}
                </option>
              ))}
            </select>
          </div>

          {/* Status Toggle */}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                checked={formData.active}
                onChange={(e) => handleInputChange('active', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-700">
                Active status
              </label>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Inactive products will not appear in procurement and recipe selections
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              {isEditing ? 'Update Product' : 'Save Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};