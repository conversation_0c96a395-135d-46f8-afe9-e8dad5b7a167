import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Loader2, Bar<PERSON>hart } from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import { useCreateBidAnalysisFromRFQMutation } from "@/redux/slices/procurement";

interface CreateBidAnalysisProps {
  open: boolean;
  onClose: () => void;
  rfq: any;
  onSuccess?: () => void;
}

interface BidAnalysisFormData {
  code: string;
  split_award: boolean;
  recommendation_notes: string;
  selected_responses: string;
  finalized_at: string;
}

const CreateBidAnalysis: React.FC<CreateBidAnalysisProps> = ({
  open,
  onClose,
  rfq,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<BidAnalysisFormData>({
    code: `BA-${String(Date.now()).slice(-4)}`,
    split_award: false,
    recommendation_notes: "",
    selected_responses: "",
    finalized_at: "",
  });

  const [createBidAnalysisFromRFQ, { isLoading }] = useCreateBidAnalysisFromRFQMutation();

  const handleInputChange = (field: keyof BidAnalysisFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const resetForm = () => {
    setFormData({
      code: `BA-${String(Date.now()).slice(-4)}`,
      split_award: false,
      recommendation_notes: "",
      selected_responses: "",
      finalized_at: "",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.recommendation_notes.trim()) {
      toast.error("Recommendation notes are required");
      return;
    }

    try {
      const payload = {
        code: formData.code,
        rfq: rfq.code,
        created_by: "EMP-001", // You might want to get this from user context
        split_award: formData.split_award,
        recommendation_notes: formData.recommendation_notes,
        selected_responses: formData.selected_responses || null,
        finalized_at: formData.finalized_at || null,
      };

      await createBidAnalysisFromRFQ({ 
        rfq_id: rfq.id, 
        ...payload 
      }).unwrap();

      toast.success("Bid Analysis created successfully");
      resetForm();
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error creating bid analysis:", error);
      toast.error(error?.data?.message || "Failed to create bid analysis");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5" />
            Create Bid Analysis for {rfq?.code}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="code">Bid Analysis Code *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange("code", e.target.value)}
                placeholder="BA-0001"
                required
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="split_award"
                checked={formData.split_award}
                onCheckedChange={(checked) => handleInputChange("split_award", checked)}
              />
              <Label htmlFor="split_award">Split Award (Multiple Suppliers)</Label>
            </div>
          </div>

          {/* Recommendation Notes */}
          <div>
            <Label htmlFor="recommendation_notes">Recommendation Notes *</Label>
            <Textarea
              id="recommendation_notes"
              value={formData.recommendation_notes}
              onChange={(e) => handleInputChange("recommendation_notes", e.target.value)}
              placeholder="Enter your recommendation notes..."
              rows={4}
              required
            />
          </div>

          {/* Selected Responses (for single supplier) */}
          {!formData.split_award && (
            <div>
              <Label htmlFor="selected_responses">Selected Response Code</Label>
              <Input
                id="selected_responses"
                value={formData.selected_responses}
                onChange={(e) => handleInputChange("selected_responses", e.target.value)}
                placeholder="RESP-00001"
              />
              <p className="text-sm text-gray-500 mt-1">
                Leave empty to select later. Only used when not splitting award.
              </p>
            </div>
          )}

          {/* Finalized At */}
          <div>
            <Label htmlFor="finalized_at">Finalized At</Label>
            <Input
              id="finalized_at"
              type="datetime-local"
              value={formData.finalized_at}
              onChange={(e) => handleInputChange("finalized_at", e.target.value)}
            />
            <p className="text-sm text-gray-500 mt-1">
              Leave empty to finalize later.
            </p>
          </div>

          {/* Split Award Information */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Award Type Information</h4>
            {formData.split_award ? (
              <p className="text-sm text-blue-700">
                <strong>Split Award:</strong> You can award different items to different suppliers. 
                Individual supplier selection will be done in the Bid Analysis page.
              </p>
            ) : (
              <p className="text-sm text-blue-700">
                <strong>Single Award:</strong> All items will be awarded to one supplier. 
                You can select the winning response now or later.
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Bid Analysis
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateBidAnalysis;
