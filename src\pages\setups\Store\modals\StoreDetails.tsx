import { Edit } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { storeTypes } from "@/types/store";
import { Button } from "@/components/ui/button";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  store?: storeTypes;
}

const StoreDetails = ({ isOpen, onClose, store }: propTypes) => {
  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Store Details"
      description="View store details"
    >
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm text-gray-500">Name</h3>
            <p className="text-lg font-medium">{store?.name}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Code</h3>
            <p className="text-lg font-medium">{store?.code}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Location</h3>
            <p className="text-lg font-medium">{store?.location}</p>
          </div>
          <div>
            <h3 className="text-sm text-gray-500">Description</h3>
            <p className="text-lg font-medium">{store?.branch}</p>
          </div>
        </div>
        <div className="w-full flex justify-end gap-2 mt-6">
          <Button type="button" variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </BaseModal>
  );
};

export default StoreDetails;
