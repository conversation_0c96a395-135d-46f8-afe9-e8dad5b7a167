import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Loader, Save } from 'lucide-react';
import { useAddUserMutation } from '@/redux/slices/user';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { useGetUserRolesQuery } from '@/redux/slices/userRoles';
import { toast } from '@/components/custom/Toast/MyToast';

// Form schema for user creation
const userFormSchema = z.object({
  username: z.string().min(3, { message: "Username is required (min 3 chars)." }),
  employee_no: z.string().min(1, { message: "Employee number is required." }),
  first_name: z.string().min(2, { message: "First name is required." }),
  last_name: z.string().min(2, { message: "Last name is required." }),
  email: z.string().email({ message: "Invalid email address." }),
  password: z.string().min(6, { message: "Password is required (min 6 chars)." }),
  role: z.string().min(1, { message: "Role selection is required." }),
  branch: z.string().min(1, { message: "Branch selection is required." }),
  phone: z.string().optional(),
  pin: z.string().optional(),
});

type UserFormData = z.infer<typeof userFormSchema>;

export default function NewUser() {
  const navigate = useNavigate();
  const [createUser, { isLoading: isCreating }] = useAddUserMutation();
  const { data: branches = [], isLoading: loadingBranches } = useGetBranchesQuery({});
  const { data: userRoles = [], isLoading: loadingUserRoles } = useGetUserRolesQuery({});

  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      username: '',
      employee_no: '',
      first_name: '',
      last_name: '',
      email: '',
      password: '',
      role: '',
      branch: '',
      phone: '',
      pin: '',
    },
  });

  const onSubmit = async (data: UserFormData) => {
    try {
      const userPayload = {
        user: {
          username: data.username,
          employee_no: data.employee_no,
          first_name: data.first_name,
          last_name: data.last_name,
          email: data.email,
          password: data.password,
          role: parseInt(data.role),
          branch: parseInt(data.branch),
          phone: data.phone || null,
          pin: data.pin ? parseInt(data.pin) : null,
        }
      };

      await createUser(userPayload).unwrap();
      toast.success('User created successfully!');
      navigate('/admin/users');
    } catch (error: any) {
      const { data } = error || {};
      
      // Handle field-specific errors
      if (data && typeof data === 'object') {
        const fieldErrors = [];
        
        // Check for errors in user object
        if (data.user && typeof data.user === 'object') {
          const fields = ['username', 'employee_no', 'first_name', 'last_name', 'email', 'password', 'role', 'branch'];
          for (const field of fields) {
            if (data.user[field] && Array.isArray(data.user[field])) {
              fieldErrors.push(`${field.replace('_', ' ')}: ${data.user[field][0]}`);
            }
          }
        }
        
        if (fieldErrors.length > 0) {
          toast.error(fieldErrors[0]);
        } else {
          toast.error('Failed to create user. Please check your information and try again.');
        }
      } else {
        toast.error('Failed to create user. Please try again.');
      }
    }
  };

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/admin/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Create New User</h1>
            <p className="text-muted-foreground">Add a new user to the system</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter username" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employee_no"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employee Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter employee number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter email address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>PIN (Optional)</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter PIN" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Role and Branch Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="branch"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a branch" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingBranches ? (
                            <SelectItem value="loading" disabled>Loading branches...</SelectItem>
                          ) : Array.isArray(branches) ? (
                            branches.map((branch) => (
                              <SelectItem key={branch.id} value={branch.branch_code || ''}>
                                {branch.name} ({branch.branch_code})
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-branches" disabled>No branches available</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingUserRoles ? (
                            <SelectItem value="loading" disabled>Loading roles...</SelectItem>
                          ) : Array.isArray(userRoles) && userRoles.length > 0 ? (
                            userRoles.map((role) => (
                              <SelectItem key={role.id} value={role.id?.toString() || ''}>
                                {role.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="1">Default Role</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/users')}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? (
                <span className='flex items-center justify-center gap-2'>
                  <Loader className="animate-spin" size={22} />
                  Creating...
                </span>
              ) : (
                <span className='flex items-center justify-center gap-2'>
                  <Save size={22} />
                  Create User
                </span>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
