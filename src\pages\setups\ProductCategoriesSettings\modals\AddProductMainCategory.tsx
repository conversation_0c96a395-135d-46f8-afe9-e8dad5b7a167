import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  useAddProductMainCategoryMutation,
  usePatchProductMainCategoryMutation,
} from "@/redux/slices/productCategories";
import { ProductMainCategory } from "@/types/products";
import { Loader2, Send } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";

interface propTypes {
  isOpen: boolean;
  onClose: () => void;
  updateData?: ProductMainCategory;
}

const AddProductMainCategory = ({ isOpen, onClose, updateData }: propTypes) => {
  const [createCategory, { isLoading: loading }] =
    useAddProductMainCategoryMutation();
  const [updateCategory, { isLoading: loadingUpdate }] =
    usePatchProductMainCategoryMutation();

  const [formData, setFormData] = useState({
    name: updateData ? updateData.name : "",
    description: updateData ? updateData.description : "",
    cost_center: updateData ? updateData.cost_center : "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddCategory = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      if (updateData) {
        const response = await updateCategory({
          id: updateData.id,
          ...formData,
        }).unwrap();
        toast.success("Category updated successfully");
      } else {
        const response = await createCategory(formData).unwrap();
        toast.success("Category created successfully");
      }
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "An error occurred");
    }
  };

  return (
    <BaseModal
      size="lg"
      isOpen={isOpen}
      onOpenChange={onClose}
      title={updateData ? "Update Product Category" : "Add Product Category"}
      description="Enter product category details"
    >
      <form onSubmit={handleAddCategory}>
        <div className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="name">Name*</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter category name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter category description"
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose} type="button">
              Cancel
            </Button>
            {loading || loadingUpdate ? (
              <Button disabled>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </Button>
            ) : (
              <Button type="submit">
                {updateData ? "Update Store" : "Add Store"}
                <Send className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </form>
    </BaseModal>
  );
};

export default AddProductMainCategory;
