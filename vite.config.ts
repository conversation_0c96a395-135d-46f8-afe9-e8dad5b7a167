import path from "path";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

const host = process.env.VITE_HOST_URL;
const port = parseInt(process.env.VITE_HOST_PORT || "5181");

export default defineConfig({
  plugins: [react()],
  server: {
    host,
    port,
    allowedHosts: ["dev.pos.gmcplace.co.ke"],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  worker: {
    format: "es", 
  },
});
