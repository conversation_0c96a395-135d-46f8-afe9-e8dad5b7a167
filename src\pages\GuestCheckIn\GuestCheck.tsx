import React, { useState, useEffect, useMemo } from 'react';
import { AlertCircle, CheckCircle, DollarSign, Users } from 'lucide-react';
import { CheckInForm, GuestCheck } from './Components/types/types';
import { CheckDetailsDialog } from './Components/CheckDetailsModal';
import { CheckInDialog } from './Components/CheckInDialogue';

import { StatsCard } from './Components/StatsCard';
import { Screen } from '@/app-components/layout/screen';
import { useGetGuestChecksQuery, useAddGuestCheckMutation } from '@/redux/slices/guestCheck';
import { useGetUsersQuery } from '@/redux/slices/users'; // Changed to useGetUsersQuery
import { useGetTablesQuery } from '@/redux/slices/tables';
import { GuestChecksSearch } from './Components/SearchBar';
import { GuestChecksHeader } from './Components/GuestCheckHeader';
import { GuestChecksTabs } from './Components/GuestCheckTabs';

// User interface for type safety
interface User {
  id: string | number;
  username?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  role?: number | string;
}

// Table interface for type safety
interface Table {
  id: string | number;
  number?: string | number;
  name?: string;
  capacity?: number;
  status?: string;
  is_active?: boolean;
}

// Helper function to safely extract guest checks from API response
const extractGuestChecksFromResponse = (data: any): GuestCheck[] => {
  if (!data) return [];

  // Handle paginated API response structure: { message: string, data: { results: [], ... } }
  if (data.data && Array.isArray(data.data.results)) {
    return data.data.results;
  }

  // Handle array of guest checks
  if (Array.isArray(data)) {
    return data;
  }

  // Handle other common response patterns
  if (data.results && Array.isArray(data.results)) {
    return data.results;
  }

  if (data.checks && Array.isArray(data.checks)) {
    return data.checks;
  }

  return [];
};

// Helper function to extract users from API response
const extractUsersFromResponse = (data: any): User[] => {
  if (!data) return [];

  // Handle paginated API response: { data: { results: [...] } }
  if (data.data && Array.isArray(data.data.results)) {
    return data.data.results;
  }

  // Handle array of users
  if (Array.isArray(data)) {
    return data;
  }

  // Handle other response patterns
  if (data.results && Array.isArray(data.results)) {
    return data.results;
  }

  if (data.users && Array.isArray(data.users)) {
    return data.users;
  }

  return [];
};

// Helper function to extract tables from API response
const extractTablesFromResponse = (data: any): Table[] => {
  if (!data) return [];

  if (data.data && Array.isArray(data.data.results)) {
    return data.data.results;
  }

  if (Array.isArray(data)) {
    return data;
  }

  if (data.results && Array.isArray(data.results)) {
    return data.results;
  }

  return [];
};

const GuestChecksUI: React.FC = () => {
  // API hooks
  const { data: guestChecksData, isLoading, isError, error } = useGetGuestChecksQuery({});
  const { data: tablesData, isLoading: isLoadingTables, isError: isTablesError, error: tablesError } = useGetTablesQuery({});
  const {
    data: usersData,
    isLoading: isLoadingUsers,
    isError: isUsersError,
    error: usersError,
  } = useGetUsersQuery({}); // Changed from useGetUserQuery to useGetUsersQuery
  const [addGuestCheck] = useAddGuestCheckMutation();

  // State management
  const [activeTab, setActiveTab] = useState('active');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCheck, setSelectedCheck] = useState<GuestCheck | null>(null);
  const [showCheckInDialog, setShowCheckInDialog] = useState(false);
  const [showCheckDetails, setShowCheckDetails] = useState(false);
  const [checkInForm, setCheckInForm] = useState<CheckInForm>({
    tableNumber: '',
    guestCount: '',
    waiterName: '',
    specialRequests: '',
  });

  // Consolidated data processing with useMemo
  const processedData = useMemo(() => {
    // Process guest checks
    const guestChecks = extractGuestChecksFromResponse(guestChecksData);
    let guestCheckError: string | null = null;

    if (isError) {
      guestCheckError = error && 'status' in error
        ? `API Error ${error.status}: ${(error as any).data?.message || 'Unknown error'}`
        : (error as any)?.message || 'Network error occurred';
    } else if (guestChecks.length === 0 && !isLoading) {
      guestCheckError = 'No guest checks found in database';
    }

    // Process users
    const users = extractUsersFromResponse(usersData);
    let usersError: string | null = null;

    if (isUsersError) {
      usersError = usersError && 'status' in usersError
        ? `Users API Error ${(usersError as any).status}: ${(usersError as any).data?.message || 'Unknown error'}`
        : (usersError as any)?.message || 'Failed to load users';
    }

    // Process tables
    const tables = extractTablesFromResponse(tablesData);
    let tablesErrorMsg: string | null = null;

    if (isTablesError) {
      tablesErrorMsg = tablesError && 'status' in tablesError
        ? `Tables API Error ${(tablesError as any).status}: ${(tablesError as any).data?.message || 'Unknown error'}`
        : (tablesError as any)?.message || 'Failed to load tables';
    }

    return {
      guestChecks,
      users,
      tables,
      guestCheckError,
      usersError,
      tablesError: tablesErrorMsg,
    };
  }, [guestChecksData, isError, error, usersData, isUsersError, usersError, tablesData, isTablesError, tablesError]);

  // Debug logging
  useEffect(() => {
    console.log('Guest checks data:', guestChecksData);
    console.log('Users data:', usersData);
    console.log('Tables data:', tablesData);
    console.log('Processed guest checks:', processedData.guestChecks);
    console.log('Processed users:', processedData.users);
    console.log('Processed tables:', processedData.tables);
    if (processedData.guestCheckError) {
      console.error('Guest Check Query error:', processedData.guestCheckError);
    }
    if (processedData.usersError) {
      console.error('Users Query error:', processedData.usersError);
    }
    if (processedData.tablesError) {
      console.error('Tables Query error:', processedData.tablesError);
    }
  }, [processedData, guestChecksData, usersData, tablesData]);

  // Memoized filtered checks
  const filteredChecks = useMemo(() => {
    return processedData.guestChecks.filter((check) => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        check.id.toLowerCase().includes(searchLower) ||
        (check.tableNumber || check.table_number || '').toLowerCase().includes(searchLower) ||
        check.waiterName.toLowerCase().includes(searchLower)
      );
    });
  }, [processedData.guestChecks, searchTerm]);

  // Memoized check categorization
  const { activeChecks, closedChecks } = useMemo(() => ({
    activeChecks: filteredChecks.filter((check) => check.status === 'active'),
    closedChecks: filteredChecks.filter((check) => check.status === 'closed'),
  }), [filteredChecks]);

  // Memoized stats calculation
  const stats = useMemo(() => {
    const totalRevenue = closedChecks.reduce((sum, check) => sum + check.total, 0);
    const avgCheckSize = closedChecks.length > 0 ? totalRevenue / closedChecks.length : 0;

    return { totalRevenue, avgCheckSize };
  }, [closedChecks]);

  // Event handlers
  const handleCloseCheck = (checkId: string) => {
    const updatedChecks = processedData.guestChecks.map((check) =>
      check.id === checkId ? { ...check, status: 'closed' } : check
    );
    // Update via API if needed
    // addGuestCheck(updatedChecks); // Example, adjust based on API
  };

  const handleVoidItem = (checkId: string, itemIndex: number) => {
    const updatedChecks = processedData.guestChecks.map((check) => {
      if (check.id === checkId) {
        const voidedItem = check.items[itemIndex];
        return {
          ...check,
          items: check.items.filter((_, index) => index !== itemIndex),
          voids: [...check.voids, voidedItem],
        };
      }
      return check;
    });
    // Update via API if needed
    // addGuestCheck(updatedChecks); // Example, adjust based on API
  };

  const handleCheckIn = async () => {
    try {
      const newCheck: GuestCheck = {
        id: `CHK${String(processedData.guestChecks.length + 1).padStart(3, '0')}`,
        tableNumber: checkInForm.tableNumber,
        guestCount: parseInt(checkInForm.guestCount),
        waiterName: checkInForm.waiterName,
        orderTime: new Date().toISOString(),
        status: 'active',
        subtotal: 0,
        tax: 0,
        serviceCharge: 0,
        total: 0,
        items: [],
        discounts: [],
        voids: [],
      };
      // Call API to add new check
      await addGuestCheck(newCheck).unwrap();
      setCheckInForm({ tableNumber: '', guestCount: '', waiterName: '', specialRequests: '' });
      setShowCheckInDialog(false);
    } catch (error) {
      console.error('Failed to add guest check:', error);
    }
  };

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-white">
        <div className="w-full max-w-[1600px] mx-auto p-6">
          {/* Loading State */}
          {isLoading && (
            <div className="mb-4 p-4 bg-blue-100 border border-blue-300 rounded-md flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Loading guest checks...
            </div>
          )}

          {/* API Error Display */}
          {processedData.guestCheckError && (
            <div className="mb-4 p-4 bg-red-100 border border-red-300 rounded-md flex items-center">
              <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
              <span className="text-red-800">{processedData.guestCheckError}</span>
            </div>
          )}

          {/* Users API Error Display */}
          {processedData.usersError && (
            <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded-md flex items-center">
              <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
              <span className="text-yellow-800">
                Waiters loading issue: {processedData.usersError}. Using default waiter list in check-in dialog.
              </span>
            </div>
          )}

          {/* Tables API Error Display */}
          {processedData.tablesError && (
            <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded-md flex items-center">
              <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
              <span className="text-yellow-800">
                Tables loading issue: {processedData.tablesError}. Using default table list in check-in dialog.
              </span>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !isError && processedData.guestChecks.length === 0 && (
            <div className="mb-4 p-6 bg-gray-50 border border-gray-200 rounded-md text-center">
              <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-700 mb-1">No Guest Checks Found</h3>
              <p className="text-gray-500 mb-3">
                Your database is empty. Start by checking in your first guest!
              </p>
              <button
                onClick={() => setShowCheckInDialog(true)}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Check In First Guest
              </button>
            </div>
          )}

          <GuestChecksHeader onNewCheckIn={() => setShowCheckInDialog(true)} />
          <GuestChecksSearch searchTerm={searchTerm} setSearchTerm={setSearchTerm} />

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatsCard
              title="Active Checks"
              value={activeChecks.length}
              icon={<AlertCircle className="h-6 w-6 text-primary" />}
              trend="+12% from yesterday"
            />
            <StatsCard
              title="Closed Today"
              value={closedChecks.length}
              icon={<CheckCircle className="h-6 w-6 text-secondary" />}
              trend="+8% from yesterday"
            />
            <StatsCard
              title="Total Revenue"
              value={`$${stats.totalRevenue.toFixed(2)}`}
              icon={<DollarSign className="h-6 w-6 text-primary" />}
              trend="+15% from yesterday"
            />
            <StatsCard
              title="Avg Check Size"
              value={`$${stats.avgCheckSize.toFixed(2)}`}
              icon={<Users className="h-6 w-6 text-secondary" />}
              trend="+5% from yesterday"
            />
          </div>

          <GuestChecksTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            activeChecks={activeChecks}
            closedChecks={closedChecks}
            onCloseCheck={handleCloseCheck}
            onVoidItem={handleVoidItem}
            onSplitCheck={(check) => setSelectedCheck(check)}
            onViewCheck={(check) => {
              setSelectedCheck(check);
              setShowCheckDetails(true);
            }}
          />

          <CheckInDialog
            open={showCheckInDialog}
            onOpenChange={setShowCheckInDialog}
            checkInForm={checkInForm}
            setCheckInForm={setCheckInForm}
            onCheckIn={handleCheckIn}
            users={processedData.users}
            isLoadingUsers={isLoadingUsers}
          />

          <CheckDetailsDialog
            open={showCheckDetails}
            onOpenChange={setShowCheckDetails}
            selectedCheck={selectedCheck}
          />
        </div>
      </div>
    </Screen>
  );
};

export default GuestChecksUI;