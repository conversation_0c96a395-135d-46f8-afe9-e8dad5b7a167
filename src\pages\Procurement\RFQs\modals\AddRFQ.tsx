import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, Loader2, Package, Building, MapPin, Clock, X } from "lucide-react";
import {
  useCreateRFQMutation,
  useCreateRFQItemMutation,
  useGetPurchaseRequisitionsQuery,
  useGetSupplierCategoriesQuery,
  useGetProcurementProductsQ<PERSON>y,
  useGetProcurementUnitsOfMeasureQuery,
} from "@/redux/slices/procurement";
import { RFQFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddRFQProps {
  open: boolean;
  onClose: () => void;
}

const AddRFQ = ({ open, onClose }: AddRFQProps) => {
  const [createRFQ, { isLoading: creating }] = useCreateRFQMutation();
  const [createRFQItem] = useCreateRFQItemMutation();

  // Fetch supporting data
  const { data: purchaseRequisitions } = useGetPurchaseRequisitionsQuery({ status: "Approved" });
  const { data: supplierCategories } = useGetSupplierCategoriesQuery({});
  const { data: products } = useGetProcurementProductsQuery({});
  const { data: unitsOfMeasure } = useGetProcurementUnitsOfMeasureQuery({});

  const [formData, setFormData] = useState<RFQFormData>({
    rfq_number: "",
    response_deadline: "",
    requisition: "",
    created_by: "",
    supplier_category: "",
    status: "Open",
    items: [
      {
        quantity: "",
        product: "",
        unit_of_measure: "",
      },
    ],
  });

  const handleInputChange = (field: keyof RFQFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };
    handleInputChange("items", updatedItems);
  };

  const addItem = () => {
    const newItem = {
      quantity: "",
      product: "",
      unit_of_measure: "",
    };
    handleInputChange("items", [...formData.items, newItem]);
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      const updatedItems = formData.items.filter((_, i) => i !== index);
      handleInputChange("items", updatedItems);
    }
  };

  const resetForm = () => {
    setFormData({
      rfq_number: "",
      response_deadline: "",
      requisition: "",
      created_by: "",
      supplier_category: "",
      status: "Open",
      items: [
        {
          quantity: "",
          product: "",
          unit_of_measure: "",
        },
      ],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation - Check all required fields per API specification
    if (!formData.rfq_number.trim()) {
      toast.error("Please enter an RFQ number");
      return;
    }

    if (!formData.response_deadline) {
      toast.error("Please set a response deadline");
      return;
    }

    if (!formData.requisition) {
      toast.error("Please select a purchase requisition");
      return;
    }

    if (!formData.supplier_category) {
      toast.error("Please select a supplier category");
      return;
    }

    const validItems = formData.items.filter(
      (item) => item.product && item.quantity && item.unit_of_measure
    );

    if (validItems.length === 0) {
      toast.error("Please add at least one valid item with all required fields");
      return;
    }

    try {
      const payload = {
        rfq_number: formData.rfq_number.trim(),
        response_deadline: formData.response_deadline,
        requisition: Number(formData.requisition),
        created_by: Number(formData.created_by) || 1, // Default to current user
        supplier_category: Number(formData.supplier_category),
        status: formData.status || "Open",
      };

      const result = await createRFQ(payload).unwrap();

      // Create items separately if the RFQ was created successfully
      if (result.id && validItems.length > 0) {
        for (const item of validItems) {
          await createRFQItem({
            quantity: item.quantity,
            rfq: result.id,
            product: Number(item.product),
            unit_of_measure: Number(item.unit_of_measure),
          }).unwrap();
        }
      }

      toast.success("RFQ created successfully");
      resetForm();
      onClose();
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create RFQ");
    }
  };

  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Create Request for Quotation (RFQ)
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="rfq_number">RFQ Number *</Label>
                  <Input
                    id="rfq_number"
                    value={formData.rfq_number}
                    onChange={(e) => handleInputChange("rfq_number", e.target.value)}
                    placeholder="Enter RFQ number (max 100 characters)"
                    maxLength={100}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Unique RFQ number (required, max 100 characters)
                  </p>
                </div>

                <div>
                  <Label htmlFor="response_deadline">Response Deadline *</Label>
                  <Input
                    id="response_deadline"
                    type="datetime-local"
                    value={formData.response_deadline}
                    onChange={(e) => handleInputChange("response_deadline", e.target.value)}
                    min={new Date().toISOString().slice(0, 16)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="requisition">Purchase Requisition *</Label>
                  <Select
                    value={formData.requisition ? formData.requisition.toString() : ""}
                    onValueChange={(value) => handleInputChange("requisition", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select purchase requisition" />
                    </SelectTrigger>
                    <SelectContent>
                      {purchaseRequisitions?.results?.map((req: any) => (
                        <SelectItem key={req.id} value={req.id.toString()}>
                          {req.requisition_number} - {req.purpose}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="supplier_category">Supplier Category *</Label>
                  <Select
                    value={formData.supplier_category ? formData.supplier_category.toString() : ""}
                    onValueChange={(value) => handleInputChange("supplier_category", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select supplier category" />
                    </SelectTrigger>
                    <SelectContent>
                      {supplierCategories?.results?.map((category: any) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Package className="h-4 w-4" />
                RFQ Items
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <Label>Product *</Label>
                      <Select
                        value={item.product ? item.product.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "product", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.code}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label>Quantity *</Label>
                      <Input
                        placeholder="Enter quantity (decimal as string)"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Quantity as decimal string (required)
                      </p>
                    </div>

                    <div>
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure ? item.unit_of_measure.toString() : ""}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {unitsOfMeasure?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.symbol})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addItem}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create RFQ"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddRFQ;
