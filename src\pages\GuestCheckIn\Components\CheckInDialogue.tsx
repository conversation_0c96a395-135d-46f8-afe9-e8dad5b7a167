import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CheckInForm } from './types/types';
import { AlertCircle, Users, ShoppingCart } from 'lucide-react';
import { useGetTablesQuery } from '@/redux/slices/tables';
import { useGetUsersQuery } from '@/redux/slices/users';
import { useAddGuestCheckMutation } from '@/redux/slices/guestCheck';
import { useLazyGetOrdersQuery } from '@/redux/slices/order';

interface User {
  id: string | number;
  username?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  role?: number | string;
  employee_no?: string;
}

interface Table {
  id: string | number;
  number?: string | number;
  name?: string;
  capacity?: number;
  status?: string;
  is_active?: boolean;
}

interface Order {
  id: number;
  order_number: string;
  order_type: string;
  status: string;
  order_date: string;
  total_amount: string;
  payment_status: boolean;
  guest_count?: number;
  created_at: string;
  modified_at: string;
  tax_amount?: string;
  service_charge?: string;
  catering_levy?: string;
  revenue_center?: number;
  workstation?: number;
  table_number?: number;
  created_by: string;
}

interface CheckInDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  checkInForm: CheckInForm;
  setCheckInForm: React.Dispatch<React.SetStateAction<CheckInForm>>;
  onCheckIn: () => void;
  users?: User[];
  isLoadingUsers?: boolean;
}

interface GuestCheckPayload {
  guest_count: number;
  opened_at: string;
  closed_at?: string;
  status: string;
  payment_status: string;
  sub_total: string;
  tax_total: string;
  service_charge_total: string;
  discount_total: string;
  order?: number;
  table_number: number;
  employee: string;
  linked_checks: number[];
}

const fallbackWaiters: User[] = [
  { id: 'w1', first_name: 'Sarah', last_name: 'Johnson', role: 'waiter', employee_no: 'EMP001' },
  { id: 'w2', first_name: 'Mike', last_name: 'Chen', role: 'waiter', employee_no: 'EMP002' },
  { id: 'w3', first_name: 'Emma', last_name: 'Wilson', role: 'waiter', employee_no: 'EMP003' },
  { id: 'w4', first_name: 'James', last_name: 'Rodriguez', role: 'waiter', employee_no: 'EMP004' },
  { id: 'w5', first_name: 'Lisa', last_name: 'Thompson', role: 'waiter', employee_no: 'EMP005' },
];

const fallbackTables: Table[] = Array.from({ length: 20 }, (_, i) => ({
  id: `t${i + 1}`,
  number: i + 1,
  name: `Table ${i + 1}`,
  status: 'available',
}));

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError(error: any) {
    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('ErrorBoundary caught in CheckInDialog:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-600 bg-red-50 rounded">
          <AlertCircle className="h-4 w-4 inline mr-2" />
          Something went wrong. Please try again or contact support.
        </div>
      );
    }
    return this.props.children;
  }
}

const roleMap: { [key: number]: string } = {
  1: 'admin',
  2: 'manager',
  3: 'waiter',
  4: 'chef',
  5: 'cashier',
};

export const CheckInDialog: React.FC<CheckInDialogProps> = ({
  open,
  onOpenChange,
  checkInForm,
  setCheckInForm,
  onCheckIn,
  users = [],
  isLoadingUsers = false,
}) => {
  const { data: usersApiResponse, isLoading: loadingUsers, error: usersError } = useGetUsersQuery({});
  const { data: tablesApiResponse, isLoading: isLoadingTables, error: tablesError } = useGetTablesQuery({});
  const [getOrders, { data: ordersApiResponse, isLoading: isLoadingOrders, error: ordersError }] = useLazyGetOrdersQuery();
  const [addGuestCheck, { isLoading: isSubmitting }] = useAddGuestCheckMutation({});

  React.useEffect(() => {
    if (open) {
      getOrders({});
    }
  }, [open, getOrders]);

  const apiUsers = React.useMemo(() => {
    console.log('Full users API response:', usersApiResponse);
    if (!usersApiResponse) return [];
    if (usersApiResponse.data?.results) return usersApiResponse.data.results;
    if (usersApiResponse.results) return usersApiResponse.results;
    if (Array.isArray(usersApiResponse.data)) return usersApiResponse.data;
    if (Array.isArray(usersApiResponse)) return usersApiResponse;
    return [];
  }, [usersApiResponse]);

  const apiTables = React.useMemo(() => {
    console.log('Full tables API response:', tablesApiResponse);
    if (!tablesApiResponse) return [];
    if (tablesApiResponse.data?.results) return tablesApiResponse.data.results;
    if (tablesApiResponse.results) return tablesApiResponse.results;
    if (Array.isArray(tablesApiResponse.data)) return tablesApiResponse.data;
    if (Array.isArray(tablesApiResponse)) return tablesApiResponse;
    return [];
  }, [tablesApiResponse]);

  const apiOrders = React.useMemo(() => {
    console.log('Full orders API response:', ordersApiResponse);
    if (!ordersApiResponse) return [];
    if (ordersApiResponse.data?.results) return ordersApiResponse.data.results;
    if (ordersApiResponse.results) return ordersApiResponse.results;
    if (Array.isArray(ordersApiResponse.data)) return ordersApiResponse.data;
    if (Array.isArray(ordersApiResponse)) return ordersApiResponse;
    return [];
  }, [ordersApiResponse]);

  const getUserDisplayName = (user: User): string => {
    if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`;
    if (user.first_name) return user.first_name;
    if (user.username) return user.username;
    if (user.employee_no) return user.employee_no;
    return user.email || `User ${user.id}`;
  };

  const getEmployeeNoFromName = (name: string): string => {
    const allUsers = apiUsers.length > 0 ? apiUsers : users;
    const user = allUsers.find((u) => getUserDisplayName(u) === name);
    if (user && user.employee_no) return user.employee_no;
    console.warn(`No employee_no found for user: ${name}, using ID as fallback`);
    return user ? String(user.id) : name;
  };

  const getTableNumberFromValue = (tableValue: string): number => {
    const match = tableValue.match(/T-(\d+)/);
    return match ? parseInt(match[1], 10) : 1;
  };

  const getOrderDisplayInfo = (order: Order) => {
    const orderNumber = order.order_number || `ORD-${order.id}`;
    const display = `${orderNumber} (${order.order_type}, ${order.status})`;
    return {
      value: String(order.id),
      display,
      disabled: order.status !== 'Open',
    };
  };

  const tables = React.useMemo(() => {
    if (isLoadingTables) return [];
    if (tablesError) {
      console.error('Tables API error:', tablesError);
      return fallbackTables.map((table) => ({
        id: String(table.id),
        number: table.number,
        name: table.name || `Table ${table.number || table.id}`,
        capacity: table.capacity && table.capacity <= 20 ? table.capacity : undefined,
        status: table.is_active === false ? 'unavailable' : table.status || 'available',
      })).map((table) => ({
        value: `T-${String(table.number || table.id).padStart(2, '0')}`,
        display: `${table.name}${table.capacity ? ` (${table.capacity} seats)` : ''}${table.status !== 'available' ? ` - ${table.status}` : ''}`,
        disabled: table.status === 'occupied' || table.status === 'reserved' || table.status === 'unavailable' || isNaN(Number(table.number)),
      }));
    }
    return apiTables
      .filter((table) => {
        const tableNumber = table.number || table.id;
        return !isNaN(Number(tableNumber)) && tableNumber !== 'string';
      })
      .map((table) => ({
        id: String(table.id),
        number: table.number,
        name: table.name || `Table ${table.number || table.id}`,
        capacity: table.capacity && table.capacity <= 20 ? table.capacity : undefined,
        status: table.is_active === false ? 'unavailable' : table.status || 'available',
      }))
      .map((table) => ({
        value: `T-${String(table.number || table.id).padStart(2, '0')}`,
        display: `${table.name}${table.capacity ? ` (${table.capacity} seats)` : ''}${table.status !== 'available' ? ` - ${table.status}` : ''}`,
        disabled: table.status === 'occupied' || table.status === 'reserved' || table.status === 'unavailable' || isNaN(Number(table.number)),
      }));
  }, [apiTables, isLoadingTables, tablesError]);

  const waiters = React.useMemo(() => {
    const usersToProcess = apiUsers.length > 0 ? apiUsers : users;
    if (usersToProcess.length > 0) {
      return usersToProcess
        .filter((user: User) => {
          const role = typeof user.role === 'number' ? roleMap[user.role] : user.role?.toLowerCase();
          return true; // Include all users for now
        })
        .map((user: User) => ({
          id: user.id,
          name: getUserDisplayName(user),
          role: typeof user.role === 'number' ? roleMap[user.role] || 'unknown' : user.role || 'unknown',
          employee_no: user.employee_no,
        }));
    }
    return fallbackWaiters.map((waiter) => ({
      id: waiter.id,
      name: getUserDisplayName(waiter),
      role: waiter.role,
      employee_no: waiter.employee_no,
    }));
  }, [apiUsers, users]);

  const orders = React.useMemo(() => {
    if (isLoadingOrders) return [];
    if (ordersError) {
      console.error('Orders API error:', ordersError);
      return [];
    }
    return apiOrders
      .filter((order) => order.status === 'Open')
      .map(getOrderDisplayInfo);
  }, [apiOrders, isLoadingOrders, ordersError]);

  const isUsersLoading = loadingUsers || isLoadingUsers;

  const isFormValid =
    checkInForm.tableNumber &&
    checkInForm.guestCount &&
    Number(checkInForm.guestCount) > 0 &&
    checkInForm.waiterName &&
    !isUsersLoading &&
    !isLoadingTables &&
    !isLoadingOrders;

  const createPayload = (): GuestCheckPayload => {
    const now = new Date().toISOString();
    const payload: GuestCheckPayload = {
      guest_count: parseInt(checkInForm.guestCount) || 1,
      opened_at: now,
      status: 'Open',
      payment_status: 'Unpaid',
      sub_total: '0.00',
      tax_total: '0.00',
      service_charge_total: '0.00',
      discount_total: '0.00',
      table_number: getTableNumberFromValue(checkInForm.tableNumber),
      employee: getEmployeeNoFromName(checkInForm.waiterName),
      linked_checks: [],
    };
    if (checkInForm.order) {
      payload.order = parseInt(checkInForm.order);
    }
    return payload;
  };

  const handleCheckIn = async () => {
    if (!isFormValid) return;
    try {
      const payload = createPayload();
      console.log('Submitting guest check payload:', payload);
      const employeeNo = getEmployeeNoFromName(checkInForm.waiterName);
      if (!employeeNo || employeeNo === checkInForm.waiterName) {
        throw new Error('Invalid employee selection. Please select a valid waiter.');
      }
      const result = await addGuestCheck(payload).unwrap();
      console.log('Guest check created successfully:', result);
      onCheckIn();
      getOrders({}); // Refetch orders to update OrderDashboard
      onOpenChange(false);
      setCheckInForm({
        tableNumber: '',
        guestCount: '',
        waiterName: '',
        specialRequests: '',
        order: '',
      });
    } catch (error: any) {
      console.error('Error creating guest check:', error);
      if (error?.data?.employee?.[0]) {
        alert(`Employee Error: ${error.data.employee[0]}`);
      } else if (error?.data?.order?.[0]) {
        alert(`Order Error: ${error.data.order[0]}`);
      } else if (error?.message) {
        alert(`Error: ${error.message}`);
      } else {
        alert('Failed to create guest check. Please try again.');
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid) {
      handleCheckIn();
    }
  };

  return (
    <ErrorBoundary>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-sm border-0">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              New Guest Check-in
            </DialogTitle>
            <DialogDescription>
              Fill out the form below to check in a new guest.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="order" className="text-sm font-medium">
                Order <span className="text-gray-400">(Optional)</span>
              </Label>
              <Select
                value={checkInForm.order || ''}
                onValueChange={(value) => setCheckInForm({ ...checkInForm, order: value })}
                disabled={isLoadingOrders}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={isLoadingOrders ? 'Loading orders...' : 'Select order (optional)'} />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingOrders ? (
                    <SelectItem value="" disabled>
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2" />
                        Loading orders...
                      </div>
                    </SelectItem>
                  ) : orders.length > 0 ? (
                    orders.map((order) => (
                      <SelectItem key={order.value} value={order.value} disabled={order.disabled}>
                        <div className="flex items-center">
                          <ShoppingCart className="h-3 w-3 mr-2" />
                          <span className={order.disabled ? 'text-gray-400' : ''}>
                            {order.display}
                          </span>
                          {order.disabled && (
                            <span className="ml-2 text-xs text-red-500">(Closed)</span>
                          )}
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      <div className="flex items-center text-gray-500">
                        <AlertCircle className="h-3 w-3 mr-2" />
                        No open orders available
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {ordersError && (
                <div className="flex items-center text-sm text-red-600 bg-red-50 p-2 rounded">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Failed to load orders.
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="tableNumber" className="text-sm font-medium">
                Table Number *
              </Label>
              <Select
                value={checkInForm.tableNumber}
                onValueChange={(value) => setCheckInForm({ ...checkInForm, tableNumber: value })}
                disabled={isLoadingTables}
                required
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={isLoadingTables ? 'Loading tables...' : 'Select table'} />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingTables ? (
                    <SelectItem value="" disabled>
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2" />
                        Loading tables...
                      </div>
                    </SelectItem>
                  ) : tables.length > 0 ? (
                    tables.map((table) => (
                      <SelectItem key={table.value} value={table.value} disabled={table.disabled}>
                        <div className="flex items-center">
                          <span className={table.disabled ? 'text-gray-400' : ''}>
                            {table.display}
                          </span>
                          {table.disabled && (
                            <span className="ml-2 text-xs text-red-500">(Unavailable)</span>
                          )}
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      <div className="flex items-center text-gray-500">
                        <AlertCircle className="h-3 w-3 mr-2" />
                        No tables available
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {tablesError && (
                <div className="flex items-center text-sm text-red-600 bg-red-50 p-2 rounded">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Failed to load tables. Using default tables.
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="guestCount" className="text-sm font-medium">
                Number of Guests *
              </Label>
              <Input
                id="guestCount"
                type="number"
                min="1"
                max="20"
                placeholder="Enter guest count"
                value={checkInForm.guestCount}
                onChange={(e) => setCheckInForm({ ...checkInForm, guestCount: e.target.value })}
                className="w-full"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="waiterName" className="text-sm font-medium">
                Assigned Waiter *
              </Label>
              <Select
                value={checkInForm.waiterName}
                onValueChange={(value) => setCheckInForm({ ...checkInForm, waiterName: value })}
                disabled={isUsersLoading}
                required
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={isUsersLoading ? 'Loading waiters...' : 'Select waiter'} />
                </SelectTrigger>
                <SelectContent>
                  {isUsersLoading ? (
                    <SelectItem value="" disabled>
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2" />
                        Loading waiters...
                      </div>
                    </SelectItem>
                  ) : waiters.length > 0 ? (
                    waiters.map((waiter) => (
                      <SelectItem key={waiter.id} value={waiter.name}>
                        <div className="flex items-center">
                          <Users className="h-3 w-3 mr-2" />
                          {waiter.name}
                          {waiter.employee_no && (
                            <span className="ml-2 text-xs text-gray-500">
                              ({waiter.employee_no})
                            </span>
                          )}
                          {waiter.role && (
                            <span className="ml-2 text-xs text-gray-500 capitalize">
                              - {waiter.role}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      <div className="flex items-center text-gray-500">
                        <AlertCircle className="h-3 w-3 mr-2" />
                        No waiters available
                      </div>
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {usersError && (
                <div className="flex items-center text-sm text-red-600 bg-red-50 p-2 rounded">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Failed to load users. Using fallback waiters.
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="specialRequests" className="text-sm font-medium">
                Special Requests <span className="text-gray-400">(Optional)</span>
              </Label>
              <Input
                id="specialRequests"
                placeholder="Any special requests or notes"
                value={checkInForm.specialRequests}
                onChange={(e) => setCheckInForm({ ...checkInForm, specialRequests: e.target.value })}
                className="w-full"
              />
            </div>
          </form>

          <DialogFooter className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCheckIn}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              disabled={!isFormValid || isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                  Creating Check...
                </div>
              ) : isUsersLoading || isLoadingTables || isLoadingOrders ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                  Loading...
                </div>
              ) : (
                'Check In'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </ErrorBoundary>
  );
};