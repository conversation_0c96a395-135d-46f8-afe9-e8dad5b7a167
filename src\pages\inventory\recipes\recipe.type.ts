export interface Recipe {
  id: string;
  name: string;
  type: 'Prep' | 'Final';
  category: string;
  portionSize: number;
  portionUnit: string;
  costPerPortion: number;
  active: boolean;
  description?: string;
  instructions?: string;
  prepTime?: number; // in minutes
  cookTime?: number; // in minutes
  servings: number;
  linkedMenuItems?: string[];
  createdAt: string;
  updatedAt: string;
  ingredients: RecipeIngredient[];
}

export interface RecipeIngredient {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unit: string;
  cost: number;
  notes?: string;
}

export interface RecipeCategory {
  id: string;
  name: string;
  description?: string;
  type: 'Prep' | 'Final' | 'Both';
}

export interface RecipeFormData {
  name: string;
  type: 'Prep' | 'Final';
  category: string;
  portionSize: string;
  portionUnit: string;
  description: string;
  instructions: string;
  prepTime: string;
  cookTime: string;
  servings: string;
  active: boolean;
  ingredients: RecipeIngredient[];
}

export interface RecipeFormErrors {
  name?: string;
  category?: string;
  portionSize?: string;
  portionUnit?: string;
  servings?: string;
  ingredients?: string;
}