import React, { useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import { Screen } from '@/app-components/layout/screen';
import { 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Store,
  Building2,
  Calculator,
  Monitor
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RevenueCenter, Branch } from '@/types/pos';
import {
  useGetRevenueCentersQuery,
  useDeleteRevenueCenterMutation
} from '@/redux/slices/revenueCenters';
import { useGetBranchesQuery } from '@/redux/slices/branches';
import { handleApiError, handleApiSuccess } from '@/utils/errorHandling';
import { useEffect } from 'react';

const RevenueCenterManagement: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState(searchParams.get('branch') || 'all');

  // API hooks
  const { data: revenueCenters = [], isLoading: loadingRevenueCenters, error: revenueCentersError, refetch: refetchRevenueCenters } = useGetRevenueCentersQuery({});
  const { data: branches = [], isLoading: loadingBranches, error: branchesError } = useGetBranchesQuery({});
  const [deleteRevenueCenter] = useDeleteRevenueCenterMutation();

  // Handle API errors
  useEffect(() => {
    if (revenueCentersError) {
      handleApiError(revenueCentersError, 'load revenue centers');
    }
    if (branchesError) {
      handleApiError(branchesError, 'load branches');
    }
  }, [revenueCentersError, branchesError]);

  const handleDeleteRevenueCenter = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this revenue center?')) {
      try {
        await deleteRevenueCenter(id).unwrap();
        handleApiSuccess('Revenue center deleted successfully!');
      } catch (error: any) {
        handleApiError(error, 'delete revenue center');
      }
    }
  };

  const handleRefresh = () => {
    console.log('Manually refreshing revenue centers data...');
    refetchRevenueCenters();
  };

  const columns: ColumnDef<RevenueCenter>[] = [
    {
      accessorKey: 'revenue_center_code',
      header: 'Code',
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue('revenue_center_code')}</div>
      ),
    },
    {
      accessorKey: 'name',
      header: 'Revenue Center Name',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Store className="h-4 w-4 text-muted-foreground" />
          <div>
            <div className="font-medium">{row.getValue('name')}</div>
            <div className="text-sm text-muted-foreground">Revenue Center</div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'branch',
      header: 'Branch',
      cell: ({ row }) => {
        const branchRef = row.original.branch;
        // Find branch by both branch_code and id for compatibility
        const branch = branches.find(b =>
          b.branch_code === branchRef ||
          b.id?.toString() === branchRef
        );
        return (
          <div className="flex items-center space-x-2">
            <Building2 className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">{branch?.name || 'Unknown Branch'}</div>
              <div className="text-sm text-muted-foreground">{branch?.branch_code || branchRef}</div>
            </div>
          </div>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const isActive = row.original.is_active ?? true;
        return (
          <Badge variant={isActive ? "default" : "secondary"}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const revenueCenter = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/pos/revenue-centers/${revenueCenter.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Revenue Center
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Monitor className="h-4 w-4 mr-2" />
                Manage Workstations
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Calculator className="h-4 w-4 mr-2" />
                Tax Configuration
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                {revenueCenter.is_active ? 'Deactivate' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => handleDeleteRevenueCenter(revenueCenter.id?.toString() || '')}
              >
                Delete Revenue Center
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filteredRevenueCenters = Array.isArray(revenueCenters) ? revenueCenters.filter(rc => {
    const matchesSearch = rc.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rc.revenue_center_code?.toLowerCase().includes(searchTerm.toLowerCase());
    // Support filtering by both branch_code and id
    const matchesBranch = selectedBranch === 'all' ||
                         rc.branch?.toString() === selectedBranch ||
                         rc.branch === selectedBranch;
    return matchesSearch && matchesBranch;
  }) : [];

  return (
    <Screen>
      <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Revenue Center Management</h1>
          <p className="text-muted-foreground">
            Manage business units within branches that generate income
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh} disabled={loadingRevenueCenters}>
            🔄 Refresh
          </Button>
          <Link to="/pos/revenue-centers/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Revenue Center
            </Button>
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>
            Find revenue centers by name, code, or branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search revenue centers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedBranch} onValueChange={setSelectedBranch}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select branch" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id?.toString() || ''}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Revenue Centers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Revenue Centers ({filteredRevenueCenters.length})</CardTitle>
          <CardDescription>
            Business units configured across all branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingRevenueCenters ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-muted-foreground">Loading revenue centers...</div>
            </div>
          ) : revenueCentersError ? (
            <div className="flex justify-center items-center h-32">
              <div className="text-red-600">Error loading revenue centers. Please try again.</div>
            </div>
          ) : (
            <DataTable
              data={filteredRevenueCenters}
              columns={columns}
              enablePagination={true}
              enableSorting={true}
              enableColumnFilters={true}
              enableSelectColumn={false}
            />
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Revenue Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Array.isArray(revenueCenters) ? revenueCenters.length : 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Array.isArray(revenueCenters) ? revenueCenters.filter(rc => rc.is_active !== false).length : 0}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Total Centers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(revenueCenters) ? revenueCenters.length : 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Branches Covered</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Array.isArray(revenueCenters) ? new Set(revenueCenters.map(rc => rc.branch)).size : 0}
            </div>
          </CardContent>
        </Card>
      </div>
      </div>
    </Screen>
  );
};

export default RevenueCenterManagement;
