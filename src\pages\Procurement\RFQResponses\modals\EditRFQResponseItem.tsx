import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/custom/Toast/MyToast";
import {
  useUpdateRFQResponseItemMutation,
  useGetTaxRatesQuery,
} from "@/redux/slices/procurement";

interface EditRFQResponseItemProps {
  open: boolean;
  onClose: () => void;
  item: any;
  onSuccess?: () => void;
}

const EditRFQResponseItem = ({ open, onClose, item, onSuccess }: EditRFQResponseItemProps) => {
  const [formData, setFormData] = useState({
    unit_price: "",
    delivery_time_days: "",
    currency: "KES",
    tax_rate: "",
    total_price: "",
  });

  const [updateRFQResponseItem, { isLoading }] = useUpdateRFQResponseItemMutation();
  const { data: taxRates } = useGetTaxRatesQuery({});

  // Default currencies - only KES for now
  const currencies = {
    results: [
      { id: 1, code: "KES", name: "Kenya Shillings" }
    ]
  };

  // Initialize form data when item changes
  useEffect(() => {
    if (item) {
      setFormData({
        unit_price: item.unit_price || "",
        delivery_time_days: item.delivery_time_days?.toString() || "",
        currency: item.currency || "KES",
        tax_rate: item.tax_rate?.toString() || "0",
        total_price: item.total_price || "",
      });
    }
  }, [item]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.unit_price.trim()) {
      toast.error("Unit price is required");
      return;
    }

    if (!formData.currency) {
      toast.error("Currency is required");
      return;
    }

    try {
      const payload = {
        id: item.id,
        unit_price: formData.unit_price,
        currency: formData.currency,
        ...(formData.delivery_time_days && {
          delivery_time_days: parseInt(formData.delivery_time_days)
        }),
        ...(formData.tax_rate && formData.tax_rate !== "0" && {
          tax_rate: parseFloat(formData.tax_rate)
        }),
        ...(formData.total_price && {
          total_price: formData.total_price
        }),
      };

      await updateRFQResponseItem(payload).unwrap();
      toast.success("RFQ response item updated successfully");
      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Error updating RFQ response item:", error);
      toast.error(error?.data?.message || "Failed to update RFQ response item");
    }
  };

  // Calculate total price based on unit price, quantity, and tax rate
  const calculateTotalPrice = (unitPrice: string, quantity: string, taxRate: string) => {
    const price = parseFloat(unitPrice) || 0;
    const qty = parseFloat(quantity) || 0;
    const tax = parseFloat(taxRate) || 0;

    const subtotal = price * qty;
    const taxAmount = (subtotal * tax) / 100;
    const total = subtotal + taxAmount;

    return total.toFixed(2);
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Auto-calculate total price when unit_price or tax_rate changes
      if (field === 'unit_price' || field === 'tax_rate') {
        const quantity = item?.quantity || "1";
        newData.total_price = calculateTotalPrice(
          field === 'unit_price' ? value.toString() : prev.unit_price,
          quantity,
          field === 'tax_rate' ? value.toString() : prev.tax_rate
        );
      }

      return newData;
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Edit RFQ Response Item</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Item Information */}
          {item && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="font-medium text-sm text-gray-700">Item Details</h4>
              <p className="text-sm text-gray-600">Product: {item.product || 'N/A'}</p>
              <p className="text-sm text-gray-600">
                Quantity: {item.quantity || 'N/A'} {item.unit_of_measure || ''}
              </p>
            </div>
          )}

          {/* Unit Price */}
          <div>
            <Label htmlFor="unit_price">Unit Price *</Label>
            <Input
              id="unit_price"
              type="text"
              placeholder="Enter unit price"
              value={formData.unit_price}
              onChange={(e) => handleInputChange("unit_price", e.target.value)}
              required
            />
          </div>

          {/* Currency */}
          <div>
            <Label htmlFor="currency">Currency *</Label>
            <Select
              value={formData.currency}
              onValueChange={(value) => handleInputChange("currency", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                {currencies?.results?.map((currency: any) => (
                  <SelectItem key={currency.id} value={currency.code}>
                    {currency.code} - {currency.name}
                  </SelectItem>
                )) || (
                  <>
                    <SelectItem value="KES">KES - Kenyan Shilling</SelectItem>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Delivery Time */}
          <div>
            <Label htmlFor="delivery_time_days">Delivery Time (Days)</Label>
            <Input
              id="delivery_time_days"
              type="number"
              min="1"
              placeholder="Enter delivery time in days"
              value={formData.delivery_time_days}
              onChange={(e) => handleInputChange("delivery_time_days", e.target.value)}
            />
          </div>

          {/* Tax Rate */}
          <div>
            <Label htmlFor="tax_rate">Tax Rate</Label>
            <Select
              value={formData.tax_rate}
              onValueChange={(value) => handleInputChange("tax_rate", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select tax rate" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">No Tax</SelectItem>
                {taxRates?.results?.map((taxRate: any) => (
                  <SelectItem key={taxRate.id} value={taxRate.percentage.toString()}>
                    {taxRate.name} ({taxRate.percentage}%)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Total Price (Calculated) */}
          {formData.total_price && (
            <div>
              <Label htmlFor="total_price">Total Price (Calculated)</Label>
              <Input
                id="total_price"
                type="text"
                value={`${formData.currency} ${formData.total_price}`}
                disabled
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">
                Calculated: Unit Price × Quantity + Tax
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? "Updating..." : "Update Item"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditRFQResponseItem;
