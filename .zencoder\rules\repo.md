# GMC Client Information

## Summary
GMC Client is a React-based web application built with TypeScript and Vite. It appears to be a Point of Sale (POS) system with authentication, user management, and sales functionality. The application uses modern React patterns and libraries for state management, UI components, and routing.

## Structure
- **src/**: Main source code directory
  - **app-components/**: Layout and sidebar components
  - **components/**: Reusable UI components organized by function
  - **pages/**: Application pages and routes
  - **redux/**: Redux state management
  - **zustand/**: Zustand state management
  - **hooks/**: Custom React hooks
  - **utils/**: Utility functions
  - **types/**: TypeScript type definitions
  - **config/**: Application configuration
- **public/**: Static assets
- **.vscode/**: VS Code configuration

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: TypeScript ~5.6.2
**Build System**: Vite 6.0.5
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- **UI Framework**: React 18.3.1, React DOM 18.3.1
- **Routing**: React Router DOM 7.5.1
- **State Management**: Redux Toolkit 2.3.0, Zustand 5.0.4
- **UI Components**: Radix UI components, Tailwind CSS 3.4.17
- **Forms**: React Hook Form 7.54.2, Zod 3.24.1
- **Data Visualization**: Recharts 2.15.0
- **HTTP Client**: Axios 1.7.9
- **Date Handling**: Date-fns 3.6.0, Dayjs 1.11.13
- **PDF Generation**: PDFMake 0.2.18
- **Data Export**: XLSX 0.18.5

**Development Dependencies**:
- **Build Tools**: Vite 6.0.5, TypeScript 5.6.2
- **Linting**: ESLint 9.24.0
- **CSS Processing**: PostCSS 8.5.1, Tailwind CSS 3.4.17
- **Type Definitions**: Various @types packages

## Build & Installation
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```

## Main Entry Points
- **src/main.tsx**: Application entry point
- **src/App.tsx**: Main component with routing configuration
- **src/pages/Home.tsx**: Home page component
- **src/pages/auth/**: Authentication-related pages

## State Management
The application uses a combination of:
- **Redux**: For global state with Redux Toolkit and redux-persist
- **Zustand**: For simpler state management (currency, lock screen, sales)
- **React Context**: For theme and toast notifications

## Features
- **Authentication**: Sign in, sign up, password reset, OTP verification
- **UI Components**: Extensive use of Radix UI primitives with Tailwind styling
- **Responsive Design**: Mobile-friendly with responsive layouts
- **Data Visualization**: Charts and data display components
- **Form Handling**: Validation with Zod and React Hook Form
- **Internationalization**: Support for multiple currencies