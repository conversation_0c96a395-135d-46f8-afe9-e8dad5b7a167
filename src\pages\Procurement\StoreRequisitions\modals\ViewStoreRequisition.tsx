import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { StoreRequisition } from "@/types/procurement";
import { format } from "date-fns";
import { 
  Calendar, 
  User, 
  Building, 
  FileText, 
  Package,
  Hash,
  Clock
} from "lucide-react";

interface ViewStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
  requisition: StoreRequisition | null;
}

const ViewStoreRequisition = ({ isOpen, onClose, requisition }: ViewStoreRequisitionProps) => {
  if (!requisition) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Draft": return "bg-gray-100 text-gray-800";
      case "Submitted": return "bg-blue-100 text-blue-800";
      case "Approved": return "bg-green-100 text-green-800";
      case "Rejected": return "bg-red-100 text-red-800";
      case "Issued": return "bg-purple-100 text-purple-800";
      case "Converted": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Store Requisition Details - {requisition.code}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Basic Information
                </span>
                <Badge className={getStatusColor(requisition.status)}>
                  {requisition.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Requisition Code</p>
                  <p className="text-sm text-gray-600">{requisition.code}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Requested By</p>
                  <p className="text-sm text-gray-600">
                    {requisition.requested_by_name || `User ID: ${requisition.requested_by}`}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Cost Center</p>
                  <p className="text-sm text-gray-600">
                    {requisition.cost_center_name || `Cost Center ID: ${requisition.cost_center}`}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Store</p>
                  <p className="text-sm text-gray-600">
                    {requisition.store_name || `Store ID: ${requisition.store}`}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Required By</p>
                  <p className="text-sm text-gray-600">
                    {format(new Date(requisition.required_by), "PPP")}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Created At</p>
                  <p className="text-sm text-gray-600">
                    {requisition.created_at ? format(new Date(requisition.created_at), "PPp") : "N/A"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Purpose */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Purpose
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700">{requisition.purpose}</p>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Items ({requisition.items?.length || 0})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {requisition.items && requisition.items.length > 0 ? (
                <div className="space-y-4">
                  {requisition.items.map((item, index) => (
                    <div key={item.id || index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium">Product</p>
                          <p className="text-sm text-gray-600">
                            {item.product_name || `Product ID: ${item.product}`}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Quantity</p>
                          <p className="text-sm text-gray-600">
                            {item.quantity} {item.unit_of_measure_symbol || `Unit ID: ${item.unit_of_measure}`}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm font-medium">Unit of Measure</p>
                          <p className="text-sm text-gray-600">
                            {item.unit_of_measure_name || `Unit ID: ${item.unit_of_measure}`}
                          </p>
                        </div>
                      </div>
                      {item.remarks && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">Remarks</p>
                          <p className="text-sm text-gray-600">{item.remarks}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No items found</p>
              )}
            </CardContent>
          </Card>

          {/* Workflow Information */}
          {(requisition.submitted_at || requisition.approved_at || requisition.rejected_at) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Workflow History
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {requisition.submitted_at && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <p className="text-sm">
                      Submitted on {format(new Date(requisition.submitted_at), "PPp")}
                    </p>
                  </div>
                )}
                {requisition.approved_at && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <p className="text-sm">
                      Approved on {format(new Date(requisition.approved_at), "PPp")}
                    </p>
                  </div>
                )}
                {requisition.rejected_at && (
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <p className="text-sm">
                      Rejected on {format(new Date(requisition.rejected_at), "PPp")}
                    </p>
                  </div>
                )}
                {requisition.rejection_reason && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                    <p className="text-sm font-medium text-red-800">Rejection Reason:</p>
                    <p className="text-sm text-red-700">{requisition.rejection_reason}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewStoreRequisition;
