# GRN (Goods Received Note) Module

## Overview

The GRN (Goods Received Note) module is a comprehensive system for managing goods received from suppliers. It integrates seamlessly with the existing procurement workflow, allowing users to record deliveries, update inventory, and track the status of received items.

## Features

### ✅ Core Functionality
- **Create GRN**: Generate new GRNs from approved Purchase Orders
- **Edit GRN**: Update received quantities, prices, and status
- **View GRN Details**: Comprehensive view of GRN information and items
- **Delete GRN**: Remove GRNs with proper inventory reversal
- **Status Management**: Track GRN status (Partial, Full, Rejected)

### ✅ Advanced Features
- **Purchase Order Integration**: Direct GRN creation from PO detail page
- **Inventory Integration**: Automatic stock updates (with placeholder API calls)
- **Search & Filtering**: Advanced search and status-based filtering
- **Real-time Statistics**: Dashboard with GRN metrics
- **Responsive Design**: Mobile-friendly interface
- **Data Validation**: Comprehensive form validation and error handling

## File Structure

```
src/pages/Procurement/GRN/
├── index.tsx                 # Main GRN listing page
├── GRNDetail.tsx            # Individual GRN detail view
├── modals/
│   ├── AddGRN.tsx           # Create new GRN modal
│   └── EditGRN.tsx          # Edit existing GRN modal
└── README.md                # This documentation
```

## API Integration

### Endpoints Used
- `GET /procurement/grns` - List all GRNs
- `GET /procurement/grns/{id}` - Get specific GRN
- `POST /procurement/grns` - Create new GRN
- `PATCH /procurement/grns/{id}` - Update GRN
- `DELETE /procurement/grns/{id}` - Delete GRN
- `GET /procurement/grn-items` - List GRN items
- `POST /procurement/grn-items` - Create GRN item
- `PATCH /procurement/grn-items/{id}` - Update GRN item
- `DELETE /procurement/grn-items/{id}` - Delete GRN item

### Data Types
All API calls use exact type matching as specified in the API documentation:
- GRN status: "Partial" | "Full" | "Rejected"
- Decimal fields sent as strings
- Required fields properly validated

## Components

### 1. GRN Index (`index.tsx`)
**Purpose**: Main listing page for all GRNs

**Features**:
- Paginated data table with sorting
- Real-time search with debouncing
- Status-based filtering
- Quick statistics cards
- Bulk actions and export functionality
- Responsive design

**Key Functions**:
- `handleSearchChange()` - Debounced search
- `handleStatusFilter()` - Filter by status
- `handleDelete()` - Delete GRN with confirmation
- `handleEdit()` - Open edit modal

### 2. GRN Detail (`GRNDetail.tsx`)
**Purpose**: Detailed view of individual GRN

**Features**:
- Complete GRN information display
- Items table with received quantities
- Status update functionality
- Print and export options
- Related PO information
- Notes and attachments

**Key Functions**:
- `handleStatusUpdate()` - Update GRN status with inventory integration
- `handleDelete()` - Delete with inventory reversal

### 3. Add GRN Modal (`modals/AddGRN.tsx`)
**Purpose**: Create new GRNs from Purchase Orders

**Features**:
- PO selection with auto-population
- Item management with quantity tracking
- Real-time total calculations
- Form validation
- Summary statistics
- URL parameter support for direct PO linking

**Key Functions**:
- `handleSubmit()` - Create GRN with inventory integration
- `handleItemChange()` - Update item quantities and prices
- `handlePOSelect()` - Load PO items

### 4. Edit GRN Modal (`modals/EditGRN.tsx`)
**Purpose**: Edit existing GRNs

**Features**:
- Pre-populated form data
- Item quantity updates
- Status changes
- Inventory integration for changes
- Validation and error handling

**Key Functions**:
- `handleSubmit()` - Update GRN with inventory adjustments
- `handleItemChange()` - Modify item details

## Integration Points

### Purchase Orders
- **Create GRN Button**: Added to approved PO detail pages
- **Related GRNs Section**: Shows GRNs created from each PO
- **Status Tracking**: PO shows delivery status based on GRNs

### Procurement Dashboard
- **GRN Statistics**: Quick stats card with status breakdown
- **Navigation**: Direct links to GRN management
- **Quick Actions**: Create GRN shortcut

### Inventory System
- **Stock Updates**: Automatic inventory updates when items received
- **Reversal Logic**: Inventory reversal when GRNs cancelled/rejected
- **Validation**: Stock level validation and warnings
- **Integration Hooks**: Modular system for inventory API integration

## Inventory Integration

### Current Implementation
The inventory integration is implemented with a modular approach using placeholder functions that can be easily replaced with actual API calls when the inventory endpoints are available.

### Key Files
- `src/utils/inventoryIntegration.ts` - Integration utilities and hooks

### Integration Points
1. **GRN Creation**: Updates stock when items received
2. **GRN Updates**: Adjusts stock when quantities change
3. **GRN Cancellation**: Reverses stock when GRN rejected/deleted
4. **Validation**: Checks for reasonable quantities and expiry dates

### Future Implementation
Replace placeholder functions in `inventoryIntegration.ts` with actual API calls:
- `updateInventoryStock()` - Update stock levels
- `reverseInventoryStock()` - Reverse stock changes
- `getCurrentStockLevels()` - Get current inventory

## Usage Guide

### Creating a GRN
1. Navigate to approved Purchase Order
2. Click "Create GRN" button
3. Fill in received quantities for each item
4. Add notes if needed
5. Submit to create GRN and update inventory

### Managing GRNs
1. Go to Procurement → Goods Received Notes
2. Use search and filters to find specific GRNs
3. Click on GRN number to view details
4. Use actions menu for edit/delete operations

### Status Management
- **Partial**: Some items received, others pending
- **Full**: All items fully received
- **Rejected**: Delivery rejected (reverses inventory)

## Error Handling

### Form Validation
- Required field validation
- Quantity and price validation
- Date validation
- PO selection validation

### API Error Handling
- Network error handling
- Server error display
- Retry mechanisms
- Graceful degradation

### Inventory Integration
- Non-blocking inventory updates
- Error logging for failed updates
- Fallback mechanisms
- User notifications for critical failures

## Performance Optimizations

### Data Loading
- Lazy loading of GRN items
- Debounced search queries
- Paginated results
- Optimistic updates

### Caching
- Redux RTK Query caching
- Automatic cache invalidation
- Background refetching

### UI Performance
- Virtualized tables for large datasets
- Memoized components
- Efficient re-renders

## Testing Recommendations

### Unit Tests
- Form validation logic
- Data transformation functions
- Inventory integration hooks
- Status calculation functions

### Integration Tests
- API endpoint integration
- Redux state management
- Component interactions
- Error scenarios

### E2E Tests
- Complete GRN workflow
- PO to GRN integration
- Search and filtering
- Status updates

## Security Considerations

### Data Validation
- Server-side validation required
- Input sanitization
- Type checking
- Range validation

### Access Control
- Role-based permissions
- Operation-level security
- Audit logging
- Data encryption

## Deployment Notes

### Environment Variables
Ensure proper API endpoints are configured for:
- Procurement API
- Inventory API (when available)
- User management API

### Database Considerations
- Proper indexing on search fields
- Foreign key constraints
- Transaction handling for inventory updates

### Monitoring
- API response times
- Error rates
- User activity
- Inventory accuracy

## Future Enhancements

### Planned Features
1. **Batch GRN Processing**: Handle multiple deliveries
2. **Barcode Scanning**: Mobile app integration
3. **Photo Attachments**: Delivery documentation
4. **Automated Matching**: PO vs delivery comparison
5. **Supplier Portal**: Direct supplier updates
6. **Advanced Analytics**: Delivery performance metrics

### Technical Improvements
1. **Real-time Updates**: WebSocket integration
2. **Offline Support**: PWA capabilities
3. **Advanced Search**: Full-text search
4. **Export Options**: Multiple format support
5. **Audit Trail**: Complete change history

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

---

**Last Updated**: January 2025
**Version**: 1.0.0
**Author**: Development Team
