import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import React, { useState, useEffect } from "react";
import { CreateComboMealModal } from "./Modals/CreateCombo";
import { MealCard } from "./Components/ComboCard";
import { useGetComboMenusQuery } from "@/redux/slices/comboMenu";

interface MealItem {
  title: string;
  description: string;
  imageUrl: string;
  price: string;
  category: string;
  taxClass?: string;
  rating?: number;
  prepTime: string;
}

// API Response interfaces
interface ComboApiItem {
  id: number;
  quantity: number;
  required: boolean;
  allow_choice: boolean;
  combo: number;
  menu_item: number;
}

interface ComboApiResponse {
  message: string;
  data: {
    current_page: number;
    last_page: number;
    per_page: number;
    total_data: number;
    links: {
      next: string | null;
      previous: string | null;
    };
    results: ComboApiItem[];
  };
}

function FoodCombo() {
  // Remove the empty object parameter
  const { data: comboMenus, isLoading, error } = useGetComboMenusQuery({});
  
  const [meals, setMeals] = useState<MealItem[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Map actual API response to meals format
  useEffect(() => {

    if (comboMenus?.data?.results && Array.isArray(comboMenus.data.results)) {
      const transformedMeals = transformApiDataToMeals(comboMenus.data.results);
      setMeals(transformedMeals);
      console.log("Mapped API Data to meals:", transformedMeals);
    }
  }, [comboMenus]);

  // Transform API data to meal format
  const transformApiDataToMeals = (apiData: ComboApiItem[]): MealItem[] => {
    // Group combos by combo ID to create proper combo meals
    const comboGroups = apiData.reduce((groups, item) => {
      const comboId = item.combo;
      if (!groups[comboId]) {
        groups[comboId] = [];
      }
      groups[comboId].push(item);
      return groups;
    }, {} as Record<number, ComboApiItem[]>);

    // Convert each combo group to a meal item
    return Object.entries(comboGroups).map(([comboId, items]) => {
      const mainItem = items[0]; // Use first item for main details
      const itemCount = items.length;
      const requiredItems = items.filter(item => item.required).length;
      const optionalItems = items.filter(item => !item.required).length;

      return {
        title: `Combo ${comboId}`,
        description: `${itemCount} items combo - ${requiredItems} required, ${optionalItems} optional`,
        imageUrl: getComboImage(parseInt(comboId)),
        price: calculateComboPrice(items),
        category: "Combo Meal",
        taxClass: "standard",
        rating: 4.5,
        prepTime: calculatePrepTime(items),
      };
    });
  };

  // Helper function to get combo image based on combo ID
  const getComboImage = (comboId: number): string => {
    const comboImages = [
      "https://images.unsplash.com/photo-1546069901-ba9599a7e63c?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1571091718767-18b5b1457add?auto=format&fit=crop&w=400&q=80",
      "https://images.unsplash.com/photo-1512152272829-e3139592d56f?auto=format&fit=crop&w=400&q=80"
    ];
    return comboImages[comboId % comboImages.length] || comboImages[0];
  };

  // Helper function to calculate combo price (you might need to adjust this based on your pricing logic)
  const calculateComboPrice = (items: ComboApiItem[]): string => {
    // Base price calculation - adjust based on your business logic
    const basePrice = 150; // Base combo price in KES
    const additionalPrice = items.length * 50; // Additional price per item
    const totalPrice = basePrice + additionalPrice;
    return `KES ${totalPrice.toFixed(2)}`;
  };

  // Helper function to calculate prep time based on items
  const calculatePrepTime = (items: ComboApiItem[]): string => {
    const basePrepTime = 8; // Base prep time in minutes
    const additionalTime = items.length * 2; // Additional time per item
    const totalTime = basePrepTime + additionalTime;
    return `${totalTime} mins`;
  };

  const handleAddMenuItem = (newItem: MealItem) => {
    setMeals((prev) => [...prev, newItem]);
  };

  const handleMealCardClick = (meal: MealItem) => {
    console.log("Selected meal:", meal);
  };

  return (
    <Screen>
      <header className="mb-6">
        <div className="flex-1">
          <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 via-red-500 to-red-600 rounded-2xl shadow-2xl">
            <div className="px-8 py-12">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <div className="w-8 h-8 bg-white/30 rounded flex items-center justify-center">
                    🍽️
                  </div>
                </div>
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold text-white drop-shadow">Combo Menu</h1>
                  <p className="text-white/80 text-sm">
                    Delicious combo meals at great prices
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex justify-end mb-6">
        <PrimaryButton 
          variant="primary" 
          onClick={() => setIsModalOpen(true)}
          disabled={isLoading}
        >
          Add Combo
        </PrimaryButton>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-3 text-gray-600">Loading combos...</span>
        </div>
      )}

      {/* Error State - More detailed error handling */}
    

      

      {/* Meals Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        {meals.map((meal, index) => (
          <MealCard
            key={`${meal.title}-${index}`}
            title={meal.title}
            description={meal.description}
            imageUrl={meal.imageUrl}
            price={meal.price}
            category={meal.category}
            rating={meal.rating}
            prepTime={meal.prepTime}
            onClick={() => handleMealCardClick(meal)}
            className="max-w-xs hover:scale-105 transition-transform duration-200"
          />
        ))}
      </div>

      {/* Empty State */}
      {!isLoading && meals.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🍽️</div>
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No combos available</h3>
          <p className="text-gray-500 mb-4">Start by adding your first combo meal!</p>
          <PrimaryButton onClick={() => setIsModalOpen(true)}>
            Add First Combo
          </PrimaryButton>
        </div>
      )}

      {/* Modal */}
      {isModalOpen && (
        <CreateComboMealModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleAddMenuItem}
        />
      )}
    </Screen>
  );
}

export default FoodCombo;