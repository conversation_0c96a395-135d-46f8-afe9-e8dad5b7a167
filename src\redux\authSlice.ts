import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { RootState } from './store';

// Authentication API Types
export interface LoginRequest {
  employee_no: string;
  username: string; // email
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user?: UserDetails;
  // Legacy response fields for backward compatibility
  AccessToken?: string;
  RefreshToken?: string;
  email?: string;
  fullnames?: string;
  employee_no?: string;
  department?: string;
  office?: string;
  team?: string;
  user_group?: string;
  user_permissions?: any[];
}

export interface RegistrationRequest {
  user: {
    email: string;
    password: string;
    username: string;
    first_name: string;
    last_name: string;
    employee_no: string;
    branch: string;
    role: number;
  };
}

export interface LogoutRequest {
  refresh_token: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface ResetLinkRequest {
  email: string;
}

// Updated User interface based on API specification
interface UserDetails {
  id?: number;
  username?: string;
  employee_no: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  pin?: number | null;
  dob?: string | null;
  phone?: string | null;
  permit_number?: string | null;
  permit_expiry_date?: string | null;
  hire_date?: string | null;
  is_saliry_paid?: boolean;
  pays_tax?: boolean;
  reset_code?: string | null;
  reset_code_creation_time?: string | null;
  role?: number;
  branch?: number;
  revenue_center?: number | null;
  work_station?: number | null;
  groups?: number[];
  user_permissions?: number[];
  // Legacy fields for backward compatibility
  fullnames?: string | null;
  department?: string | null;
  office?: string | null;
  team?: string | null;
  user_group?: string | null;
}

interface Token{
  access_token: string | null;
  refresh_token: string | null;
}

interface AuthState {
  token: Token | null;
  user_details:UserDetails | null;
}

const initialState: AuthState = {
  token: null,
  user_details: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<{ token: Token; user_details: UserDetails } | LoginResponse>) => {
      // Handle both new API response format and legacy format
      if ('access_token' in action.payload && 'refresh_token' in action.payload) {
        // New API response format (lowercase)
        const response = action.payload as LoginResponse;
        state.token = {
          access_token: response.access_token,
          refresh_token: response.refresh_token,
        };
        state.user_details = response.user || {
          email: response.email || null,
          fullnames: response.fullnames || null,
          employee_no: response.employee_no || null,
          department: response.department || null,
          office: response.office || null,
          team: response.team || null,
          user_group: response.user_group || null,
          user_permissions: response.user_permissions || null,
          first_name: null,
          last_name: null,
        };
      } else if ('AccessToken' in action.payload && 'RefreshToken' in action.payload) {
        // Legacy API response format (camelCase)
        const response = action.payload as LoginResponse;
        state.token = {
          access_token: response.AccessToken!,
          refresh_token: response.RefreshToken!,
        };
        state.user_details = response.user || {
          email: response.email || null,
          fullnames: response.fullnames || null,
          employee_no: response.employee_no || null,
          department: response.department || null,
          office: response.office || null,
          team: response.team || null,
          user_group: response.user_group || null,
          user_permissions: response.user_permissions || null,
          first_name: null,
          last_name: null,
        };
      } else {
        // Legacy format
        const payload = action.payload as { token: Token; user_details: UserDetails };
        state.token = payload.token;
        state.user_details = payload.user_details;
      }
    },
    logout: (state) => {
      state.token = null;
      state.user_details = null;
    },
  },
});

const persistConfig = {
  key: 'auth',
  storage,
  whitelist: ['token', 'user_details'],
};

export const persistedAuthReducer = persistReducer(persistConfig, authSlice.reducer);
export const { setCredentials, logout } = authSlice.actions;

// Selectors
export const selectCurrentUserDetails = (state: RootState) => state.auth.user_details;
export const selectCurrentToken = (state: RootState) => state.auth.token;