import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { RFQ } from "@/types/procurement";
import { Clock, Calendar, User, Package } from "lucide-react";

interface ViewRFQProps {
  isOpen: boolean;
  onClose: () => void;
  rfq: RFQ | null;
}

const ViewRFQ: React.FC<ViewRFQProps> = ({
  isOpen,
  onClose,
  rfq,
}) => {
  if (!rfq) return null;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Draft: { 
        variant: "secondary" as const, 
        color: "bg-gray-100 text-gray-800", 
        icon: "📝",
        description: "In preparation"
      },
      Open: { 
        variant: "default" as const, 
        color: "bg-green-100 text-green-800", 
        icon: "📂",
        description: "Accepting responses"
      },
      Closed: { 
        variant: "default" as const, 
        color: "bg-blue-100 text-blue-800", 
        icon: "🔒",
        description: "Response period ended"
      },
      Cancelled: { 
        variant: "destructive" as const, 
        color: "bg-red-100 text-red-800", 
        icon: "❌",
        description: "Cancelled"
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Draft;

    return (
      <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
        <span>{config.icon}</span>
        {status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>RFQ Details</span>
            {getStatusBadge(rfq.status || "Draft")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">RFQ Number</label>
                <p className="text-lg font-semibold text-gray-900">
                  {rfq.rfq_number || `RFQ-${String(rfq.id).padStart(5, '0')}`}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Created By
                </label>
                <p className="text-gray-900">
                  {rfq.created_by || "Not specified"}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Supplier Category</label>
                <p className="text-gray-900">
                  {rfq.supplier_category || "All Categories"}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Created Date
                </label>
                <p className="text-gray-900">
                  {formatDate(rfq.created_at || "")}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Response Deadline
                </label>
                <p className="text-gray-900">
                  {formatDate(rfq.response_deadline || "")}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Status</label>
                <div className="flex flex-col gap-1">
                  {getStatusBadge(rfq.status || "Draft")}
                  <span className="text-xs text-gray-500">
                    {rfq.status === "Draft" && "In preparation"}
                    {rfq.status === "Open" && "Accepting responses"}
                    {rfq.status === "Closed" && "Response period ended"}
                    {rfq.status === "Cancelled" && "Cancelled"}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Items Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Package className="h-5 w-5" />
              RFQ Items ({(rfq as any).rfq_items?.length || 0})
            </h3>
            
            {(rfq as any).rfq_items && (rfq as any).rfq_items.length > 0 ? (
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit of Measure
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        RFQ Reference
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {(rfq as any).rfq_items.map((item: any, index: number) => (
                      <tr key={item.id || index}>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex flex-col">
                            <span className="text-sm font-medium text-gray-900">
                              {item.product || "Not specified"}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            {parseFloat(item.quantity || "0").toFixed(2)}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">
                            Unit ID: {item.unit_of_measure}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className="text-sm text-blue-600 font-medium">
                            {item.rfq}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No items found
              </div>
            )}
          </div>

          {/* Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Items:</span>
                <span className="ml-2 font-medium">{(rfq as any).rfq_items?.length || 0}</span>
              </div>
              <div>
                <span className="text-gray-600">Total Quantity:</span>
                <span className="ml-2 font-medium">
                  {(rfq as any).rfq_items?.reduce((sum: number, item: any) => 
                    sum + parseFloat(item.quantity || "0"), 0
                  ).toFixed(2) || "0.00"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewRFQ;
