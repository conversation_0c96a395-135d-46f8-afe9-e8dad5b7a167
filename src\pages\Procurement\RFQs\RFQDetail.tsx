import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  useGetRFQQuery,
  useCloseRFQMutation,
  useCancelRFQMutation,
  useDeleteRFQMutation,
} from "@/redux/slices/procurement";
import { 
  ArrowLeft, 
  Send, 
  CheckCircle, 
  XCircle, 
  FileText, 
  Calendar,
  User,
  Building,
  Package,
  Loader2,
  Download,
  MapPin,
  Clock,
  Users,
  MessageSquare
} from "lucide-react";
import { toast } from "@/components/custom/Toast/MyToast";
import RFQResponsesView from "./components/RFQResponsesView";

import { downloadRFQPDF, printRFQ } from "@/utils/rfqPDF";

const RFQDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState("");
  const [activeTab, setActiveTab] = useState<"details" | "responses">("details");

  const { data: rfq, isLoading, error } = useGetRFQQuery(id!);

  const [closeRFQ, { isLoading: closing }] = useCloseRFQMutation();
  const [cancelRFQ, { isLoading: cancelling }] = useCancelRFQMutation();
  const [deleteRFQ, { isLoading: deleting }] = useDeleteRFQMutation();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      Open: { variant: "secondary" as const, color: "bg-green-100 text-green-800" },
      Closed: { variant: "default" as const, color: "bg-blue-100 text-blue-800" },
      Cancelled: { variant: "destructive" as const, color: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.Open;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {status}
      </Badge>
    );
  };



  const handleClose = async () => {
    try {
      await closeRFQ(Number(id)).unwrap();
      toast.success("RFQ closed successfully");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to close RFQ");
    }
  };

  const handleCancel = async () => {
    if (!cancelReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }
    
    try {
      await cancelRFQ({ id: Number(id), reason: cancelReason }).unwrap();
      toast.success("RFQ cancelled");
      setShowCancelDialog(false);
      setCancelReason("");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to cancel RFQ");
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you sure you want to delete this RFQ? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteRFQ(Number(id)).unwrap();
      toast.success("RFQ deleted successfully");
      navigate("/procurement/rfqs");
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to delete RFQ");
    }
  };

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Screen>
    );
  }

  if (error || !rfq) {
    return (
      <Screen>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">RFQ Not Found</h2>
          <p className="text-gray-600 mb-4">The RFQ you're looking for doesn't exist or has been removed.</p>
          <Button onClick={() => navigate("/procurement/rfqs")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to RFQs
          </Button>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/procurement/rfqs")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                RFQ {rfq.rfq_number}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                {getStatusBadge(rfq.status || "Open")}
                <span className="text-gray-500">•</span>
                <span className="text-gray-600">
                  Created {new Date(rfq.created_at || "").toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {rfq.status === "Open" && (
              <>
                <Button onClick={handleClose} disabled={closing}>
                  {closing ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Close RFQ
                </Button>

                <Button
                  variant="destructive"
                  onClick={() => setShowCancelDialog(true)}
                  disabled={cancelling}
                >
                  {cancelling ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <XCircle className="mr-2 h-4 w-4" />
                  )}
                  Cancel
                </Button>
              </>
            )}

            {(rfq.status === "Cancelled") && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleting}
              >
                {deleting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Delete
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("details")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "details"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <FileText className="inline-block w-4 h-4 mr-2" />
              RFQ Details
            </button>
            <button
              onClick={() => setActiveTab("responses")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "responses"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <MessageSquare className="inline-block w-4 h-4 mr-2" />
              Responses ({rfq.response_count || 0})
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "details" ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Details */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    RFQ Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">RFQ Number</Label>
                      <p className="font-medium">{rfq.rfq_number}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Status</Label>
                      <div className="mt-1">
                        {getStatusBadge(rfq.status || "Open")}
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">From Requisition</Label>
                      <p className="font-medium">{rfq.requisition_number || "Direct RFQ"}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Response Deadline</Label>
                      <p className="font-medium">
                        {rfq.response_deadline 
                          ? new Date(rfq.response_deadline).toLocaleDateString()
                          : "Not specified"
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Items Table */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    RFQ Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Unit</TableHead>
                        <TableHead>Specifications</TableHead>
                        <TableHead>Est. Unit Cost</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {rfq.items?.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{item.product_name}</div>
                              <div className="text-sm text-gray-500">{item.product_code}</div>
                            </div>
                          </TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell>{item.unit_of_measure_name}</TableCell>
                          <TableCell>
                            <div className="text-sm">{item.specifications || "-"}</div>
                          </TableCell>
                          <TableCell>
                            {item.estimated_unit_cost
                              ? `$${item.estimated_unit_cost.toLocaleString()}`
                              : "-"
                            }
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Suppliers */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Selected Suppliers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {rfq.supplier_names?.map((supplier, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <Building className="h-4 w-4 text-gray-500" />
                        <span className="text-sm font-medium">{supplier}</span>
                      </div>
                    )) || <p className="text-sm text-gray-500">No suppliers selected</p>}
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Delivery Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Delivery Location</Label>
                    <p className="font-medium">{rfq.delivery_location_name}</p>
                  </div>
                  {rfq.delivery_address && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">Delivery Address</Label>
                      <p className="text-sm">{rfq.delivery_address}</p>
                    </div>
                  )}
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Required Date</Label>
                    <p className="font-medium">
                      {rfq.required_date
                        ? new Date(rfq.required_date).toLocaleDateString()
                        : "Not specified"
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Created By</Label>
                    <p className="font-medium">{rfq.created_by_name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Created Date</Label>
                    <p className="text-sm">
                      {new Date(rfq.created_at || "").toLocaleString()}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">Response Deadline</Label>
                    <p className="text-sm">
                      {rfq.response_deadline
                        ? new Date(rfq.response_deadline).toLocaleString()
                        : "Not specified"
                      }
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <RFQResponsesView rfqId={Number(id)} />
        )}

        {/* Notes and Terms */}
        {activeTab === "details" && (rfq.notes || rfq.terms_and_conditions) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {rfq.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{rfq.notes}</p>
                </CardContent>
              </Card>
            )}

            {rfq.terms_and_conditions && (
              <Card>
                <CardHeader>
                  <CardTitle>Terms and Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm whitespace-pre-wrap">{rfq.terms_and_conditions}</p>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Cancel Dialog */}
        <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cancel RFQ</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="cancel-reason">Reason for Cancellation</Label>
                <Textarea
                  id="cancel-reason"
                  placeholder="Please provide a reason for cancelling this RFQ..."
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setShowCancelDialog(false);
                  setCancelReason("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleCancel}
                disabled={cancelling || !cancelReason.trim()}
              >
                {cancelling ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Cancel RFQ
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Send Email Modal */}

      </div>
    </Screen>
  );
};

export default RFQDetail;
