import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Save, 
  Loader2, 
  FileText, 
  Building, 
  AlertTriangle,
  CheckCircle,
  Upload,
  X,
  Receipt
} from "lucide-react";
import {
  useUpdateInvoiceMutation,
  useUpdateInvoiceItemMutation,
  useGetInvoiceItemsQuery,
  useGetGLAccountsQuery,
  useUploadInvoiceFileMutation,
} from "@/redux/slices/procurement";
import { Invoice, InvoiceItem } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface EditInvoiceProps {
  open: boolean;
  onClose: () => void;
  invoice: Invoice | null;
}

const EditInvoice = ({ open, onClose, invoice }: EditInvoiceProps) => {
  // Mock mutation states
  const [updating, setUpdating] = useState(false);
  const [uploading, setUploading] = useState(false);

  const updateInvoice = async (payload: any) => {
    setUpdating(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setUpdating(false);
    return { unwrap: () => Promise.resolve() };
  };

  const updateInvoiceItem = async (payload: any) => {
    await new Promise(resolve => setTimeout(resolve, 200));
    return { unwrap: () => Promise.resolve() };
  };

  const uploadFile = async (formData: FormData) => {
    setUploading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setUploading(false);
    return { unwrap: () => Promise.resolve({ file_path: "invoices/updated-file.pdf" }) };
  };

  // Mock supporting data
  const glAccounts = {
    results: [
      { id: 1, account_code: "5001", account_name: "Food Supplies", account_type: "Expense" },
      { id: 2, account_code: "5002", account_name: "Office Supplies", account_type: "Expense" },
      { id: 3, account_code: "5003", account_name: "Equipment", account_type: "Asset" },
      { id: 4, account_code: "5004", account_name: "Raw Materials", account_type: "Expense" },
    ]
  };

  // Mock invoice items data
  const invoiceItemsData = {
    results: [
      {
        id: 1,
        invoice: invoice?.id,
        product: 1,
        product_name: "Office Paper",
        quantity_invoiced: "100",
        unit_price: "15.00",
        total_price: "1500.00",
        variance_quantity: "0.00",
        variance_amount: "0.00",
      },
    ]
  };

  // Form state
  const [formData, setFormData] = useState({
    invoice_number: "",
    invoice_date: "",
    total_amount: "0.00",
    currency: "USD",
    gl_account: 0,
    notes: "",
  });

  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Initialize form data when invoice changes
  useEffect(() => {
    if (invoice && open) {
      setFormData({
        invoice_number: invoice.invoice_number,
        invoice_date: invoice.invoice_date.split('T')[0], // Convert to date format
        total_amount: invoice.total_amount,
        currency: invoice.currency,
        gl_account: invoice.gl_account,
        notes: invoice.notes || "",
      });
      
      // Set items from invoice or fetch from API
      if (invoice.items && invoice.items.length > 0) {
        setItems(invoice.items);
      } else if (invoiceItemsData?.results) {
        setItems(invoiceItemsData.results);
      }
    }
  }, [invoice, open, invoiceItemsData]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleItemChange = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Calculate total price if quantity or unit price changes
    if (field === "quantity_invoiced" || field === "unit_price") {
      const quantity = parseFloat(field === "quantity_invoiced" ? value : updatedItems[index].quantity_invoiced);
      const unitPrice = parseFloat(field === "unit_price" ? value : updatedItems[index].unit_price);
      
      if (!isNaN(quantity) && !isNaN(unitPrice)) {
        updatedItems[index].total_price = (quantity * unitPrice).toFixed(2);
      }

      // Calculate variance (assuming we have GRN data)
      // This would need to be enhanced with actual GRN item data
      const grnQty = 0; // Would come from GRN item
      const grnPrice = 0; // Would come from GRN item
      
      updatedItems[index].variance_quantity = (quantity - grnQty).toFixed(2);
      updatedItems[index].variance_amount = ((quantity * unitPrice) - (grnQty * grnPrice)).toFixed(2);
    }
    
    // Recalculate total amount
    const totalAmount = updatedItems.reduce((sum, item) => 
      sum + parseFloat(item.total_price), 0
    ).toFixed(2);
    
    setFormData(prev => ({ ...prev, total_amount: totalAmount }));
    setItems(updatedItems);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error("File size must be less than 10MB");
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!invoice) return;

    // Validation
    if (!formData.invoice_number.trim()) {
      toast.error("Invoice number is required");
      return;
    }

    if (!formData.gl_account) {
      toast.error("Please select a GL account");
      return;
    }

    try {
      // Upload file if selected
      let filePath = invoice.invoice_file;
      if (selectedFile) {
        const fileFormData = new FormData();
        fileFormData.append("file", selectedFile);

        const uploadResult = await uploadFile(fileFormData);
        const result = await uploadResult.unwrap();
        filePath = result.file_path;
      }

      // Update invoice
      const invoicePayload = {
        id: invoice.id!,
        invoice_number: formData.invoice_number.trim(),
        invoice_date: formData.invoice_date,
        total_amount: formData.total_amount,
        currency: formData.currency,
        gl_account: formData.gl_account,
        invoice_file: filePath || undefined,
        notes: formData.notes.trim() || undefined,
      };

      const invoiceResult = await updateInvoice(invoicePayload);
      await invoiceResult.unwrap();

      // Update invoice items
      const itemPromises = items.map(async (item) => {
        if (item.id) {
          const itemPayload = {
            id: item.id,
            quantity_invoiced: item.quantity_invoiced,
            unit_price: item.unit_price,
            total_price: item.total_price,
            variance_quantity: item.variance_quantity,
            variance_amount: item.variance_amount,
            notes: item.notes || undefined,
          };
          const itemResult = await updateInvoiceItem(itemPayload);
          return itemResult.unwrap();
        }
        return Promise.resolve();
      });

      await Promise.all(itemPromises);

      toast.success("Invoice updated successfully");
      onClose();
    } catch (error: any) {
      console.error("Error updating invoice:", error);
      toast.error(error?.data?.message || "Failed to update invoice");
    }
  };

  // Calculate variance warnings
  const getVarianceWarnings = () => {
    const warnings: string[] = [];
    items.forEach((item, index) => {
      const qtyVariance = parseFloat(item.variance_quantity || "0");
      const amountVariance = parseFloat(item.variance_amount || "0");
      
      if (Math.abs(qtyVariance) > 0.01) {
        warnings.push(`Item ${index + 1}: Quantity variance of ${qtyVariance}`);
      }
      if (Math.abs(amountVariance) > 0.01) {
        warnings.push(`Item ${index + 1}: Amount variance of ${amountVariance}`);
      }
    });
    return warnings;
  };

  const varianceWarnings = getVarianceWarnings();

  if (!invoice) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Edit Invoice - {invoice.invoice_number}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="invoice_number">Invoice Number *</Label>
                <Input
                  id="invoice_number"
                  value={formData.invoice_number}
                  onChange={(e) => handleInputChange("invoice_number", e.target.value)}
                  placeholder="Enter invoice number"
                  required
                  disabled={invoice.status !== "Draft"}
                />
              </div>

              <div>
                <Label htmlFor="invoice_date">Invoice Date *</Label>
                <Input
                  id="invoice_date"
                  type="date"
                  value={formData.invoice_date}
                  onChange={(e) => handleInputChange("invoice_date", e.target.value)}
                  required
                  disabled={invoice.status !== "Draft"}
                />
              </div>

              <div>
                <Label>GRN Number</Label>
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                  <Receipt className="h-4 w-4 text-gray-600" />
                  <span className="font-medium">
                    {invoice.grn_number || `GRN-${invoice.grn}`}
                  </span>
                </div>
              </div>

              <div>
                <Label>Supplier</Label>
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                  <Building className="h-4 w-4 text-gray-600" />
                  <span className="font-medium">
                    {invoice.supplier_name || "N/A"}
                  </span>
                </div>
              </div>

              <div>
                <Label htmlFor="gl_account">GL Account *</Label>
                <Select
                  value={formData.gl_account.toString()}
                  onValueChange={(value) => handleInputChange("gl_account", value)}
                  disabled={invoice.status !== "Draft"}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select GL account" />
                  </SelectTrigger>
                  <SelectContent>
                    {glAccounts?.results?.map((account: any) => (
                      <SelectItem key={account.id} value={account.account_code}>
                        <div>
                          <p className="font-medium">{account.account_code} - {account.account_name}</p>
                          <p className="text-sm text-gray-600">{account.account_type}</p>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="currency">Currency</Label>
                <Select 
                  value={formData.currency} 
                  onValueChange={(value) => handleInputChange("currency", value)}
                  disabled={invoice.status !== "Draft"}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="KES">KES</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Invoice File</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="invoice_file">Upload Invoice PDF</Label>
                  <div className="mt-2">
                    {invoice.invoice_file && (
                      <div className="flex items-center gap-2 mb-2 p-2 bg-blue-50 rounded-md">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">Current file: {invoice.invoice_file.split('/').pop()}</span>
                      </div>
                    )}
                    <input
                      id="invoice_file"
                      type="file"
                      accept=".pdf"
                      onChange={handleFileSelect}
                      className="hidden"
                      disabled={invoice.status !== "Draft"}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById("invoice_file")?.click()}
                      disabled={uploading || invoice.status !== "Draft"}
                    >
                      {uploading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Upload className="h-4 w-4 mr-2" />
                      )}
                      {selectedFile ? "Change File" : invoice.invoice_file ? "Replace File" : "Select File"}
                    </Button>
                    {selectedFile && (
                      <div className="flex items-center gap-2 mt-2">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{selectedFile.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedFile(null)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Add any additional notes..."
              rows={3}
              disabled={invoice.status !== "Draft"}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {invoice.status === "Draft" && (
              <Button type="submit" disabled={updating || uploading}>
                {updating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Update Invoice
                  </>
                )}
              </Button>
            )}
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditInvoice;
