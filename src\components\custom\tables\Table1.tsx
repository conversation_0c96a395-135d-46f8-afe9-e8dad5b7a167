import React, { useState, useRef, useEffect, ReactNode } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  RowSelectionState,
  VisibilityState,
  FilterFn,
  Column,
} from "@tanstack/react-table";
import {
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Settings,
  Pin,
  X,
  Maximize,
  Minimize,
  Download,
  Printer,
  CalendarIcon,
  Menu,
  Search,
} from "lucide-react";
import * as XLSX from "xlsx";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import {
  Content,
  DynamicContent,
  PredefinedPageSize,
} from "pdfmake/interfaces";
import { DateRange } from "react-day-picker";
// import { Popover, PopoverContent, PopoverTrigger } from '';
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Paginator from "./Paginator";

// Initialize pdfMake with fonts
pdfMake.vfs = pdfFonts.vfs;

// Generic type for table data
type DataRecord = Record<string, any>;

// Component props interface with generics
interface DataTableProps<T extends DataRecord> {
  data: T[];
  columns: ColumnDef<T>[];
  title?: string;
  enableToolbar?: boolean;
  enableExportToExcel?: boolean;
  enablePagination?: boolean;
  enablePrintPdf?: boolean;
  enableFullScreenToggle?: boolean;
  enableColumnControl?: boolean;
  searchInput?: ReactNode;
  enableColumnFilters?: boolean;
  enableSorting?: boolean;
  enableSelectColumn?: boolean;
  enableSelectToolbar?: boolean;
  enableSelectToolbarButtonExportToExcel?: boolean;
  enableSelectToolbarButtonPrintToPdf?: boolean;
  containerClassName?: string;
  tableClassName?: string;
  tHeadClassName?: string;
  tHeadCellsClassName?: string;
  tBodyClassName?: string;
  tBodyTrClassName?: string;
  tBodyCellsClassName?: string;
  getSelectedRows?: React.Dispatch<React.SetStateAction<[]>>;
  customButtons?: ReactNode;
  onRowClick?: (row: any) => void;
  // pagination
  currentPage?: number;
  setCurrentPage?: (page: number) => void;
  itemsPerPage?: number;
  setItemsPerPage?: (items: number) => void;
  totalItems?: number;
  siblingCount?: number;
  itemsPerPageOptions?: number[];
  paginationClassName?: string;
}

export function DataTable<T extends DataRecord>({
  data,
  columns,
  title = "Data Table",
  enableToolbar = false,
  enableExportToExcel = false,
  enablePagination = false,
  enablePrintPdf = false,
  enableFullScreenToggle = false,
  enableColumnControl = false,
  searchInput,
  enableColumnFilters = true,
  enableSorting = true,
  enableSelectColumn = true,
  enableSelectToolbar = false,
  enableSelectToolbarButtonExportToExcel = true,
  enableSelectToolbarButtonPrintToPdf = true,
  containerClassName,
  tableClassName,
  tHeadClassName,
  tHeadCellsClassName,
  tBodyClassName,
  tBodyTrClassName,
  tBodyCellsClassName,
  getSelectedRows,
  customButtons,
  onRowClick,
  // pagination
  currentPage,
  setCurrentPage,
  itemsPerPage,
  setItemsPerPage,
  totalItems,
  itemsPerPageOptions,
  paginationClassName,
}: DataTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnPinning, setColumnPinning] = useState({
    left: [""],
    right: [""],
  });
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Function to toggle fullscreen
  const toggleFullScreen = () => {
    if (!isFullScreen) {
      // Request fullscreen
      if (tableContainerRef.current?.requestFullscreen) {
        tableContainerRef.current.requestFullscreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, []);

  // Define the filter UI components for each column
  const renderColumnFilter = (column: any) => {
    const columnFilterValue = column.getFilterValue();
    const { filterVariant } = column.columnDef.meta ?? {};

    return filterVariant === "date" ? (
      <DateRangeFilter column={column} />
    ) : (
      <ColumnFilter column={column} />
    );
  };

  // Create a memoized checkbox column
  const selectionColumn: ColumnDef<T> = {
    id: "select",
    header: ({ table }) => (
      <div className="w-full flex justify-center">
        <input
          type="checkbox"
          checked={table.getIsAllRowsSelected()}
          onChange={table.getToggleAllRowsSelectedHandler()}
          className="h-4 w-4 "
          onClick={(e) => e.stopPropagation()}
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className="w-full flex justify-center">
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          className="h-4 w-4"
          onClick={(e) => e.stopPropagation()}
        />
      </div>
    ),
    enableSorting: false,
    enableColumnFilter: false,
  };

  // Prepend selection column to the columns array
  // columns = [ selectionColumn, ...columns];
  enableSelectColumn
    ? (columns = [selectionColumn, ...columns])
    : (columns = [...columns]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      rowSelection,
      columnVisibility,
      columnPinning,
    },
    enableColumnFilters: enableColumnFilters,
    enableSorting: enableSorting,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    // getPaginationRowModel: getPaginationRowModel(),
  });

  // Handler for row click
  const handleRowClick = (row: any) => {
    row.toggleSelected();
    onRowClick && onRowClick(row);
  };

  // Pin column function
  const pinColumn = (columnId: string, position: "left" | "right" | null) => {
    const newPinning = { ...columnPinning };

    // Remove from any existing pinning position
    newPinning.left = newPinning.left.filter((id) => id !== columnId);
    newPinning.right = newPinning.right.filter((id) => id !== columnId);

    // Add to the new position if not null
    if (position === "left") {
      newPinning.left.push(columnId);
    } else if (position === "right") {
      newPinning.right.push(columnId);
    }

    setColumnPinning(newPinning);
  };

  // Get pinning status of a column
  const getPinningStatus = (columnId: string) => {
    if (columnPinning.left.includes(columnId)) return "left";
    if (columnPinning.right.includes(columnId)) return "right";
    return null;
  };

  {
    /* ############################################################################################################### */
  }
  // Get selected rows data
  const getSelectedRowsData = () => {
    const selectedRowIds = Object.keys(rowSelection);
    return data.filter((row, index) =>
      selectedRowIds.includes(index.toString())
    );
  };

  useEffect(() => {
    const selectedRowsData = getSelectedRowsData();
    getSelectedRows && getSelectedRows(selectedRowsData as []);
  }, [rowSelection]);

  // Export selected rows to Excel function
  const exportSelectedToExcel = () => {
    // Get only visible columns (excluding select and actions columns)
    const visibleColumns = table
      .getVisibleLeafColumns()
      .filter((column) => column.id !== "select" && column.id !== "actions");

    // Get selected rows
    const selectedRowsData = getSelectedRowsData();

    // Create header row with visible column names
    const headers = visibleColumns.map((column) => {
      // Get the header value
      const headerValue = column.columnDef.header;
      return typeof headerValue === "string" ? headerValue : column.id;
    });

    // Create data rows
    const excelData = selectedRowsData.map((rowData) => {
      const formattedRowData: Record<string, any> = {};

      visibleColumns.forEach((column) => {
        // Use the accessor key to get the correct value
        const accessorKey = column.id;
        if (accessorKey in rowData) {
          formattedRowData[column.columnDef.header as string] = (
            rowData as any
          )[accessorKey];
        }
      });

      return formattedRowData;
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Selected Data");

    // Generate file name with date
    const date = new Date().toISOString().split("T")[0];
    const fileName = `${title}_${date}.xlsx`;

    // Write file and trigger download
    XLSX.writeFile(workbook, fileName);
  };

  // Print selected rows to PDF function using pdfMake
  const printSelectedToPdf = () => {
    // Get only visible columns (excluding select and actions columns)
    const visibleColumns = table
      .getVisibleLeafColumns()
      .filter((column) => column.id !== "select" && column.id !== "actions");

    // Get selected rows
    const selectedRowsData = getSelectedRowsData();

    // Create header row for PDF
    const headers = visibleColumns.map((column) => {
      const headerValue = column.columnDef.header;
      return typeof headerValue === "string" ? headerValue : column.id;
    });

    function getEmptyObjects(count: number) {
      return Array.from({ length: count }, () => ({}));
    }

    // Create PDF document definition
    const docDefinition = {
      content: [
        {
          table: {
            headerRows: 2,
            widths: [20, ...Array(visibleColumns.length - 1).fill("*")], // Equal width for all columns
            heights: function (row: any): number | "auto" {
              if (row == 0) {
                return 25;
              }
              return "auto";
            },
            body: [
              // Table headers
              [
                {
                  text: title,
                  style: "header",
                  colSpan: visibleColumns.length,
                  alignment: "center",
                },
                ...getEmptyObjects(visibleColumns.length - 1),
              ],
              headers,
              // Table data rows
              ...selectedRowsData.map((rowData) => {
                return visibleColumns.map((column) => {
                  // Use the accessor key to get the correct value
                  const accessorKey = column.id;
                  const value =
                    accessorKey in rowData ? (rowData as any)[accessorKey] : "";

                  // Return simple string value
                  return value !== null && value !== undefined
                    ? String(value)
                    : "";
                });
              }),
            ],
          },
          layout: {
            hLineWidth: function () {
              return 1;
            },
            vLineWidth: function () {
              return 1;
            },
            hLineColor: function () {
              return "#dddddd";
            },
            vLineColor: function () {
              return "#dddddd";
            },
          },
        },
      ],
      styles: {
        header: {
          fontSize: 14,
          bold: true,
          paddingTop: 10,
          paddingBottom: 10,
        },
        subheader: {
          fontSize: 12,
          italics: true,
          color: "#666666",
        },
      },
      // footer
      footer: function (currentPage: number, pageCount: number): Content {
        return {
          text:
            "Page " +
            currentPage.toString() +
            " of " +
            pageCount +
            "            " +
            `Generated on: ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
          alignment: "center",
          fontSize: 9,
        };
      },
      // page size to A4
      pageSize: "A4" as PredefinedPageSize,
      // page orientation
      pageOrientation: (visibleColumns.length > 5
        ? "landscape"
        : "portrait") as "portrait" | "landscape",
      // page margins
      pageMargins: [15, 15, 15, 15] as [number, number, number, number],
    };

    // Add information about selected rows count
    const selectedRowsCount = selectedRowsData.length;
    (docDefinition.content as any[]).splice(2, 0, {
      text: `Selected Rows: ${selectedRowsCount}`,
      style: "subheader",
      margin: [0, 0, 0, 10],
    });

    // Generate PDF and open in new window
    pdfMake.createPdf(docDefinition).open();
  };

  // Export to Excel function for all filtered data
  const exportToExcel = () => {
    // Get only visible columns (excluding select and actions columns)
    const visibleColumns = table
      .getVisibleLeafColumns()
      .filter((column) => column.id !== "select" && column.id !== "actions");

    // Get filtered rows (which are currently visible in the table)
    const rows = table.getFilteredRowModel().rows;

    // Create header row with visible column names
    const headers = visibleColumns.map((column) => {
      // Get the header value
      const headerValue = column.columnDef.header;
      return typeof headerValue === "string" ? headerValue : column.id;
    });

    // Create data rows
    const excelData = rows.map((row) => {
      const rowData: Record<string, any> = {};

      visibleColumns.forEach((column) => {
        // Get the cell value
        const value = row.getValue(column.id);

        // For status column, just get the raw value, not the JSX
        if (column.id === "status") {
          rowData[column.columnDef.header as string] = value;
        } else {
          rowData[column.columnDef.header as string] = value;
        }
      });

      return rowData;
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Data");

    // Generate file name with date
    const date = new Date().toISOString().split("T")[0];
    const fileName = `${title}_${date}.xlsx`;

    // Write file and trigger download
    XLSX.writeFile(workbook, fileName);
  };

  // Print to PDF function using pdfMake for all filtered data
  const printToPdf = () => {
    // Get only visible columns (excluding select and actions columns)
    const visibleColumns = table
      .getVisibleLeafColumns()
      .filter((column) => column.id !== "select" && column.id !== "actions");

    // Get filtered rows (which are currently visible in the table)
    const rows = table.getFilteredRowModel().rows;

    // Create header row for PDF
    const headers = visibleColumns.map((column) => {
      const headerValue = column.columnDef.header;
      return typeof headerValue === "string" ? headerValue : column.id;
    });

    function getEmptyObjects(count: number) {
      return Array.from({ length: count }, () => ({}));
    }

    // Create PDF document definition
    const docDefinition = {
      content: [
        {
          table: {
            headerRows: 2,
            widths: [20, ...Array(visibleColumns.length - 1).fill("*")], // Equal width for all columns
            heights: function (row: any): number | "auto" {
              if (row == 0) {
                return 25;
              }
              return "auto";
            },
            body: [
              // Table headers
              [
                {
                  text: title,
                  style: "header",
                  colSpan: visibleColumns.length,
                  alignment: "center",
                },
                ...getEmptyObjects(visibleColumns.length - 1),
              ],
              headers,
              // Table data rows
              ...rows.map((row) => {
                return visibleColumns.map((column) => {
                  const value = row.getValue(column.id);

                  // Return simple string value
                  return value !== null && value !== undefined
                    ? String(value)
                    : "";
                });
              }),
            ],
          },
          layout: {
            hLineWidth: function () {
              return 1;
            },
            vLineWidth: function () {
              return 1;
            },
            hLineColor: function () {
              return "#dddddd";
            },
            vLineColor: function () {
              return "#dddddd";
            },
          },
        },
      ],
      styles: {
        header: {
          fontSize: 14,
          bold: true,
          paddingTop: 10,
          paddingBottom: 10,
        },
        subheader: {
          fontSize: 12,
          italics: true,
          color: "#666666",
        },
      },
      // footer
      footer: function (currentPage: number, pageCount: number): Content {
        return {
          text:
            "Page " +
            currentPage.toString() +
            " of " +
            pageCount +
            "            " +
            `Generated on: ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
          alignment: "center",
          fontSize: 9,
        };
      },
      // page size to A4
      pageSize: "A4" as PredefinedPageSize,
      // page orientation
      pageOrientation: (visibleColumns.length > 5
        ? "landscape"
        : "portrait") as "portrait" | "landscape",
      // page margins
      pageMargins: [15, 15, 15, 15] as [number, number, number, number],
    };

    // Add information about applied filters
    const activeFilters: string[] = [];

    // Check global filter
    if (globalFilter) {
      activeFilters.push(`Global Search: "${globalFilter}"`);
    }

    // Check column filters
    table.getState().columnFilters.forEach((filter) => {
      const column = table.getColumn(filter.id);
      if (column) {
        const headerName = column.columnDef.header as string;
        activeFilters.push(`${headerName}: "${filter.value}"`);
      }
    });

    // If there are active filters, add them to the document
    if (activeFilters.length > 0) {
      (docDefinition.content as any[]).splice(
        2,
        0,
        {
          text: "Applied Filters:",
          style: "subheader",
          margin: [0, 0, 0, 5],
        },
        {
          ul: activeFilters,
          margin: [0, 0, 0, 10],
        }
      );
    }

    // If there are selected rows, add count to the document
    const selectedRowsCount = Object.keys(rowSelection).length;
    if (selectedRowsCount > 0) {
      (docDefinition.content as any[]).splice(2, 0, {
        text: `Selected Rows: ${selectedRowsCount}`,
        style: "subheader",
        margin: [0, 0, 0, 10],
      });
    }

    // Generate PDF and open in new window
    pdfMake.createPdf(docDefinition).open();
  };

  // {/* Mobile dropdown menu (visible on smaller screens <800px) */}
  // <div className="md:hidden flex items-center gap-5">
  // Column visibility and pinning dropdown
  const ColumnManagementDropdown = () => {
    return (
      <div className="relative">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setDropdownOpen(!dropdownOpen);
          }}
          className="hidden md:flex items-center gap-1 px-3 py-2 bg-background border rounded-md shadow-sm text-sm"
        >
          <Settings size={16} />
          <span>Columns</span>
        </button>

        <button
          className="md:hidden flex"
          onClick={(e) => {
            e.stopPropagation();
            setDropdownOpen(!dropdownOpen);
          }}
        >
          <Settings size={20} />
        </button>

        {dropdownOpen && (
          <div className="absolute right-0 mt-2 bg-background border rounded-md shadow-lg z-10 p-2 min-w-72">
            <div className="px-2 py-2.5 border-b mb-2">
              <h3 className="font-medium text-sm">Manage Columns</h3>
            </div>
            {table.getAllLeafColumns().map((column) => {
              const pinningStatus = getPinningStatus(column.id);

              return (
                <div
                  key={column.id}
                  className="flex items-center justify-between px-2 py-2.5 hover:bg-muted border-b border-border"
                >
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={column.getIsVisible()}
                      onChange={column.getToggleVisibilityHandler()}
                      className="h-4 w-4"
                    />
                    <label className="text-sm">
                      {column.columnDef.header as string}
                    </label>
                  </div>

                  <div className="flex items-center">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        pinColumn(
                          column.id,
                          pinningStatus === "left" ? null : "left"
                        );
                      }}
                      title="Pin to left"
                      className={`p-1 rounded ${
                        pinningStatus === "left"
                          ? " text-blue-600"
                          : "text-gray-500 hover:bg-blue-200"
                      }`}
                    >
                      <Pin size={14} />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        pinColumn(
                          column.id,
                          pinningStatus === "right" ? null : "right"
                        );
                      }}
                      title="Pin to right"
                      className={`p-1 rounded ${
                        pinningStatus === "right"
                          ? " text-blue-600"
                          : "text-gray-500 hover:bg-blue-200"
                      }`}
                    >
                      <Pin size={14} />
                    </button>

                    {pinningStatus && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          pinColumn(column.id, null);
                        }}
                        title="Unpin"
                        className="p-1 rounded text-gray-500 hover:bg-blue-200"
                      >
                        <X size={14} />
                      </button>
                    )}
                  </div>
                </div>
              );
            })}

            <div className="flex justify-between  py-2 ">
              <div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    table.getAllLeafColumns().forEach((column) => {
                      column.toggleVisibility(true);
                    });
                  }}
                  className="text-xs text-blue-600 px-2  cursor-pointer"
                >
                  Show All
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Hide all except select column and at least one data column
                    const toHide: VisibilityState = {};
                    table.getAllLeafColumns().forEach((column) => {
                      if (column.id !== "select" && column.id !== "firstName") {
                        toHide[column.id] = false;
                      }
                    });
                    setColumnVisibility(toHide);
                  }}
                  className="text-xs text-blue-600 px-2 py-1 cursor-pointer"
                >
                  Hide All
                </button>
              </div>
              <div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setColumnPinning({
                      left: ["select"],
                      right: [],
                    });
                  }}
                  className="text-xs text-blue-600 px-2 py-1 cursor-pointer"
                >
                  Reset Pins
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownOpen && !(e.target as Element).closest(".relative")) {
        setDropdownOpen(false);
      }
    };

    window.addEventListener("click", handleClickOutside);
    return () => {
      window.removeEventListener("click", handleClickOutside);
    };
  }, [dropdownOpen]);

  {
    /* ############################################################################################################### */
  }
  // Selection modal component
  const SelectionModal = () => {
    const selectedRowsCount = Object.keys(rowSelection).length;

    if (selectedRowsCount === 0) return null;

    return (
      <div className="fixed bottom-6 right-6 bg-muted rounded-lg shadow-2xl border p-4 z-50 animate-fade-in">
        <div
          className={`flex flex-row gap-24 ${
            !enableSelectToolbarButtonExportToExcel &&
            !enableSelectToolbarButtonPrintToPdf
              ? "gap-4"
              : ""
          }`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>{selectedRowsCount}</span>
              <span>{selectedRowsCount === 1 ? "row" : "rows"} selected</span>
            </div>
          </div>

          <div className="flex gap-3">
            {enableSelectToolbarButtonExportToExcel && (
              <button
                onClick={exportSelectedToExcel}
                className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                <Download size={14} />
                <span>Export Excel</span>
              </button>
            )}
            {enableSelectToolbarButtonPrintToPdf && (
              <button
                onClick={printSelectedToPdf}
                className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                <Printer size={14} />
                <span>Print PDF</span>
              </button>
            )}
            <button
              onClick={() => setRowSelection({})}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={22} />
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Add CSS animation for the modal
  useEffect(() => {
    const style = document.createElement("style");
    style.innerHTML = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .animate-fade-in {
                animation: fadeIn 0.3s ease-out forwards;
            }
        `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  {
    /* ############################################################################################################### */
  }
  return (
    <div
      ref={tableContainerRef}
      className={`transition-all duration-300 ${
        isFullScreen ? "bg-background p-6" : "p-0"
      } ${containerClassName}`}
    >
      {enableToolbar && (
        <div className="mb-2 flex  items-center gap-6 md:gap-4">
          <div className="grow ">
            {/* <input
                            value={globalFilter ?? ''}
                            onChange={e => setGlobalFilter(e.target.value)}
                            className="px-4 py-2 w-full border rounded-md  focus:outline-none"
                            placeholder="Search table..."
                        /> */}
            {searchInput}
          </div>

          <div className="hidden ml-auto md:flex items-center gap-2">
            {enableColumnControl && <ColumnManagementDropdown />}

            {customButtons}

            {/* Export to Excel button */}
            {enableExportToExcel && (
              <button
                onClick={exportToExcel}
                className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-md shadow-sm text-sm hover:bg-green-700"
              >
                <Download size={16} />
                <span>Export to Excel</span>
              </button>
            )}

            {/* Print to PDF button  */}
            {enablePrintPdf && (
              <button
                onClick={printToPdf}
                className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm hover:bg-blue-700"
              >
                <Printer size={16} />
                <span>Print PDF</span>
              </button>
            )}
            {enableFullScreenToggle && (
              <button
                onClick={toggleFullScreen}
                className="p-2 rounded-full hover:bg-gray-100 focus:outline-none"
                title={isFullScreen ? "Exit Fullscreen" : "Enter Fullscreen"}
              >
                {isFullScreen ? <Minimize size={20} /> : <Maximize size={20} />}
              </button>
            )}
          </div>

          {/* Mobile dropdown menu (visible on smaller screens <800px) */}
          {enableColumnControl ||
          enableExportToExcel ||
          enablePrintPdf ||
          enableFullScreenToggle ? (
            <div className="md:hidden flex items-center gap-5">
              {enableColumnControl && <ColumnManagementDropdown />}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button>
                    <Menu size={24} />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  {enableExportToExcel && (
                    <DropdownMenuItem onClick={exportToExcel}>
                      <span className="flex items-center gap-2 py-2">
                        <Download size={16} />
                        Export to Excel
                      </span>
                    </DropdownMenuItem>
                  )}
                  {enablePrintPdf && (
                    <DropdownMenuItem onClick={printToPdf}>
                      <span className="flex items-center gap-2 py-2">
                        <Printer size={16} />
                        Print PDF
                      </span>
                    </DropdownMenuItem>
                  )}
                  {customButtons}
                  {enableFullScreenToggle && (
                    <DropdownMenuItem onClick={toggleFullScreen}>
                      <span className="flex items-center gap-2 py-2">
                        {isFullScreen ? (
                          <Minimize size={16} />
                        ) : (
                          <Maximize size={16} />
                        )}
                        {isFullScreen ? "Exit Fullscreen" : "Enter Fullscreen"}
                      </span>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            ""
          )}
        </div>
      )}

      {/* ############################################################################################################### */}
      {/* main table  */}
      <div
        className={`rounded-md table-mobile-scroll max-w-full ${
          isFullScreen ? "h-[calc(100vh-180px)]" : ""
        }`}
      >
        <table
          className={`w-full border border-border table-auto ${tableClassName}`}
          style={{ minWidth: 'max-content' }}
        >
          <thead className={` min-h-10 ${tHeadClassName}`}>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const isPinned = getPinningStatus(header.column.id);

                  return (
                    <th
                      key={header.id}
                      className={`px-1 py-3 text-left text-xs font-medium text-foreground tracking-wider border-b min-w-[120px]
                                            ${
                                              isPinned === "left"
                                                ? "sticky left-0 bg-muted"
                                                : isPinned === "right"
                                                ? "sticky right-0 bg-muted"
                                                : ""
                                            } ${header.id == "select" ? "w-10 min-w-[40px]" : ""} ${tHeadCellsClassName}`}
                    >
                      {header.isPlaceholder ? null : (
                        <>
                          <div
                            className={`flex justify-between items-center whitespace-nowrap ${
                              header.column.getCanSort()
                                ? "cursor-pointer select-none"
                                : ""
                            } ${header.id == "select" ? "w-10" : ""}`}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {{
                              asc: <ChevronUp size={15} />,
                              desc: <ChevronDown size={15} />,
                            }[header.column.getIsSorted() as string] ?? null}
                            {header.column.getCanSort() &&
                            (header.column.getIsSorted() as string) == "" ? (
                              <ChevronsUpDown size={16} />
                            ) : null}
                          </div>
                          {/* column filters */}
                          {header.column.getCanFilter() ? (
                            <div className="mt-2">
                              {renderColumnFilter(header.column)}
                            </div>
                          ) : null}
                        </>
                      )}
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
          <tbody className={`divide-y divide-gray-200 ${tBodyClassName}`}>
            {table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className={`hover:bg-muted/20 cursor-pointer ${
                  row.getIsSelected() ? "" : ""
                } ${tBodyTrClassName}`}
                onClick={() => handleRowClick(row)}
              >
                {row.getVisibleCells().map((cell) => {
                  const isPinned = getPinningStatus(cell.column.id);
                  return (
                    <td
                      key={cell.id}
                      className={`px-1 py-4 text-sm text-foreground border-b whitespace-nowrap min-w-[120px] ${
                        isPinned === "left"
                          ? "sticky left-0 bg-muted"
                          : isPinned === "right"
                          ? "sticky right-0 bg-muted"
                          : ""
                      } ${row.getIsSelected() && isPinned ? "" : ""}
                                                ${
                                                  cell.column.id == "select"
                                                    ? "w-10 min-w-[40px]"
                                                    : ""
                                                }
                                                ${tBodyCellsClassName}`}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* ############################################################################################################### */}
      {/* go to page  */}
      {1 > 2 && (
        <div className="mt-4 flex items-center justify-between">
          <div>
            <select
              value={table.getState().pagination.pageSize}
              onChange={(e) => {
                table.setPageSize(Number(e.target.value));
              }}
              className="p-2 border rounded"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  Show {pageSize}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-3">
            <button
              className="p-2 rounded border border-border disabled:opacity-50"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              {"<<"}
            </button>
            <button
              className="p-2 rounded border disabled:opacity-50"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              {"<"}
            </button>
            <span>
              Page{" "}
              <strong>
                {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </strong>
            </span>
            <button
              className="p-2 rounded border disabled:opacity-50"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              {">"}
            </button>
            <button
              className="p-2 rounded border disabled:opacity-50"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              {">>"}
            </button>
          </div>
          <div className="hidden md:flex items-center">
            Go to page:
            <input
              type="number"
              defaultValue={table.getState().pagination.pageIndex + 1}
              onChange={(e) => {
                const page = e.target.value ? Number(e.target.value) - 1 : 0;
                table.setPageIndex(page);
              }}
              className="w-16 ml-2 p-2 border rounded"
            />
          </div>
        </div>
      )}

      {/* Selection Modal */}
      {enableSelectToolbar && <SelectionModal />}

      {enablePagination && (
        <Paginator
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={totalItems}
          itemsPerPageOptions={itemsPerPageOptions}
          className={paginationClassName}
        />
      )}
    </div>
  );
}

// Custom date range filter function
export const dateRangeFilter: FilterFn<DataRecord | any> = (
  row,
  columnId,
  filterValue: DateRange | undefined
) => {
  if (!filterValue || (!filterValue.from && !filterValue.to)) return true;

  const rowValue = row.getValue(columnId) as Date;
  const rowDate = new Date(rowValue);

  // If only "from" date is set
  if (filterValue.from && !filterValue.to) {
    return rowDate >= filterValue.from;
  }

  // If only "to" date is set
  if (!filterValue.from && filterValue.to) {
    return rowDate <= filterValue.to;
  }

  // If both dates are set
  if (filterValue.from && filterValue.to) {
    return rowDate >= filterValue.from && rowDate <= filterValue.to;
  }

  return true;
};

// Date range picker filter component
export function DateRangeFilter({ column }: { column: any }) {
  const [date, setDate] = useState<DateRange | undefined>(
    column.getFilterValue()
  );

  return (
    <div>
      <Popover>
        <PopoverTrigger asChild className="hover:!bg-none">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              className="w-full text-[11px] hover:!bg-transparent hover:text-muted-foreground justify-start text-left font-normal h-[18px]  !p-0 border-b border-foreground/40 focus:outline-none rounded-none whitespace-nowrap overflow-hidden text-ellipsis"
              size="sm"
            >
              {date?.from ? (
                date.to ? (
                  <>
                    {format(date.from, "LLL dd, y")} -{" "}
                    {format(date.to, "LLL dd, y")}
                  </>
                ) : (
                  format(date.from, "LLL dd, y")
                )
              ) : (
                <></>
                // <span className="text-xs text-muted-foreground ">Filter by date...</span>
              )}
            </Button>
            <CalendarIcon size={20} className="hidden md:inline" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 " align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={(selectedDate) => {
              setDate(selectedDate);
              column.setFilterValue(selectedDate);
            }}
            numberOfMonths={1}
          />
          <div className="flex items-center justify-between px-3 pb-2  hover:!bg-none">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setDate(undefined);
                column.setFilterValue(undefined);
              }}
            >
              Clear
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

// column filters
function ColumnFilter({ column }: { column: any }) {
  const columnFilterValue = column.getFilterValue();

  return (
    <div className="flex items-center gap-2 pr-0 md:pr-2">
      <input
        value={columnFilterValue}
        onChange={(e) => column.setFilterValue(e.target.value)}
        className="w-full !bg-transparent text-[11px] border-b border-foreground/40 focus:outline-none"
      />
      <Search size={17} className="translate-y-0.5 hidden md:inline" />
    </div>
  );
}
