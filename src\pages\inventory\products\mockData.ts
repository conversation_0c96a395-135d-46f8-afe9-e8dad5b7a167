import { Product, Category, UnitOfMeasure, Supplier } from './product.type';

export const mockCategories: Category[] = [
  { id: '1', name: 'Beverages', description: 'All types of drinks and beverages' },
  { id: '2', name: 'Meat', description: 'Fresh and processed meat products' },
  { id: '3', name: 'Seafood', description: 'Fresh and frozen seafood' },
  { id: '4', name: 'Dairy', description: 'Milk, cheese, and dairy products' },
  { id: '5', name: 'Baking', description: 'Baking ingredients and supplies' },
  { id: '6', name: 'Cooking Oils', description: 'Various cooking oils and fats' },
  { id: '7', name: 'Consumables', description: 'Disposable items and supplies' },
  { id: '8', name: 'Packaging', description: 'Food packaging materials' },
  { id: '9', name: 'Spices & Seasonings', description: 'Herbs, spices, and seasonings' },
  { id: '10', name: 'Vegetables', description: 'Fresh and frozen vegetables' }
];

export const mockUnitsOfMeasure: UnitOfMeasure[] = [
  { id: '1', name: 'Kilogram', abbreviation: 'kg', type: 'weight' },
  { id: '2', name: 'Gram', abbreviation: 'g', type: 'weight' },
  { id: '3', name: 'Pound', abbreviation: 'lb', type: 'weight' },
  { id: '4', name: 'Liter', abbreviation: 'L', type: 'volume' },
  { id: '5', name: 'Milliliter', abbreviation: 'ml', type: 'volume' },
  { id: '6', name: 'Gallon', abbreviation: 'gal', type: 'volume' },
  { id: '7', name: 'Piece', abbreviation: 'pc', type: 'count' },
  { id: '8', name: 'Pack', abbreviation: 'pack', type: 'count' },
  { id: '9', name: 'Bottle', abbreviation: 'btl', type: 'count' },
  { id: '10', name: 'Box', abbreviation: 'box', type: 'count' },
  { id: '11', name: 'Dozen', abbreviation: 'doz', type: 'count' },
  { id: '12', name: 'Case', abbreviation: 'case', type: 'count' }
];

export const mockSuppliers: Supplier[] = [
  { id: '1', name: 'Fresh Foods Ltd', contactPerson: 'John Smith', email: '<EMAIL>', phone: '******-0101' },
  { id: '2', name: 'Quality Meats Co', contactPerson: 'Sarah Johnson', email: '<EMAIL>', phone: '******-0102' },
  { id: '3', name: 'Ocean Fresh Seafood', contactPerson: 'Mike Wilson', email: '<EMAIL>', phone: '******-0103' },
  { id: '4', name: 'Dairy Direct', contactPerson: 'Lisa Brown', email: '<EMAIL>', phone: '******-0104' },
  { id: '5', name: 'Packaging Solutions', contactPerson: 'David Lee', email: '<EMAIL>', phone: '******-0105' },
  { id: '6', name: 'Spice World', contactPerson: 'Anna Garcia', email: '<EMAIL>', phone: '******-0106' }
];

export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Bottled Water',
    code: 'BW001',
    category: 'Beverages',
    unitOfMeasure: 'Bottle',
    cost: 125,
    taxCode: 'VAT',
    perishable: false,
    defaultSupplier: 'Fresh Foods Ltd',
    active: true,
    stockLevel: 150,
    minStockLevel: 50,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Premium Ground Beef',
    code: 'PGB002',
    category: 'Meat',
    unitOfMeasure: 'Pound',
    cost: 899,
    taxCode: 'No VAT',
    perishable: true,
    shelfLifeDays: 3,
    defaultSupplier: 'Quality Meats Co',
    active: true,
    stockLevel: 25,
    minStockLevel: 10,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '3',
    name: 'Organic Sugar',
    code: 'OS003',
    category: 'Baking',
    unitOfMeasure: 'Pound',
    cost: 249,
    taxCode: 'No VAT',
    perishable: false,
    defaultSupplier: 'Fresh Foods Ltd',
    active: true,
    stockLevel: 45,
    minStockLevel: 20,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '4',
    name: 'Paper Napkins',
    code: 'PN004',
    category: 'Consumables',
    unitOfMeasure: 'Pack',
    cost: 1299,
    taxCode: 'VAT',
    perishable: false,
    defaultSupplier: 'Packaging Solutions',
    active: true,
    stockLevel: 8,
    minStockLevel: 15,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '5',
    name: 'Fresh Salmon Fillet',
    code: 'FSF005',
    category: 'Seafood',
    unitOfMeasure: 'Pound',
    cost: 1599,
    taxCode: 'No VAT',
    perishable: true,
    shelfLifeDays: 2,
    defaultSupplier: 'Ocean Fresh Seafood',
    active: true,
    stockLevel: 12,
    minStockLevel: 8,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '6',
    name: 'Olive Oil Extra Virgin',
    code: 'OOEV006',
    category: 'Cooking Oils',
    unitOfMeasure: 'Liter',
    cost: 1850,
    taxCode: 'No VAT',
    perishable: false,
    defaultSupplier: 'Fresh Foods Ltd',
    active: false,
    stockLevel: 5,
    minStockLevel: 10,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '7',
    name: 'Disposable Plates',
    code: 'DP007',
    category: 'Consumables',
    unitOfMeasure: 'Pack',
    cost: 875,
    taxCode: 'VAT',
    perishable: false,
    defaultSupplier: 'Packaging Solutions',
    active: true,
    stockLevel: 22,
    minStockLevel: 12,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: '8',
    name: 'Whole Milk',
    code: 'WM008',
    category: 'Dairy',
    unitOfMeasure: 'Gallon',
    cost: 429,
    taxCode: 'No VAT',
    perishable: true,
    shelfLifeDays: 7,
    defaultSupplier: 'Dairy Direct',
    active: true,
    stockLevel: 18,
    minStockLevel: 15,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];