import React from "react";
import { Button } from "@/components/ui/button";
import { Plus, Merge, Filter, Calendar } from "lucide-react";

interface GuestChecksHeaderProps {
  onNewCheckIn: () => void;
}

export const GuestChecksHeader: React.FC<GuestChecksHeaderProps> = ({ onNewCheckIn }) => (
  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
    <div>
      <h1 className="text-4xl font-bold bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 bg-clip-text text-transparent mb-2">
        Guest Checks
      </h1>
      <p className="text-gray-600 flex items-center">
        <Calendar className="h-4 w-4 mr-2" />
        Today, {new Date().toLocaleDateString()}
      </p>
    </div>
    <div className="flex flex-wrap gap-3">
      <Button
        className="bg-gradient-to-r from-red-500 via-yellow-400 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-300"
        onClick={onNewCheckIn}
      >
        <Plus className="h-4 w-4 mr-2" />
        New Check-in
      </Button>
      <Button
        variant="outline"
        className="hover:bg-yellow-50 hover:border-yellow-300 transition-all duration-300"
      >
        <Merge className="h-4 w-4 mr-2" />
        Merge Checks
      </Button>
      <Button
        variant="outline"
        className="hover:bg-orange-50 hover:border-orange-300 transition-all duration-300"
      >
        <Filter className="h-4 w-4 mr-2" />
        Filter
      </Button>
    </div>
  </div>
);