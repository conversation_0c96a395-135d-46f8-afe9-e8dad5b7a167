import React, { useState } from "react";
import { Coffee, Utensils, Clock, Star } from "lucide-react";
import { MealModal } from "./MealModal";


export interface Meal {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  rating?: number;
  prepTime?: string;
  isAvailable?: boolean;
  workstation?: string;
}

interface Order {
  mealId: string;
  title: string;
  quantity: number;
  workstation: string;
}

// Mock data for meals
const mockMeals: Meal[] = [
  {
    title: "Breakfast",
    description: "Start your day with energy.",
    imageUrl:
      "https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80",
    price: "$8.50",
    category: "Mains",
    rating: 4.8,
    prepTime: "5 mins",
    isAvailable: true,
    workstation: "Kitchen",
  },
  {
    title: "Beverages",
    description: "Refreshing drinks for every mood.",
    imageUrl:
      "https://images.unsplash.com/photo-1510626176961-4b57d4fbad04?auto=format&fit=crop&w=400&q=80",
    price: "$3.50",
    category: "Beverages",
    rating: 4.7,
    prepTime: "3 mins",
    isAvailable: true,
    workstation: "Bar",
  },
];

interface MealCardProps {
  title: string;
  description: string;
  imageUrl: string;
  price?: string;
  category?: string;
  rating?: number;
  prepTime?: string;
  isAvailable?: boolean;
  workstation?: string;
  onAddToCart?: (order: Order) => void;
  onViewDetails?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  onClick?: () => void;
  className?: string;
  onToggleAvailability?: (title: string, isAvailable: boolean) => void;
}

export function MealCard({
  title,
  description,
  imageUrl,
  price = "$8.50",
  category = "Mains",
  rating = 4.8,
  prepTime = "5 mins",
  isAvailable = true,
  workstation = "Kitchen",
  onAddToCart,
  onViewDetails,
  onClick,
  className,
  onToggleAvailability,
}: MealCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMeals, setModalMeals] = useState<Meal[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [selectedWorkstation, setSelectedWorkstation] = useState(workstation);

  const handleOpenModal = (meals: Meal[]) => {
    setModalMeals(meals);
    setIsModalOpen(true);
  };

  const handleViewDetails = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setModalMeals([{ title, description, imageUrl, price, category, rating, prepTime, isAvailable, workstation }]);
    setIsModalOpen(true);
    onViewDetails && onViewDetails(e);
  };

  const handleAddToCart = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (!isAvailable) {
      alert(`${title} is currently unavailable.`);
      return;
    }
    const order: Order = {
      mealId: title, // Using title as ID for simplicity; use unique ID in production
      title,
      quantity,
      workstation: selectedWorkstation,
    };
    onAddToCart && onAddToCart(order);
    console.log(`Order sent to ${selectedWorkstation} printer:`, order);
    alert(`Ordered ${quantity} ${title} to ${selectedWorkstation}`);
  };

  const handleToggleAvailability = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onToggleAvailability && onToggleAvailability(title, !isAvailable);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setQuantity(1);
    setSelectedWorkstation(workstation);
  };

  return (
    <>
      <div
        className={`
          group relative overflow-hidden
          bg-gradient-to-br from-white to-gray-50
          border-0 shadow-sm hover:shadow-xl
          transition-all duration-300 ease-out
          cursor-pointer
          backdrop-blur-sm
          hover:scale-[1.02] hover:-translate-y-1
          rounded-xl
          ${isAvailable ? "" : "opacity-60"}
          ${className || ""}
        `}
        onClick={onClick}
        tabIndex={0}
        role="button"
        aria-label={`Open ${title} details`}
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <div className="relative flex items-center p-3 space-x-3">
          {/* Compact image */}
          <div className="relative flex-shrink-0 w-16 h-16 rounded-xl overflow-hidden">
            <img
              src={imageUrl}
              alt={title}
              className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-black/10 group-hover:bg-black/0 transition-colors duration-300" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm truncate group-hover:text-orange-600 transition-colors duration-200">
              {title}
            </h3>
            <p className="text-xs text-gray-500 mt-0.5 line-clamp-2 leading-relaxed">
              {description}
            </p>
            <div className="flex items-center space-x-2 mt-1 text-xs text-gray-600">
              <span className="flex items-center">
                <Utensils className="w-3 h-3 mr-1" />
                {category}
              </span>
              <span className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {prepTime}
              </span>
              <span className="flex items-center">
                <Star className="w-3 h-3 mr-1 fill-yellow-400" />
                {rating}
              </span>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex-shrink-0 flex space-x-1">
            <button
              onClick={handleAddToCart}
              disabled={!onAddToCart || !isAvailable}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-gradient-to-r from-orange-500 to-red-500
                text-white rounded-lg
                hover:from-orange-600 hover:to-red-600
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-200
                hover:shadow-lg hover:shadow-orange-500/25
                transform hover:scale-105
              "
              aria-label={`Add ${title} to cart`}
            >
              Add
            </button>

            <button
              onClick={handleViewDetails}
              className="
                px-3 py-1.5 text-xs font-medium
                bg-white border border-gray-200
                text-gray-700 rounded-lg
                hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50
                hover:border-orange-300
                transition-all duration-200
                hover:shadow-md
                transform hover:scale-105
              "
              aria-label={`View details for ${title}`}
            >
              View
            </button>

            {/* Availability toggle (admin only, for simplicity) */}
            {onToggleAvailability && (
              <button
                onClick={handleToggleAvailability}
                className={`
                  px-3 py-1.5 text-xs font-medium
                  ${isAvailable ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"}
                  rounded-lg hover:shadow-md
                  transition-all duration-200
                  transform hover:scale-105
                `}
                aria-label={`${isAvailable ? "Mark as unavailable" : "Mark as available"} for ${title}`}
              >
                {isAvailable ? "Disable" : "Enable"}
              </button>
            )}
          </div>
        </div>

        {/* Bottom accent line */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </div>

      {/* Render Modal */}
      {isModalOpen && (
        <MealModal
          meals={modalMeals}
          onClose={closeModal}
          onAddToCart={(order) => {
            onAddToCart && onAddToCart(order);
            console.log(`Order sent to ${order.workstation} printer:`, order);
            alert(`Ordered ${order.quantity} ${order.title} to ${order.workstation}`);
            closeModal();
          }}
          quantity={quantity}
          setQuantity={setQuantity}
          selectedWorkstation={selectedWorkstation}
          setSelectedWorkstation={setSelectedWorkstation}
        />
      )}
    </>
  );
}