import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Building,
  DollarSign,
  Truck,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Crown,
  TrendingUp,
  TrendingDown,
  Package
} from "lucide-react";

interface RFQResponseComparisonProps {
  responses: any[];
}

const RFQResponseComparison = ({ responses }: RFQResponseComparisonProps) => {
  if (responses.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Responses to Compare</h3>
          <p className="text-gray-600">
            At least one response is needed to use the comparison view.
          </p>
        </CardContent>
      </Card>
    );
  }

  // Get all unique products from all responses
  const allProducts = Array.from(
    new Set(
      responses.flatMap(response => 
        response.items?.map((item: any) => item.product_name) || []
      )
    )
  );

  // Find best prices for highlighting
  const getBestPrice = (productName: string) => {
    const prices = responses
      .flatMap(response => 
        response.items?.filter((item: any) => item.product_name === productName) || []
      )
      .map(item => item.unit_price)
      .filter(price => price != null);
    
    return prices.length > 0 ? Math.min(...prices) : null;
  };

  const getBestDeliveryTime = () => {
    const deliveryTimes = responses
      .map(response => response.delivery_time_days)
      .filter(time => time != null);
    
    return deliveryTimes.length > 0 ? Math.min(...deliveryTimes) : null;
  };

  const getLowestTotal = () => {
    const totals = responses
      .map(response => response.total_value)
      .filter(total => total != null);
    
    return totals.length > 0 ? Math.min(...totals) : null;
  };

  const bestDeliveryTime = getBestDeliveryTime();
  const lowestTotal = getLowestTotal();

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      "Received": { variant: "secondary" as const, className: "bg-green-100 text-green-800" },
      "Pending": { variant: "secondary" as const, className: "bg-yellow-100 text-yellow-800" },
      "Disqualified": { variant: "secondary" as const, className: "bg-red-100 text-red-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig["Pending"];
    return <Badge variant={config.variant} className={config.className}>{status}</Badge>;
  };

  const getCompletenessBadge = (isComplete: boolean, hasDiscrepancies: boolean) => {
    if (hasDiscrepancies) {
      return (
        <Badge variant="secondary" className="bg-red-100 text-red-800 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          Issues
        </Badge>
      );
    }
    
    if (isComplete) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Complete
        </Badge>
      );
    }
    
    return (
      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 flex items-center gap-1">
        <XCircle className="h-3 w-3" />
        Incomplete
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Summary Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Response Summary Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Completeness</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Delivery Time</TableHead>
                  <TableHead>Payment Terms</TableHead>
                  <TableHead>Submitted</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {responses.map((response: any) => (
                  <TableRow key={response.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">{response.supplier_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(response.status)}</TableCell>
                    <TableCell>
                      {getCompletenessBadge(response.is_complete, response.has_discrepancies)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {response.total_value === lowestTotal && lowestTotal !== null && (
                          <Crown className="h-4 w-4 text-yellow-500" />
                        )}
                        <span className={response.total_value === lowestTotal ? "font-bold text-green-600" : ""}>
                          {response.currency} {response.total_value?.toLocaleString() || "N/A"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {response.delivery_time_days === bestDeliveryTime && bestDeliveryTime !== null && (
                          <Crown className="h-4 w-4 text-yellow-500" />
                        )}
                        <span className={response.delivery_time_days === bestDeliveryTime ? "font-bold text-green-600" : ""}>
                          {response.delivery_time_days ? `${response.delivery_time_days} days` : "N/A"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{response.payment_terms || "N/A"}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(response.submitted_at).toLocaleDateString()}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Item-by-Item Comparison */}
      {allProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Item-by-Item Price Comparison</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {allProducts.map((productName) => {
                const bestPrice = getBestPrice(productName);
                
                return (
                  <div key={productName} className="border rounded-lg p-4">
                    <h4 className="font-medium text-lg mb-4 flex items-center gap-2">
                      <Package className="h-5 w-5" />
                      {productName}
                    </h4>
                    
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Supplier</TableHead>
                            <TableHead>Unit Price</TableHead>
                            <TableHead>Delivery Time</TableHead>
                            <TableHead>Notes</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {responses.map((response: any) => {
                            const item = response.items?.find((item: any) => item.product_name === productName);
                            
                            if (!item) {
                              return (
                                <TableRow key={response.id}>
                                  <TableCell>{response.supplier_name}</TableCell>
                                  <TableCell colSpan={3} className="text-gray-500 italic">
                                    No quote provided
                                  </TableCell>
                                </TableRow>
                              );
                            }
                            
                            return (
                              <TableRow key={response.id}>
                                <TableCell>{response.supplier_name}</TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    {item.unit_price === bestPrice && bestPrice !== null && (
                                      <Crown className="h-4 w-4 text-yellow-500" />
                                    )}
                                    <span className={item.unit_price === bestPrice ? "font-bold text-green-600" : ""}>
                                      {response.currency} {item.unit_price?.toLocaleString() || "N/A"}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {item.delivery_time_days ? `${item.delivery_time_days} days` : "N/A"}
                                </TableCell>
                                <TableCell>
                                  <div className="text-sm text-gray-600">
                                    {item.notes || "-"}
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Analysis Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <TrendingDown className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">
                {lowestTotal ? `${responses[0]?.currency} ${lowestTotal.toLocaleString()}` : "N/A"}
              </div>
              <div className="text-sm text-gray-600">Lowest Total Value</div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Truck className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">
                {bestDeliveryTime ? `${bestDeliveryTime} days` : "N/A"}
              </div>
              <div className="text-sm text-gray-600">Fastest Delivery</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <CheckCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">
                {responses.filter(r => r.is_complete && !r.has_discrepancies).length}
              </div>
              <div className="text-sm text-gray-600">Complete Responses</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RFQResponseComparison;
