import "./footer.css";
import footerMimage from "./footer2.jpg";
import { Screen } from "@/app-components/layout/screen";


type footerProps = {
  company?: string;
  subCompany?: string;
  version?: string;
  system?: string;
}

export default function Footer({
  company = "GMC",
  subCompany = "GMC",
  version = "2.0",
  system = "GMC-POS"
}: footerProps) {
  return (
    <Screen>
      <div className="min-h-screen flex flex-col">
        <div className="body flex-1"></div>
        <footer className="footer flex flex-col h-[50px] w-full relative">
          {/* Footer Background Image */}
          <div className="w-full h-full overflow-hidden relative">
            <img
              src={footerMimage}
              alt="GMC Logo"
              className="h-full w-full object-fill filter grayscale"
            />
            <div className="absolute w-full inset-0 h-full bg-primary/100 mix-blend-color"></div>
          </div>
          {/* Footer Overlay Content */}
          <div className="footer-content animate-footer-pulse absolute inset-0 backdrop-blur-xl bg-primary/30 dark:bg-slate-950/70 w-full h-full px-6 py-4 flex flex-col justify-center gap-2 text-slate-800 dark:text-slate-200 text-sm">
            {/* Optional: Socials or links row could go here */}

            {/* Made with flame */}
            <div className="text-center text-xs text-slate-500 dark:text-slate-400">
              Made with <span className="inline-block animate-pulse">🔥</span>
            </div>

            {/* Bottom reserved text */}
            <div className="footer-bottom text-center text-xs text-slate-500 dark:text-slate-400">
              © {new Date().getFullYear()} {company}. All rights reserved. {" "}
              <span className="font-medium">
                {subCompany} {system} {" "}
                <span className="version text-xs font-mono text-slate-500 dark:text-slate-400">
                  v{version}
                </span>
              </span>
            </div>
          </div>
        </footer>
      </div>
    </Screen>
  );
}
