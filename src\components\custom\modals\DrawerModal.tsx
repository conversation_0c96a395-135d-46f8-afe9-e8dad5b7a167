import  { forwardRef } from "react";
import { X } from "lucide-react";
import { 
  Sheet,
  Sheet<PERSON>ontent,
  SheetTitle,
  SheetDescription
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { BaseModalProps } from "./BaseModal";

export interface DrawerModalProps extends BaseModalProps {
    side?: "left" | "right" | "top" | "bottom";
  }


const DrawerModal = forwardRef<HTMLDivElement, DrawerModalProps>(
  (
    {
      isOpen,
      onOpenChange,
      side = "right",
      title,
      description,
      className,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <Sheet open={isOpen} onOpenChange={onOpenChange} {...props}>
        <SheetContent
          ref={ref}
          side={side}
          className={cn(
            "p-0 gap-0 bg-background focus:outline-none",
            className
          )}
        >
          <div className="p-5 border-b sticky top-0 bg-background z-10 flex justify-between items-center">
            <div>
              {title && <SheetTitle>{title}</SheetTitle>}
              {description && <SheetDescription>{description}</SheetDescription>}
            </div>
            <button
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={() => onOpenChange?.(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          <div className="p-5 overflow-auto">
            {children}
          </div>
        </SheetContent>
      </Sheet>
    );
  }
);

DrawerModal.displayName = "DrawerModal";
export default DrawerModal;