import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Plus, Edit, Trash2, Search, Filter, Save, X } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Define the schema for SubGroup validation
const subGroupSchema = z.object({
  name: z.string().min(1, "Subgroup name is required"),
  description: z.string().optional(),
  parentGroup: z.string().min(1, "Parent group is required"),
  sortOrder: z.number().min(0, "Sort order must be positive"),
  isActive: z.boolean().default(true),
  color: z.string().optional(),
  icon: z.string().optional(),
});

type SubGroupFormData = z.infer<typeof subGroupSchema>;

interface SubGroupItem {
  id: string;
  name: string;
  description?: string;
  parentGroup: string;
  sortOrder: number;
  isActive: boolean;
  color?: string;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
}

function SubGroup() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<SubGroupItem | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const { toast } = useToast();

  // Mock data - Replace with actual API call
  const [subGroups, setSubGroups] = useState<SubGroupItem[]>([
    {
      id: "1",
      name: "Appetizers",
      description: "Light dishes served before the main course",
      parentGroup: "Food Items",
      sortOrder: 1,
      isActive: true,
      color: "#ff0000",
      icon: "🥗",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "2",
      name: "Main Course",
      description: "Primary dishes of the meal",
      parentGroup: "Food Items",
      sortOrder: 2,
      isActive: true,
      color: "#ffff00",
      icon: "🍽️",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "3",
      name: "Beverages",
      description: "Drinks and liquid refreshments",
      parentGroup: "Drinks",
      sortOrder: 3,
      isActive: false,
      color: "#000000",
      icon: "🥤",
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ]);

  const form = useForm<SubGroupFormData>({
    resolver: zodResolver(subGroupSchema),
    defaultValues: {
      name: "",
      description: "",
      parentGroup: "",
      sortOrder: 0,
      isActive: true,
      color: "",
      icon: "",
    },
  });

  const handleOpenModal = (item?: SubGroupItem) => {
    if (item) {
      setEditingItem(item);
      form.reset({
        name: item.name,
        description: item.description || "",
        parentGroup: item.parentGroup,
        sortOrder: item.sortOrder,
        isActive: item.isActive,
        color: item.color || "",
        icon: item.icon || "",
      });
    } else {
      setEditingItem(null);
      form.reset({
        name: "",
        description: "",
        parentGroup: "",
        sortOrder: subGroups.length + 1,
        isActive: true,
        color: "",
        icon: "",
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingItem(null);
    form.reset();
  };

  const handleSubmit = async (data: SubGroupFormData) => {
    try {
      if (editingItem) {
        // Update existing subgroup
        const updatedSubGroups = subGroups.map(item =>
          item.id === editingItem.id 
            ? { ...item, ...data, updatedAt: new Date() }
            : item
        );
        setSubGroups(updatedSubGroups);
        
        toast({
          title: "Success",
          description: "Subgroup updated successfully",
          className: "border-l-4 border-l-primary bg-card text-card-foreground",
        });
      } else {
        // Create new subgroup
        const newSubGroup: SubGroupItem = {
          id: Date.now().toString(),
          ...data,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        setSubGroups([...subGroups, newSubGroup]);
        
        toast({
          title: "Success",
          description: "Subgroup created successfully",
          className: "border-l-4 border-l-primary bg-card text-card-foreground",
        });
      }
      
      handleCloseModal();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save subgroup",
        variant: "destructive",
      });
    }
  };

  const handleDelete = (id: string) => {
    setSubGroups(subGroups.filter(item => item.id !== id));
    toast({
      title: "Deleted",
      description: "Subgroup deleted successfully",
      className: "border-l-4 border-l-destructive bg-card text-card-foreground",
    });
  };

  const filteredSubGroups = subGroups.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterActive === null || item.isActive === filterActive;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="p-6 space-y-6 bg-background min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Menu Subgroups</h1>
          <p className="text-muted-foreground mt-1">
            Manage your menu subgroups and organize your menu items
          </p>
        </div>
        <Button
          onClick={() => handleOpenModal()}
          className="bg-primary hover:bg-primary/90 text-primary-foreground"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Subgroup
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search subgroups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select
          value={filterActive === null ? "all" : filterActive.toString()}
          onValueChange={(value) => 
            setFilterActive(value === "all" ? null : value === "true")
          }
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="true">Active</SelectItem>
            <SelectItem value="false">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Subgroups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSubGroups.map((item) => (
          <Card key={item.id} className="border-border hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {item.icon && <span className="text-xl">{item.icon}</span>}
                  <CardTitle className="text-lg text-foreground">{item.name}</CardTitle>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={item.isActive ? "default" : "secondary"}
                    className={cn(
                      item.isActive 
                        ? "bg-primary text-primary-foreground" 
                        : "bg-secondary text-secondary-foreground"
                    )}
                  >
                    {item.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">{item.description}</p>
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Parent: {item.parentGroup}</span>
                  <span>Order: {item.sortOrder}</span>
                </div>
                {item.color && (
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded-full border border-border"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-xs text-muted-foreground">{item.color}</span>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleOpenModal(item)}
                  className="border-primary/20 hover:bg-primary/10"
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(item.id)}
                  className="border-destructive/20 hover:bg-destructive/10 text-destructive"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredSubGroups.length === 0 && (
        <div className="text-center py-12">
          <div className="text-muted-foreground">
            <p className="text-lg">No subgroups found</p>
            <p className="text-sm mt-1">Try adjusting your search or filter criteria</p>
          </div>
        </div>
      )}

      {/* Modal for Add/Edit Subgroup */}
      <BaseModal
        isOpen={isModalOpen}
        onOpenChange={handleCloseModal}
        title={editingItem ? "Edit Subgroup" : "Add New Subgroup"}
        description={editingItem ? "Update subgroup details" : "Create a new menu subgroup"}
        size="lg"
        showClose={true}
        className="border-border"
      >
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Subgroup Name *</Label>
              <Input
                id="name"
                {...form.register("name")}
                placeholder="Enter subgroup name"
                className="border-border focus:ring-primary"
              />
              {form.formState.errors.name && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="parentGroup">Parent Group *</Label>
              <Select {...form.register("parentGroup")}>
                <SelectTrigger>
                  <SelectValue placeholder="Select parent group" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Food Items">Food Items</SelectItem>
                  <SelectItem value="Drinks">Drinks</SelectItem>
                  <SelectItem value="Desserts">Desserts</SelectItem>
                  <SelectItem value="Specials">Specials</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.parentGroup && (
                <p className="text-sm text-destructive">
                  {form.formState.errors.parentGroup.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Enter subgroup description"
              rows={3}
              className="border-border focus:ring-primary"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sortOrder">Sort Order</Label>
              <Input
                id="sortOrder"
                type="number"
                {...form.register("sortOrder", { valueAsNumber: true })}
                placeholder="0"
                className="border-border focus:ring-primary"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <div className="flex gap-2">
                <Input
                  id="color"
                  type="color"
                  {...form.register("color")}
                  className="w-12 h-10 border-border"
                />
                <Input
                  {...form.register("color")}
                  placeholder="#000000"
                  className="flex-1 border-border focus:ring-primary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="icon">Icon (Emoji)</Label>
              <Input
                id="icon"
                {...form.register("icon")}
                placeholder="🍽️"
                className="border-border focus:ring-primary"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={form.watch("isActive")}
              onCheckedChange={(checked) => form.setValue("isActive", checked)}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCloseModal}
              className="border-border hover:bg-muted"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingItem ? "Update" : "Create"}
            </Button>
          </div>
        </form>
      </BaseModal>
    </div>
  );
}

export default SubGroup;