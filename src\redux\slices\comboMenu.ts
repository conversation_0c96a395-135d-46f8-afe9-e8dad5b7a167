import { apiSlice } from "../apiSlice";

export const comboMenuApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getComboMenus: builder.query({
      query: (params) => ({
        url: "/menu/combo-components",
        method: "GET",
        params: params,
      }),
      providesTags: ["ComboMenu"],
    }),

    retrieveComboMenu: builder.query({
      query: (id) => ({
        url: `/menu/combo-components/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "ComboMenu", id }],
    }),

    addComboMenu: builder.mutation({
      query: (payload) => ({
        url: "/menu/combo-meals",
        method: "POST",
        body: payload,
      }),
      invalidatesTags: ["ComboMenu"],
    }),

    patchComboMenu: builder.mutation({
      query: (payload) => ({
        url: `/menu/combo-components/${payload?.id}`,
        method: "PATCH",
        body: payload,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "ComboMenu", id },
        "ComboMenu",
      ],
    }),

    deleteComboMenu: builder.mutation({
      query: (id) => ({
        url: `/menu/combo-components/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["ComboMenu"],
    }),
  }),
});

export const {
  useGetComboMenusQuery,
  useRetrieveComboMenuQuery,
  useAddComboMenuMutation,
  usePatchComboMenuMutation,
  useDeleteComboMenuMutation,

  useLazyGetComboMenusQuery,
  useLazyRetrieveComboMenuQuery,
} = comboMenuApiSlice;
