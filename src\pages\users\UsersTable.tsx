import { useState, useMemo } from "react";
import { Screen } from "@/app-components/layout/screen";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Eye, Edit, Trash2, Users, Search, KeyRoundIcon } from "lucide-react";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import { Badge } from "@/components/custom/badges/badges";
import { Card6 } from "@/components/custom/cards/Card6";
import { useGetUsersQuery, useDeleteUserMutation } from "@/redux/slices/user";
import { useToast } from "@/hooks/use-toast";
import TooltipTemp from "@/components/custom/tooltip/TooltipTemp";

import EditUserModal from "./EditUserModal";
import ViewUserModal from "./ViewUser";
import DeleteUserModal from "./DeleteUserModal";
import UserPermissionsModal from "./UserPermissionsModal";

export interface User {
  id: number;
  employee_no: string;
  email: string;
  fullnames: string;
  first_name: string;
  last_name: string;
  department: string | null;
  designation: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone_number: string | null;
  gender: string | null;
  created_date: string;
  category: string | null;
}

interface TableUser {
  id: number;
  employee_no: string;
  email: string;
  fullnames: string;
  department: string | null;
  designation: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone: string | null;
  gender: string | null;
  created_date: string;
  category: string | null;
}

export default function UsersTable() {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [searchTerm, setSearchTerm] = useState("");
  // Fetch all users with a high page size to get all records
  const {
    data: usersList,
    isLoading,
    isError,
    refetch,
  } = useGetUsersQuery({
    search: searchTerm,
    page: currentPage,
    page_size: itemsPerPage, // Fetch up to 1000 users
    ordering: "-id", // Order by newest first
  });

  const { toast } = useToast();
  const [deleteUser] = useDeleteUserMutation();

  // Transform users data
  const usersData: TableUser[] = Array.isArray(usersList?.data?.results)
    ? usersList?.data?.results.map((user: User) => ({
        id: user.id,
        employee_no: user.employee_no || "N/A",
        email: user.email || "N/A",
        fullnames: user.fullnames || "N/A",
        department: user.department || "N/A",
        designation: user.designation || "N/A",
        status: user.status || "Active", // Default to Active if null
        team: user.team || "N/A",
        region: user.region || "N/A",
        manager: user.manager || "N/A",
        phone: user.phone_number || "N/A",
        gender: user.gender || "N/A",
        created_date: user.created_date || "N/A",
        category: user.category || "N/A",
      }))
    : [];

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!searchTerm) return usersData;

    return usersData.filter(
      (user) => {
        const fullName = user.fullnames || (user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : '');
        return (
          fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (user.employee_no || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (user.username || '').toLowerCase().includes(searchTerm.toLowerCase())
        );
      }
    );
  }, [usersData, searchTerm]);

  const getAvatarUrl = (name: string) => {
    const initials = name
      .split(" ")
      .map((n) => n[0])
      .join("");
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(
      initials
    )}&background=random`;
  };

  // Modal states
  const [viewUser, setViewUser] = useState<TableUser | null>(null);
  const [editUser, setEditUser] = useState<TableUser | null>(null);
  const [userPermissionsData, setUserPermissionsData] =
    useState<TableUser | null>(null);

  // Status badge renderer
  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase();
    if (statusLower === "active") {
      return (
        <Badge
          variant="primary"
          className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
        >
          Active
        </Badge>
      );
    } else if (statusLower === "inactive") {
      return (
        <Badge
          variant="destructive"
          className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
        >
          Inactive
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="outline"
          className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
        >
          {status}
        </Badge>
      );
    }
  };

  // Handle delete user
  const handleDeleteUser = async (user: TableUser) => {
    try {
      await deleteUser(user.id).unwrap();
      toast({
        title: "User deleted",
        description: `${user.fullnames} has been deleted successfully.`,
      });
      refetch(); // Refresh the users list
      setUserPermissionsData(null);
    } catch (error: any) {
      toast({
        title: "Delete failed",
        description: error?.data?.message || "Could not delete user.",
        variant: "destructive",
      });
    }
  };

  const columns: ColumnDef<TableUser>[] = [
    {
      accessorKey: "id",
      header: "ID",
      cell: ({ row }) => <span>{row.getValue("id")}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "employee_no",
      header: "Employee No",
      cell: ({ row }) => <span>{row.getValue("employee_no")}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => <span>{row.getValue("email")}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "fullnames",
      header: "Full Names",
      cell: ({ row }) => {
        const user = row.original;
        const name = user.fullnames || (user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : 'Unknown User');
        return (
          <div className="flex items-center space-x-2">
            <span>{name}</span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "employee_no",
      header: "Employee No",
      cell: ({ row }) => <span>{row.getValue("employee_no") || 'N/A'}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => <span>Role ID: {row.getValue("role") || 'N/A'}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => <span>{row.getValue("email") || 'N/A'}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "branch",
      header: "Branch",
      cell: ({ row }) => <span>Branch ID: {row.getValue("branch") || 'N/A'}</span>,
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_date",
      header: "Created Date",
      cell: ({ row }) => (
        <span>
          {new Date(row.getValue("created_date")).toLocaleDateString()}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => <span>{row.getValue("category")}</span>,
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div className="flex items-center gap-1">
            <TooltipTemp content={<p>View user details</p>} position="top">
              <OutlinedButton
                variant="ghost"
                size="sm"
                onClick={() => setViewUser(user)}
                className="h-8 w-8 p-0 bg-blue-100 dark:hover:bg-blue-900/50"
              >
                <Eye className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="sr-only">View</span>
              </OutlinedButton>
            </TooltipTemp>

            <TooltipTemp content={<p>Edit user</p>} position="top">
              <OutlinedButton
                variant="ghost"
                size="sm"
                onClick={() => setEditUser(user)}
                className="h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/50"
              >
                <Edit className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="sr-only">Edit</span>
              </OutlinedButton>
            </TooltipTemp>

            <TooltipTemp content={<p>User Permissions</p>} position="top">
              <OutlinedButton
                variant="ghost"
                size="sm"
                onClick={() => setUserPermissionsData(user)}
                className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/50"
              >
                <KeyRoundIcon className="h-4 w-4 text-red-600 dark:text-red-400" />
                <span className="sr-only">Permissions</span>
              </OutlinedButton>
            </TooltipTemp>
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Stats Card */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card6
            title="Total Users"
            value={usersList?.data?.total_data || 0}
            icon={Users}
            iconBg="bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30"
            iconColor="text-blue-600 dark:text-blue-400"
            change={`+${
              usersData.filter((u) => u.status?.toLowerCase() === "active")
                .length
            }`}
            changeLabel="Active Users"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20"
          />
          <Card6
            title="Active Users"
            value={
              usersData.filter((u) => u.status?.toLowerCase() === "active")
                .length
            }
            icon={Users}
            iconBg="bg-gradient-to-r from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/30"
            iconColor="text-green-600 dark:text-green-400"
            change={`${Math.round(
              (usersData.filter((u) => u.status?.toLowerCase() === "active")
                .length /
                usersData.length) *
                100
            )}%`}
            changeLabel="of total users"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-green-50 dark:from-gray-800 dark:to-green-900/20"
          />
          <Card6
            title="Departments"
            value={
              new Set(
                usersData.map((u) => u.department).filter((d) => d !== "N/A")
              ).size
            }
            icon={Users}
            iconBg="bg-gradient-to-r from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30"
            iconColor="text-purple-600 dark:text-purple-400"
            change={`${usersData.filter((u) => u.department !== "N/A").length}`}
            changeLabel="users assigned"
            positive={true}
            cardBg="bg-gradient-to-r from-white to-purple-50 dark:from-gray-800 dark:to-purple-900/20"
          />
        </div>

        {/* Main Table Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Users Management
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Manage and view all system users
                </p>
              </div>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-64 flex h-10 rounded-md border border-gray bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600 dark:text-gray-400">
                    Loading users...
                  </p>
                </div>
              </div>
            ) : isError ? (
              <div className="text-center py-12">
                <div className="text-red-500 text-lg font-medium">
                  Failed to load users
                </div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  Please try again later.
                </p>
                <OutlinedButton onClick={() => refetch()} className="mt-4">
                  Retry
                </OutlinedButton>
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-500 text-lg font-medium">
                  {searchTerm
                    ? "No users found matching your search"
                    : "No users found"}
                </div>
                <p className="text-gray-400 mt-2">
                  {searchTerm
                    ? "Try adjusting your search terms"
                    : "Users will appear here once they are added to the system"}
                </p>
              </div>
            ) : (
              <DataTable<TableUser>
                data={filteredUsers}
                columns={columns}
                enableToolbar={true}
                enableExportToExcel
                enablePrintPdf
                enablePagination={true}
                enableColumnFilters
                enableSorting
                enableSelectColumn
                title="Users List"
                tHeadClassName="bg-secondary"
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={usersList?.data?.total_data || 0}
              />
            )}
          </div>
        </div>

        {/* Modals */}
        <ViewUserModal
          isOpen={!!viewUser}
          onOpenChange={() => setViewUser(null)}
          user={viewUser}
        />
        <EditUserModal
          isOpen={!!editUser}
          onOpenChange={() => setEditUser(null)}
          user={editUser}
          onSave={(updated) => {
            refetch(); // Refresh the users list
            setEditUser(null);
          }}
        />
        {userPermissionsData && (
          <UserPermissionsModal
            isOpen={!!userPermissionsData}
            onClose={() => setUserPermissionsData(null)}
            user={userPermissionsData}
          />
        )}
      </div>
    </Screen>
  );
}
