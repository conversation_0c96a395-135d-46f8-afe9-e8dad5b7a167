import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Loader2, CloudCog } from "lucide-react";
import {
  useCreateStoreRequisitionMutation,
  useGetProcurementCostCentersQuery,
  useGetProcurementStoresQuery,
  useGetProcurementProductsQuery,
  useGetProcurementUnitsOfMeasureQuery,
  useGetProcurementUsersQuery,
} from "@/redux/slices/procurement";
import { useA<PERSON><PERSON>ook } from "@/utils/useAuthHook";
import { StoreRequisitionFormData } from "@/types/procurement";
import { toast } from "@/components/custom/Toast/MyToast";

interface AddStoreRequisitionProps {
  isOpen: boolean;
  onClose: () => void;
}

const AddStoreRequisition = ({ isOpen, onClose }: AddStoreRequisitionProps) => {
  const [createStoreRequisition, { isLoading: creating }] = useCreateStoreRequisitionMutation();
  const { user_details } = useAuthHook();

  // Fetch supporting data
  const { data: costCenters } = useGetProcurementCostCentersQuery({});
  const { data: stores } = useGetProcurementStoresQuery({});
  const { data: products } = useGetProcurementProductsQuery({});
  const { data: unitsOfMeasure } = useGetProcurementUnitsOfMeasureQuery({});
  const { data: users } = useGetProcurementUsersQuery({});

  // Debug logging
  console.log("DETAILED DEBUG:");
  console.log("costCenters structure:", costCenters);
  console.log("costCenters?.results:", costCenters?.results);
  console.log("stores structure:", stores);
  console.log("stores?.results:", stores?.results);
  console.log("users structure:", users);
  console.log("users?.results:", users?.results);
  console.log("products structure:", products);
  console.log("products?.results:", products?.results);
  console.log("unitsOfMeasure structure:", unitsOfMeasure);
  console.log("unitsOfMeasure?.results:", unitsOfMeasure?.data?.results);

  const [formData, setFormData] = useState<StoreRequisitionFormData>({
    requested_by: "", // Required field - user making the request
    cost_center: "",
    store: "",
    purpose: "",
    required_by: "",
    status: "Draft", // Default status
    items: [
      {
        product: "",
        quantity: "", // String as per API
        unit_of_measure: "",
        remarks: "",
      },
    ],
  });

  const handleInputChange = (field: keyof StoreRequisitionFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  // Auto-select current user if available
  useEffect(() => {
    if (user_details && users?.results) {
      const currentUser = users.results.find((user: any) =>
        user.email === user_details.email || user.employee_no === user_details.employee_no
      );
      if (currentUser && !formData.requested_by) {
        setFormData(prev => ({ ...prev, requested_by: currentUser.employee_no }));
      }
    }
  }, [user_details, users, formData.requested_by]);

  const addItem = () => {
    setFormData((prev) => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product: "",
          quantity: "",
          unit_of_measure: "",
          remarks: "",
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        items: prev.items.filter((_, i) => i !== index),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation - Check all required fields per API specification
    if (!formData.requested_by || !formData.cost_center || !formData.store || !formData.purpose || !formData.required_by) {
      toast.error("Please fill in all required fields: Requested By, Cost Center, Store, Purpose, and Required By Date");
      return;
    }

    // Validate purpose length (API: maxLength: 255, minLength: 1)
    if (formData.purpose.length < 1 || formData.purpose.length > 255) {
      toast.error("Purpose must be between 1 and 255 characters");
      return;
    }

    if (formData.items.some(item => !item.product || !item.quantity || !item.unit_of_measure)) {
      toast.error("Please complete all item details");
      return;
    }

    try {
      const payload = {
        ...formData,
        items: formData.items.map(item => ({
          ...item,
          quantity: String(item.quantity), // API expects string($decimal)
        })),
      };

      await createStoreRequisition(payload).unwrap();
      toast.success("Store requisition created successfully");
      onClose();
      
      // Reset form
      setFormData({
        requested_by: "",
        cost_center: "",
        store: "",
        purpose: "",
        required_by: "",
        status: "Draft",
        items: [
          {
            product: "",
            quantity: "",
            unit_of_measure: "",
            remarks: "",
          },
        ],
      });
    } catch (error: any) {
      toast.error(error?.data?.message || "Failed to create store requisition");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Store Requisition</DialogTitle>
          <p className="text-sm text-gray-600 mt-2">
            All fields marked with * are required by the API. Please ensure all information is accurate.
          </p>
          {/* Debug info */}
          <div className="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded">
            <strong>Debug Info:</strong>
            Cost Centers: {(costCenters as any)?.data?.results?.length || costCenters?.results?.length || 0},
            Stores: {(stores as any)?.data?.results?.length || stores?.results?.length || 0},
            Users: {users?.results?.length || 0},
            Products: {products?.results?.length || 0}
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="requested_by">Requested By *</Label>
                <Select
                  value={formData.requested_by.toString()}
                  onValueChange={(value) => handleInputChange("requested_by", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select user" />
                  </SelectTrigger>
                  <SelectContent>
                    {users?.results?.map((user: any) => (
                      <SelectItem key={user.id} value={user.employee_no}>
                        {user.first_name} {user.last_name} ({user.employee_no})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost_center">Cost Center *</Label>
                <Select
                  value={formData.cost_center.toString()}
                  onValueChange={(value) => handleInputChange("cost_center", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select cost center" />
                  </SelectTrigger>
                  <SelectContent>
                    {(costCenters?.results || (costCenters as any)?.data?.results)?.map((center: any) => (
                      <SelectItem key={center.id} value={center.code}>
                        {center.name} ({center.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="store">Store *</Label>
                <Select
                  value={formData.store.toString()}
                  onValueChange={(value) => handleInputChange("store", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {(stores?.results || (stores as any)?.data?.results)?.map((store: any) => (
                      <SelectItem key={store.id} value={store.code}>
                        {store.name} - {store.location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="required_by">Required By *</Label>
                <Input
                  id="required_by"
                  type="date"
                  value={formData.required_by}
                  onChange={(e) => handleInputChange("required_by", e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
                <p className="text-xs text-gray-500">Date when items are needed</p>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="purpose">Purpose * (1-255 characters)</Label>
                <Textarea
                  id="purpose"
                  value={formData.purpose}
                  onChange={(e) => handleInputChange("purpose", e.target.value)}
                  placeholder="Enter the purpose of this requisition..."
                  rows={3}
                  maxLength={255}
                  className={formData.purpose.length > 255 ? "border-red-500" : ""}
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Required field</span>
                  <span className={formData.purpose.length > 255 ? "text-red-500" : ""}>
                    {formData.purpose.length}/255 characters
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Items</CardTitle>
              <Button type="button" onClick={addItem} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.items.map((item, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {formData.items.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Product *</Label>
                      <Select
                        value={item.product.toString()}
                        onValueChange={(value) => handleItemChange(index, "product", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {products?.results?.map((product: any) => (
                            <SelectItem key={product.id} value={product.code}>
                              {product.name} ({product.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Quantity *</Label>
                      <Input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, "quantity", e.target.value)}
                        placeholder="Enter quantity"
                        min="0"
                        step="0.01"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Unit of Measure *</Label>
                      <Select
                        value={item.unit_of_measure.toString()}
                        onValueChange={(value) => handleItemChange(index, "unit_of_measure", Number(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {(unitsOfMeasure as any)?.data?.results?.map((unit: any) => (
                            <SelectItem key={unit.id} value={unit.id.toString()}>
                              {unit.name} ({unit.symbol})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Remarks</Label>
                    <Textarea
                      value={item.remarks}
                      onChange={(e) => handleItemChange(index, "remarks", e.target.value)}
                      placeholder="Any additional notes for this item..."
                      rows={2}
                    />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating}>
              {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Requisition
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddStoreRequisition;
