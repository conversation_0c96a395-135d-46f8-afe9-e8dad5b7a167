import { useNavigate } from "react-router-dom";
import Error500Img from '@/assets/500 Internal Server Error-amico.png'

const Error500Page = () => {
  const navigate = useNavigate();
  return (
    <div className="w-full min-h-screen flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md flex flex-col items-center gap-8 py-12">
        
        {/* Heading */}
        <div className="text-center w-full">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Error 500 – Internal Server Error
          </h2>
          <p className="text-gray-500 text-sm mb-6">
            Something went wrong on our end. It's not you, it's us.
          </p>
        </div>
        {/* Image */}
        <div className="flex justify-center">
          <img src={Error500Img} alt="Error 500" className="h-80 w-full max-w-[420px] object-contain mx-auto" />
        </div>
        {/* Button */}
        <button
          onClick={() => window.location.reload()}
          className="bg-black text-white px-6 py-2 rounded-md font-semibold hover:bg-gray-900 transition-colors text-sm"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
};

export default Error500Page;
